.container {
  min-height: 100vh;
  background-color: #f5f5f5;
  display: flex;
  flex-direction: column;
}

/* 标签栏样式 */
.tabs-container {
  background: #fff;
  position: sticky;
  top: 0;
  z-index: 100;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  width: 100%;
  box-sizing: border-box;
}

.tabs-scroll {
  white-space: nowrap;
  width: 100%;
}

.tabs {
  display: flex;
  padding: 10rpx 20rpx;
  box-sizing: border-box;
  width: 100%;
}

.tab {
  position: relative;
  padding: 0 20rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 10rpx;
  flex-shrink: 0;
}

.tab text {
  font-size: 28rpx;
  color: #666;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.tab.active text {
  color: #1890ff;
  font-weight: 500;
}

.tab-line {
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 30rpx;
  height: 4rpx;
  background: #1890ff;
  border-radius: 2rpx;
  opacity: 0;
  transition: all 0.3s ease;
}

.tab.active .tab-line {
  opacity: 1;
}

/* 通知列表样式 */
.notice-scroll {
  flex: 1;
  height: calc(100vh - 100rpx);
}

.notice-list {
  padding: 20rpx;
}

.notice-item {
  background: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.notice-item.unread {
  background: #f0f7ff;
}

.notice-item.unread::before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 6rpx;
  background: #1890ff;
  border-radius: 3rpx;
}

.notice-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.notice-type-tag {
  background: #e6f7ff;
  color: #1890ff;
  padding: 4rpx 16rpx;
  border-radius: 6rpx;
  font-size: 24rpx;
}

.notice-time {
  font-size: 24rpx;
  color: #999;
}

.notice-title {
  font-size: 32rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 12rpx;
}

.notice-content {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
  margin-bottom: 20rpx;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
}

.notice-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.notice-dot {
  width: 16rpx;
  height: 16rpx;
  background: #f56c6c;
  border-radius: 50%;
}

.notice-action {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.action-text {
  font-size: 26rpx;
  color: #1890ff;
}

.action-icon {
  width: 24rpx;
  height: 24rpx;
  opacity: 0.6;
}

/* 加载状态样式 */
.loading-container {
  padding: 30rpx 0;
  text-align: center;
}

.loading {
  display: inline-flex;
  align-items: center;
  gap: 12rpx;
}

.loading-spinner {
  width: 32rpx;
  height: 32rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #1890ff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading text {
  font-size: 26rpx;
  color: #999;
}

/* 无更多数据样式 */
.no-more {
  padding: 30rpx 0;
}

.divider {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 20rpx;
}

.divider-line {
  width: 100rpx;
  height: 1rpx;
  background: #eee;
}

.no-more text {
  font-size: 26rpx;
  color: #999;
}

/* 空状态样式 */
.empty-state {
  padding: 120rpx 0;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.empty-icon {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 20rpx;
  opacity: 0.7;
}

.empty-text {
  font-size: 30rpx;
  color: #999;
  margin-bottom: 30rpx;
}

.empty-action {
  background: #f0f7ff;
  color: #1890ff;
  padding: 12rpx 30rpx;
  border-radius: 30rpx;
  font-size: 28rpx;
}

/* 下拉刷新样式 */
.wx-refresh-container {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #999;
  font-size: 26rpx;
} 