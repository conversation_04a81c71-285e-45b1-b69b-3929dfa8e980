# 小程序登录检查实现说明

## 实现目标
将小程序改为强制登录模式，即用户打开小程序后必须先登录才能使用任何功能。

## 修改内容

### 1. 首页登录检查 (pages/home/<USER>
- **onLoad 方法**：在页面加载时检查登录状态，如果未登录则直接跳转到登录页面
- **onShow 方法**：在页面显示时也检查登录状态，确保用户始终处于登录状态
- **检查条件**：验证 `member`、`member_auth`、`member_uid` 三个存储项是否存在且有效

### 2. 登录页面跳转修改 (pages/login/login.js)
- 修改所有登录成功后的跳转目标，从 `../member/index` 改为 `../home/<USER>
- 涉及的登录方式：
  - 账号密码登录
  - 微信授权登录
  - 手机号登录

### 3. 工单列表页面 (pages/workorder/list.js)
- **onLoad 方法**：添加登录检查，未登录时跳转到登录页面

### 4. 工单发布页面 (pages/workorder/post.js)
- **checkAndHandleLoginStatus 方法**：修改登录检查逻辑，未登录时直接跳转而不是显示弹窗

### 5. 财务页面 (pages/caiwu/index.js)
- **checkAdminPermission 方法**：添加登录检查，未登录时直接跳转到登录页面

### 6. 会员页面 (pages/member/index.js)
- **onLoad 和 onShow 方法**：改进登录检查逻辑，确保登录状态验证更严格

### 7. 测试功能
- 在首页添加了"退出登录"按钮，方便测试登录检查功能
- 添加了相应的样式文件

## 登录检查逻辑

所有页面的登录检查都遵循以下逻辑：

```javascript
// 获取登录相关的存储数据
const member = wx.getStorageSync('member');
const member_auth = wx.getStorageSync('member_auth');
const member_uid = wx.getStorageSync('member_uid');

// 检查是否满足登录条件
if (!member || !member_auth || !member_uid || !member.id) {
    // 未登录，跳转到登录页面
    wx.reLaunch({
        url: '/pages/login/login'
    });
    return;
}

// 已登录，继续执行页面逻辑
```

## 使用方法

1. **正常使用**：用户打开小程序后会自动跳转到登录页面，登录成功后进入首页
2. **测试退出**：在首页底部有"退出登录（测试）"按钮，点击可以清除登录状态并跳转到登录页面
3. **页面切换**：在任何页面，如果检测到用户未登录，都会自动跳转到登录页面

## 注意事项

1. 使用 `wx.reLaunch()` 而不是 `wx.navigateTo()` 来跳转到登录页面，这样可以清空页面栈，防止用户通过返回按钮绕过登录
2. 登录检查在 `onLoad` 和 `onShow` 两个生命周期中都进行，确保覆盖所有场景
3. 测试用的退出登录按钮可以在正式发布时移除

## 测试建议

1. 清除小程序存储数据，重新打开小程序，验证是否跳转到登录页面
2. 登录后切换到各个页面，验证功能正常
3. 使用退出登录按钮测试，验证退出后是否正确跳转到登录页面
4. 测试各种登录方式（账号密码、微信授权、手机号）是否都能正确跳转到首页
