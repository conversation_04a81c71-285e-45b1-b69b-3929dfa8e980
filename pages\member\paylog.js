var app = getApp();
var http_url = app.globalData.http_api + "s=member&app=pay&c=paylog&m=index";
http_url += '&api_call_function=member_paylog_list';

Page({

  /**
   * 页面的初始数据
   */
  data: {
    listData: [],
    hidden: true,
    page: 1,
    hasMore: "false",
    isLoading: false,
    currentTab: 0  // 当前选中的 tab
  },

  onLoad: function(options) {
    this.checkLoginAndLoadData();
  },

  // 切换 tab
  switchTab: function(e) {
    const tab = e.currentTarget.dataset.tab;
    if (this.data.currentTab === tab) return;
    
    this.setData({
      currentTab: tab,
      page: 1,
      listData: [],
      hasMore: "false"
    });
    
    this.loadData(1);
  },

  // 检查登录状态并加载数据
  checkLoginAndLoadData: function() {
    var member = wx.getStorageSync('member');
    if (!member) {
      wx.reLaunch({ 
        url: "../login/login",
        success: () => {
          wx.showToast({
            title: '请先登录',
            icon: 'none'
          });
        }
      });
      return;
    }

    this.loadData(1);
  },

  // 加载数据
  loadData: function(page) {
    if (this.data.isLoading) return;
    
    this.setData({ isLoading: true, hidden: false });
    
    var member_uid = wx.getStorageSync('member_uid');
    var member_auth = wx.getStorageSync('member_auth');
    
    if (!member_uid || !member_auth) {
      this.setData({ isLoading: false, hidden: true });
      wx.reLaunch({ 
        url: "../login/login",
        success: () => {
          wx.showToast({
            title: '登录信息已过期',
            icon: 'none'
          });
        }
      });
      return;
    }

    var requestUrl = http_url + 
      '&api_auth_uid=' + member_uid + 
      '&api_auth_code=' + member_auth +
      '&tid=' + this.data.currentTab +
      '&total=0&order=&field=id';

    wx.request({
      url: requestUrl,
      method: 'GET',
      data: { page: page },
      success: (res) => {
        if (res.data.code == 1) {
          if (page == 1) {
            this.setData({
              listData: res.data.data,
              page: 1,
              hasMore: "false"
            });
          } else {
            if (res.data.data.length == 0) {
              this.setData({
                hasMore: "true"
              });
              setTimeout(() => {
                this.setData({
                  hasMore: "false",
                  hidden: true
                });
              }, 900);
            } else {
              this.setData({
                listData: [...this.data.listData, ...res.data.data],
                page: page
              });
            }
          }
        } else {
          wx.showToast({
            title: res.data.msg || '加载失败',
            icon: 'none',
            duration: 2000
          });
        }
      },
      fail: (err) => {
        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none',
          duration: 2000
        });
      },
      complete: () => {
        this.setData({ 
          isLoading: false,
          hidden: true
        });
      }
    });
  },

  onReachBottom: function() {
    if (this.data.hasMore === "true" || this.data.isLoading) return;
    this.loadData(this.data.page + 1);
  },

  // 下拉刷新
  onPullDownRefresh: function() {
    this.checkLoginAndLoadData();
    wx.stopPullDownRefresh();
  },

  // 跳转到详情页
  goToDetail: function(e) {
    const id = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: './paylog_show?id=' + id
    });
  }
});