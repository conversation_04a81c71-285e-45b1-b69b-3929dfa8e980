.container {
  padding: 0 0 40rpx 0;
  min-height: 100vh;
  background-color: #f8f9fa;
  position: relative;
}

.top-bg {
  height: 220rpx;
  background: linear-gradient(45deg, #3a8ef0, #2c6dd5);
  width: 100%;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1;
}

.page-header {
  position: relative;
  z-index: 2;
  padding: 30rpx 30rpx 20rpx;
}

.page-title {
  color: #ffffff;
  font-size: 38rpx;
  font-weight: bold;
}

/* 统计卡片样式 */
.stats-card {
  position: relative;
  z-index: 2;
  margin: 0 30rpx 30rpx;
  background: #ffffff;
  border-radius: 15rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
  padding: 30rpx 20rpx;
}

.stats-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
  margin-bottom: 20rpx;
}

.stats-double {
  display: flex;
  width: 100%;
  justify-content: space-between;
}

.stats-block {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.stats-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 8rpx;
}

.special-count {
  color: #2ECC71; /* 特殊人群使用绿色 */
}

.stats-info {
  display: flex;
  flex-direction: column;
}

.stats-subtitle {
  font-size: 26rpx;
  color: #666;
  margin-top: 10rpx;
  background-color: #f0f7ff;
  padding: 6rpx 12rpx;
  border-radius: 6rpx;
  display: inline-block;
  border: 1rpx solid #e0eeff;
}

.subtitle-label {
  color: #999;
  margin-right: 6rpx;
}

.subtitle-icon {
  font-family: "iconfont" !important;
  font-size: 28rpx;
  color: #3a8ef0;
  margin-right: 4rpx;
}

.subtitle-unit {
  font-size: 24rpx;
  color: #999;
  margin-left: 2rpx;
}

.total-count {
  font-size: 48rpx;
  font-weight: bold;
  color: #3a8ef0;
  display: flex;
  align-items: flex-end;
}

.count-unit {
  font-size: 24rpx;
  color: #999;
  margin-left: 5rpx;
  margin-bottom: 8rpx;
}

/* 加载提示样式 */
.loading-tip {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx 0;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #3a8ef0;
  border-radius: 50%;
  margin-bottom: 15rpx;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-tip text {
  font-size: 26rpx;
  color: #999;
}

/* 统计列表样式 */
.stats-list {
  padding: 10rpx 0;
}

.stats-item {
  padding: 20rpx 10rpx;
  border-bottom: 1rpx solid #f5f5f5;
}

.stats-item:last-child {
  border-bottom: none;
}

.stats-item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15rpx;
}

.item-name-wrap {
  display: flex;
  align-items: center;
}

.item-dot {
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  margin-right: 10rpx;
}

.item-name {
  font-size: 28rpx;
  color: #333;
}

.item-count-card {
  display: flex;
  flex-direction: column;
  min-width: 100rpx;
  overflow: hidden;
  border-radius: 10rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}

.card-top {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 12rpx 20rpx;
}

.card-bottom {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f5f5f5;
  padding: 6rpx 0;
  border-top: 1rpx solid rgba(0,0,0,0.05);
}

.item-count {
  font-size: 32rpx;
  font-weight: bold;
  color: #ffffff;
}

.item-percent {
  font-size: 22rpx;
  color: #666;
}

/* 进度条样式 */
.progress-bar-wrap {
  position: relative;
  height: 30rpx;
  margin-top: 10rpx;
  display: flex;
  align-items: center;
}

.progress-bar-bg {
  position: absolute;
  left: 0;
  top: 0;
  height: 10rpx;
  width: 100%;
  background-color: #f5f5f5;
  border-radius: 5rpx;
}

.progress-bar {
  position: absolute;
  left: 0;
  top: 0;
  height: 10rpx;
  border-radius: 5rpx;
  transition: width 0.3s ease;
  min-width: 10rpx;
}

.progress-text {
  position: absolute;
  right: 0;
  top: -5rpx;
  font-size: 22rpx;
  color: #666;
}

/* 空状态样式 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx 0;
}

.empty-icon {
  width: 80rpx;
  height: 80rpx;
  margin-bottom: 15rpx;
  opacity: 0.5;
}

.empty-text {
  color: #999;
  font-size: 26rpx;
}

/* 统计说明样式 */
.stats-note {
  margin: 0 30rpx;
  background: #ffffff;
  border-radius: 15rpx;
  padding: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.note-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 15rpx;
  padding-left: 15rpx;
  position: relative;
}

.note-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 6rpx;
  bottom: 6rpx;
  width: 6rpx;
  background-color: #3a8ef0;
  border-radius: 3rpx;
}

.note-content {
  display: flex;
  flex-direction: column;
  padding: 0 10rpx;
}

.note-content text {
  font-size: 24rpx;
  color: #666;
  line-height: 1.7;
  margin-bottom: 8rpx;
} 