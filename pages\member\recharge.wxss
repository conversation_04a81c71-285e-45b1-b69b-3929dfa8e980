@import "../member/account.wxss";

.container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 30rpx;
  box-sizing: border-box;
}

.recharge-card {
  background: #fff;
  border-radius: 20rpx;
  padding: 40rpx 30rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
}

/* 卡片头部 */
.card-header {
  text-align: center;
  margin-bottom: 40rpx;
}

.card-title {
  font-size: 36rpx;
  color: #333;
  font-weight: 600;
  display: block;
  margin-bottom: 12rpx;
}

.card-subtitle {
  font-size: 28rpx;
  color: #999;
}

/* 快捷金额选择 */
.amount-options {
  margin-bottom: 40rpx;
}

.amount-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
}

.amount-item {
  background: #f8f9fa;
  border-radius: 12rpx;
  padding: 30rpx;
  text-align: center;
  position: relative;
  transition: all 0.3s ease;
}

.amount-item.active {
  background: #e6f7ff;
  border: 2rpx solid #1890ff;
}

.amount-value {
  font-size: 40rpx;
  color: #333;
  font-weight: 500;
  display: block;
  margin-bottom: 8rpx;
}

.amount-unit {
  font-size: 24rpx;
  color: #666;
}

/* 自定义金额输入 */
.custom-amount {
  margin-bottom: 40rpx;
}

.input-label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 16rpx;
}

.input-wrap {
  display: flex;
  align-items: center;
  background: #f8f9fa;
  border-radius: 12rpx;
  padding: 0 24rpx;
  height: 96rpx;
}

.currency-symbol {
  font-size: 36rpx;
  color: #333;
  margin-right: 16rpx;
}

.amount-input {
  flex: 1;
  height: 100%;
  font-size: 32rpx;
  color: #333;
}

.input-placeholder {
  color: #999;
  font-size: 28rpx;
}

/* 支付方式选择 */
.payment-method {
  margin-bottom: 40rpx;
}

.method-header {
  margin-bottom: 20rpx;
}

.method-title {
  font-size: 28rpx;
  color: #333;
}

.method-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.method-info {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.method-icon {
  width: 48rpx;
  height: 48rpx;
}

.method-name {
  font-size: 28rpx;
  color: #333;
}

.method-check {
  width: 40rpx;
  height: 40rpx;
}

.check-icon {
  width: 100%;
  height: 100%;
  opacity: 0.8;
}

/* 充值按钮 */
.recharge-btn {
  width: 100%;
  height: 96rpx;
  background: linear-gradient(135deg, #1890ff, #096dd9);
  color: white;
  font-size: 32rpx;
  font-weight: 500;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 30rpx;
  box-shadow: 0 6rpx 16rpx rgba(24, 144, 255, 0.25);
  transition: all 0.3s ease;
}

.recharge-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 12rpx rgba(24, 144, 255, 0.2);
  opacity: 0.9;
}

.btn-text {
  margin-right: 12rpx;
}

.btn-amount {
  font-size: 28rpx;
  opacity: 0.9;
}

/* 充值说明 */
.recharge-tips {
  padding-top: 30rpx;
  border-top: 1rpx solid #f0f0f0;
}

.tips-item {
  display: flex;
  align-items: center;
  gap: 12rpx;
  margin-bottom: 16rpx;
}

.tips-item:last-child {
  margin-bottom: 0;
}

.tips-icon {
  width: 28rpx;
  height: 28rpx;
  opacity: 0.6;
}

.tips-text {
  font-size: 24rpx;
  color: #999;
}