<view class="home-container">
  <!-- 顶部蓝色区域 -->
  <view class="header-section">
    <view class="header-content">
      <text class="welcome-text">欢迎使用</text>
      <text class="app-title">村务客户端</text>
    </view>
  </view>

  <!-- 搜索区域 -->
  <view class="search-container">
    <view class="search-wrapper">
      <view class="search-box">
        <view class="search-input-wrap">
          <image src="../../icons/search.png" class="search-icon"></image>
          <input class="search-input"
                 placeholder="输入姓名或身份证号搜索"
                 value="{{searchKeyword}}"
                 bindinput="onSearchInput"
                 bindconfirm="onSearchConfirm"
                 confirm-type="search" />
          <view class="clear-icon" bindtap="clearSearch" wx:if="{{searchKeyword}}">
            <image src="../../icons/clear.png"></image>
          </view>
        </view>
        <view class="search-btn" bindtap="onSearchConfirm">搜索</view>
      </view>

      <!-- 快捷标签 -->
      <view class="quick-tags">
        <!-- 基础分类 -->
        <view class="tag-item {{activeTag === '全部' ? 'active' : ''}}" bindtap="onTagTap" data-tag="全部">全部</view>
        <view class="tag-item {{activeTag === '党员' ? 'active' : ''}}" bindtap="onTagTap" data-tag="党员">党员</view>

        <!-- 社会保障类 -->
        <view class="tag-item {{activeTag === '低保' ? 'active' : ''}}" bindtap="onTagTap" data-tag="低保">低保</view>
        <view class="tag-item {{activeTag === '特困' ? 'active' : ''}}" bindtap="onTagTap" data-tag="特困">特困</view>
        <view class="tag-item {{activeTag === '五保户' ? 'active' : ''}}" bindtap="onTagTap" data-tag="五保户">五保户</view>

        <!-- 扶贫政策类 -->
        <view class="tag-item {{activeTag === '脱贫' ? 'active' : ''}}" bindtap="onTagTap" data-tag="脱贫">脱贫</view>
        <view class="tag-item {{activeTag === '监测户' ? 'active' : ''}}" bindtap="onTagTap" data-tag="监测户">监测户</view>

        <!-- 特殊关爱类 -->
        <view class="tag-item {{activeTag === '残疾' ? 'active' : ''}}" bindtap="onTagTap" data-tag="残疾">残疾</view>
        <view class="tag-item {{activeTag === '孤儿' ? 'active' : ''}}" bindtap="onTagTap" data-tag="孤儿">孤儿</view>

        <!-- 政策优待类 -->
        <view class="tag-item {{activeTag === '兵役' ? 'active' : ''}}" bindtap="onTagTap" data-tag="兵役">兵役</view>
        <view class="tag-item {{activeTag === '优抚' ? 'active' : ''}}" bindtap="onTagTap" data-tag="优抚">优抚</view>
      </view>


    </view>
  </view>

  <!-- 搜索结果提示 -->
  <view class="search-result-tip" wx:if="{{isSearching}}">
    <text wx:if="{{activeTag !== '全部' && searchKeyword === activeTag}}">
      分类: "{{activeTag}}" 搜索结果 ({{totalSearchCount || searchResults.length}})
    </text>
    <text wx:else>
      关键词: "{{searchKeyword}}" 搜索结果 ({{totalSearchCount || searchResults.length}})
    </text>
    <view class="back-to-all" bindtap="clearSearch">返回全部</view>
  </view>

  <!-- 最近更新 -->
  <view class="approval-section">
    <view class="section-title">{{isSearching ? '搜索结果' : '最近更新'}}</view>
    <!-- 空状态 -->
    <view class="empty-state" wx:if="{{isSearching && searchResults.length === 0}}">
      <image src="../../icons/empty.png" class="empty-icon"></image>
      <text class="empty-text">未找到匹配的村民档案</text>
      <text class="empty-tip">请尝试其他关键词或检查拼写</text>
    </view>

    <!-- 加载状态 -->
    <view class="loading-state" wx:if="{{!isSearching && recentLoading}}">
      <text class="loading-text">加载中...</text>
    </view>

    <!-- 最近更新为空状态 -->
    <view class="empty-state" wx:if="{{!isSearching && !recentLoading && recentUpdates.length === 0}}">
      <image src="../../icons/empty.png" class="empty-icon"></image>
      <text class="empty-text">暂无最近更新数据</text>
    </view>

    <view class="approval-list" wx:else>
      <view class="approval-item" wx:for="{{isSearching ? searchResults : recentUpdates}}" wx:key="id" bindtap="onApprovalTap" data-item="{{item}}">
        <view class="approval-avatar" style="background-color: {{item.color}}">
          <text class="avatar-text">{{item.name.charAt(0)}}</text>
        </view>
        <view class="approval-info">
          <view class="approval-name">{{item.name}}</view>
          <view class="approval-type">{{item.type}}</view>
          <view class="approval-tags">
            <text class="tag pending">待审批</text>
            <text class="tag urgent" wx:if="{{item.urgent}}">紧急</text>
            <text class="tag time">{{item.time}}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 加载更多按钮 -->
    <view class="load-more-container" wx:if="{{isSearching}}">
      <view class="load-more-btn" wx:if="{{searchHasMore && !searchLoading}}" bindtap="loadMoreSearchResults">
        <text>加载更多</text>
      </view>
      <view class="loading-tip" wx:if="{{searchLoading}}">
        <text>加载中...</text>
      </view>
      <view class="no-more-tip" wx:if="{{!searchHasMore && searchResults.length > 0}}">
        <text>已加载全部 ({{totalSearchCount || searchResults.length}}条)</text>
      </view>
    </view>


  </view>

  <!-- 功能卡片区域 -->
  <view class="function-cards">
    <view class="card-row">
      <view class="function-card red" bindtap="onCardTap" data-type="labor">
        <view class="card-icon">👷</view>
        <view class="card-title">劳务派遣</view>
        <view class="card-subtitle">方便灵活用工</view>
      </view>
      <view class="function-card orange" bindtap="onCardTap" data-type="consult">
        <view class="card-icon">👥</view>
        <view class="card-title">管理咨询</view>
        <view class="card-subtitle">助力企业成长</view>
      </view>
    </view>
  </view>

  <!-- 新闻资讯 -->
  <view class="news-section">
    <view class="section-title">新闻公司动态资讯</view>
    <view class="news-list">
      <view class="news-item" wx:for="{{newsList}}" wx:key="id" bindtap="onNewsTap" data-item="{{item}}">
        <view class="news-content">
          <view class="news-title">{{item.title}}</view>
          <view class="news-meta">
            <text class="news-author">{{item.author}}</text>
            <text class="news-time">{{item.time}}</text>
            <text class="news-source">{{item.source}}</text>
          </view>
        </view>
        <image class="news-image" src="{{item.image}}" mode="aspectFill"></image>
      </view>
    </view>
  </view>
</view>