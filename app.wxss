@import "/wxParse/wxParse.wxss";
/**app.wxss**/
.news-time{
    float: left;
      font-size:22rpx;
      color: #a9a9a9;
      padding-left: 0rpx;
}
.pos{
  float: left;
    width: 100%;
    height: 35rpx;
}
.fr{
    float: right;
    overflow: hidden;
   
}

.video-see,.news-see{
    float: left;
     font-size:22rpx;
      color: #a9a9a9; 
       margin-right: 15rpx;
}
.news-ups{
    float: left;
     font-size:22rpx;
      color: #a9a9a9; 
      margin-right: 15rpx;
    
}
view.news-see image{
    width:40rpx;
    height: 30rpx; 
    
}
view.video-see image{
    width:30rpx;
    height: 30rpx;
     
}
.news-pl{
     float: left;
      font-size:22rpx;
      color: #a9a9a9; 
      
}
view.news-pl image{
    width:30rpx;
    height: 30rpx; 
      
}
view.news-ups image{
    width:30rpx;
    height: 30rpx; 
}


.black{
    color: #000;
}
.gray{
    color: #999;
}
.blue{
    color: #4da6d2;
}
.hidden{
    text-align: center
}
.hidden image{
    width: 32rpx;
    height: 32rpx;
    
}
