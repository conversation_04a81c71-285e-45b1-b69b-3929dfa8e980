/* 基础样式 */
@import "../login/login.wxss";

/* 变量定义 */
page {
  --primary-color: #07c160;
  --text-color: #333;
  --text-color-secondary: #666;
  --text-color-light: #999;
  --background-color: #f8f8f8;
  --border-color: #ddd;
  --border-radius: 8rpx;
  --spacing: 20rpx;
  --shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
}

/* 页面容器 */
.post-container {
  min-height: 100vh;
  background-color: #f7f7f7;
  padding-bottom: 40rpx;
}

/* 顶部背景 */
.top-bg {
  height: 120rpx;
  background: #1677ff;
  padding: 0 24rpx;
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  padding-bottom: 20rpx;
}

.page-title {
  color: #fff;
  padding-left: 4rpx;
}

.title-text {
  font-size: 28rpx;
  font-weight: normal;
  display: block;
  margin-bottom: 2rpx;
}

.subtitle-text {
  font-size: 20rpx;
  opacity: 0.75;
}

/* 主卡片 */
.post-card {
  margin: 10rpx 24rpx 0;
  background: #fff;
  border-radius: 12rpx;
  padding: 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.06);
}

/* 提示卡片 */
.notice-card {
  background: rgba(250, 140, 22, 0.08);
  border-radius: 6rpx;
  padding: 16rpx 20rpx;
  margin-bottom: 24rpx;
  margin-top: 8rpx;
}

.notice-content {
  display: flex;
  align-items: center;
}

.notice-icon {
  width: 28rpx;
  height: 28rpx;
  margin-right: 10rpx;
}

.notice-text {
  color: #fa8c16;
  font-size: 24rpx;
}

/* 表单区块 */
.form-section {
  margin-bottom: 32rpx;
}

.section-title {
  font-size: 28rpx;
  font-weight: normal;
  color: #333;
  margin-bottom: 16rpx;
}

/* 单选按钮组 */
.radio-group-container {
  width: 100%;
  box-sizing: border-box;
  overflow: hidden;
}

.radio-group {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -6rpx;
  width: calc(100% + 12rpx);
  box-sizing: border-box;
}

.radio-button {
  width: 50%;
  padding: 6rpx;
  box-sizing: border-box;
}

.radio-button-inner {
  background: #f7f7f7;
  border-radius: 6rpx;
  padding: 20rpx 0;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s;
}

.radio-button.active .radio-button-inner {
  background: rgba(7, 193, 96, 0.1);
  color: #07c160;
}

.radio-button .radio {
  transform: scale(0.75);
}

.radio-button text {
  margin-left: 4rpx;
  font-size: 26rpx;
}

/* 输入框 */
.input-container {
  background: #f7f7f7;
  border-radius: 6rpx;
  padding: 4rpx 20rpx;
}

.form-input {
  height: 84rpx;
  font-size: 26rpx;
}

/* 文本域 */
.textarea-container {
  background: #f7f7f7;
  border-radius: 6rpx;
  padding: 20rpx;
}

.form-textarea {
  width: 100%;
  height: 240rpx;
  font-size: 26rpx;
  line-height: 1.6;
}

/* 下拉选择器 */
.select-container {
  background: #f7f7f7;
  border-radius: 6rpx;
  padding: 0 20rpx;
}

.picker-view {
  height: 84rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 26rpx;
  color: #333;
}

.picker-view.placeholder {
  color: #999;
}

.picker-arrow {
  width: 32rpx;
  height: 32rpx;
}

/* 上传区域 */
.upload-section {
  background: #f7f7f7;
  border-radius: 6rpx;
  padding: 20rpx;
}

.upload-btn {
  background: #fff;
  border: 1rpx dashed #ddd;
  border-radius: 6rpx;
  height: 180rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16rpx;
}

.upload-content {
  text-align: center;
}

.upload-icon {
  width: 48rpx;
  height: 48rpx;
  display: block;
  margin: 0 auto 8rpx;
}

.upload-text {
  font-size: 26rpx;
  color: #666;
  display: block;
  margin-bottom: 6rpx;
}

.upload-desc {
  font-size: 22rpx;
  color: #999;
}

/* 提交按钮 */
.submit-section {
  margin-top: 48rpx;
  margin-bottom: 120rpx;
  padding-bottom: 30rpx;
}

.submit-button {
  background: linear-gradient(135deg, #1677ff, #1677fb);
  color: #fff;
  border-radius: 6rpx;
  font-size: 28rpx;
  height: 84rpx;
  line-height: 84rpx;
  text-align: center;
  box-shadow: 0 4rpx 12rpx rgba(7, 193, 96, 0.2);
}

.submit-button[disabled] {
  background: #9be6b4;
  box-shadow: none;
}

/* 加载中状态 */
.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
}

.loading-spinner {
  width: 32rpx;
  height: 32rpx;
  border: 3rpx solid rgba(255, 255, 255, 0.3);
  border-top: 3rpx solid #fff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 8rpx;
}

.loading-text {
  font-size: 26rpx;
  color: #fff;
}

/* 图片预览网格 */
.image-preview-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16rpx;
  margin-top: 16rpx;
}

.preview-item {
  position: relative;
  padding-bottom: 100%;
  border-radius: 6rpx;
  overflow: hidden;
}

.preview-image {
  position: absolute;
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.delete-btn {
  position: absolute;
  top: 6rpx;
  right: 6rpx;
  width: 36rpx;
  height: 36rpx;
  background: rgba(0, 0, 0, 0.4);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.delete-icon {
  color: #fff;
  font-size: 28rpx;
  line-height: 1;
}

/* 动画 */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 底部安全区域 */
.safe-bottom {
  padding-bottom: env(safe-area-inset-bottom);
} 