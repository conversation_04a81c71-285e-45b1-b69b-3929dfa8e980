var app = getApp(); // 获取全局应用实例

Page({
    data: {
        listData: [], // 存储列表数据
        loading: false, // 控制加载状态
        page: 1, // 当前页码
        limit: 10, // 每页数据数量
        hasMore: true, // 是否还有更多数据
        hidden: true, // 控制底部提示的显示状态
        currentTab: 0, // 当前选中的tab索引
        refreshing: false, // 控制下拉刷新状态
        searchKeyword: '', // 搜索关键词
        isSearching: false, // 是否正在搜索
        retryCount: 0, // 重试次数
        showActionMenu: false, // 控制操作菜单显示
        selectedId: null, // 选中的工单ID
        isAdmin: false, // 当前用户是否是管理员
        currentUserId: null, // 当前用户ID
        isLoggedIn: false, // 用户是否已登录
    },

    onLoad: function(options) {
        try {
            // 检查登录状态
            const member = wx.getStorageSync('member');
            const member_auth = wx.getStorageSync('member_auth');
            const member_uid = wx.getStorageSync('member_uid');

            // 如果未登录，跳转到登录页面
            if (!member || !member_auth || !member_uid || !member.id) {
                wx.reLaunch({
                    url: '/pages/login/login'
                });
                return;
            }

            // 用户已登录，设置用户信息
            const isAdmin = member.is_admin && parseInt(member.is_admin) > 0;
            this.setData({
                isAdmin: isAdmin,
                currentUserId: member.id || null,
                isLoggedIn: true
            });

            this.loadData();
        } catch (err) {
            console.error('页面加载错误:', err);
            wx.showToast({
                title: '加载失败，请重试',
                icon: 'none'
            });
        }
    },

    // 页面显示时检查是否需要刷新数据
    onShow: function() {
        // 获取最新的用户信息
        const member = wx.getStorageSync('member');
        if (member && member.id) {
            const isAdmin = member.is_admin && parseInt(member.is_admin) > 0;
            this.setData({
                isAdmin: isAdmin,
                currentUserId: member.id || null,
                isLoggedIn: true
            });
        } else {
            this.setData({
                isAdmin: false,
                currentUserId: null,
                isLoggedIn: false
            });
        }
        
        // 检查是否有新发布的工单需要刷新列表
        if (app.globalData.needRefreshWorkorderList) {
            // 重置页码，清空列表，重新加载第一页数据
            this.setData({
                page: 1,
                listData: [],
                hasMore: true,
                hidden: true
            });
            this.loadData(this.data.isSearching);
            
            // 重置刷新标记
            app.globalData.needRefreshWorkorderList = false;
        }
        
        // 设置当前选中的tabbar项为工单
        if (typeof this.getTabBar === 'function' && this.getTabBar()) {
            this.getTabBar().setData({
                selected: 1
            });
        }
    },

    // 切换tab
    switchTab: function(e) {
        try {
            const index = e.currentTarget.dataset.index;
            this.setData({
                currentTab: index,
                page: 1,
                listData: []
            });
            this.loadData(this.data.isSearching);
        } catch (err) {
            console.error('切换标签错误:', err);
            wx.showToast({
                title: '操作失败，请重试',
                icon: 'none'
            });
        }
    },

    // 下拉加载更多
    loadMore: function() {
        if (this.data.hasMore && !this.data.loading) {
            this.loadData(this.data.isSearching);
        }
    },

    // 下拉刷新
    onPullDownRefresh: function() {
        try {
            this.setData({
                page: 1,
                listData: [],
                hasMore: true,
                hidden: true
            });
            
            let url = `${app.globalData.http_api}&s=workorder&c=search&pagesize=${this.data.limit}&page=1`;
            
            if (this.data.currentTab > 0) {
                url += `&jindu=${this.data.currentTab}`;
            }
            
            if (this.data.isSearching && this.data.searchKeyword) {
                url += `&keyword=${encodeURIComponent(this.data.searchKeyword)}`;
            }
            
            url += '&api_call_function=module_list';

            wx.request({
                url: url,
                method: 'GET',
                success: (res) => {
                    if (res.data.code === 1) {
                        const newData = res.data.data || [];
                        const hasMore = newData.length === this.data.limit;
                        
                        this.setData({
                            listData: newData,
                            hasMore: hasMore,
                            hidden: !hasMore,
                            page: 2
                        });
                    } else {
                        throw new Error(res.data.msg || '刷新失败');
                    }
                },
                fail: (err) => {
                    console.error('刷新失败:', err);
                    wx.showToast({
                        title: '刷新失败，请重试',
                        icon: 'none'
                    });
                },
                complete: () => {
                    this.setData({ 
                        loading: false,
                        refreshing: false
                    });
                    wx.stopPullDownRefresh();
                }
            });
        } catch (err) {
            console.error('下拉刷新错误:', err);
            this.setData({ 
                loading: false,
                refreshing: false
            });
            wx.stopPullDownRefresh();
            wx.showToast({
                title: err.message || '刷新失败',
                icon: 'none'
            });
        }
    },

    // 搜索输入事件
    onSearchInput: function(e) {
        this.setData({
            searchKeyword: e.detail.value
        });
    },

    // 清除搜索
    clearSearch: function() {
        this.setData({
            searchKeyword: '',
            isSearching: false,
            page: 1,
            listData: []
        });
        this.loadData();
    },

    // 搜索确认事件
    onSearchConfirm: function() {
        if (!this.data.searchKeyword.trim()) {
            wx.showToast({
                title: '请输入搜索关键词',
                icon: 'none'
            });
            return;
        }
        
        this.setData({
            isSearching: true,
            page: 1,
            listData: []
        });
        
        this.loadData(true);
    },

    // 加载数据
    loadData: function(isSearch = false, retryCount = 0) {
        if (this.data.loading) return;
        if (retryCount >= 3) {
            wx.showToast({
                title: '加载失败，请检查网络',
                icon: 'none'
            });
            return;
        }

        this.setData({ loading: true });
        
        try {
            let url = `${app.globalData.http_api}&s=workorder&c=search&pagesize=${this.data.limit}&page=${this.data.page}`;
            
            if (this.data.currentTab > 0) {
                url += `&jindu=${this.data.currentTab}`;
            }
            
            if (isSearch && this.data.searchKeyword) {
                url += `&keyword=${encodeURIComponent(this.data.searchKeyword)}`;
            }
            
            url += '&api_call_function=module_list';

            wx.request({
                url: url,
                method: 'GET',
                success: (res) => {
                    if (res.data.code === 1) {
                        const newData = res.data.data || [];
                        const hasMore = newData.length === this.data.limit;
                        
                        this.setData({
                            listData: this.data.page === 1 ? newData : [...this.data.listData, ...newData],
                            hasMore: hasMore,
                            hidden: !hasMore,
                            page: this.data.page + 1,
                            retryCount: 0
                        });

                        if (!hasMore) {
                            setTimeout(() => {
                                this.setData({ hidden: true });
                            }, 1500);
                        }
                    } else {
                        throw new Error(res.data.msg || '加载失败');
                    }
                },
                fail: (err) => {
                    console.error('请求失败:', err);
                    // 重试
                    setTimeout(() => {
                        this.loadData(isSearch, retryCount + 1);
                    }, 1000);
                },
                complete: () => {
                    this.setData({ loading: false });
                }
            });
        } catch (err) {
            console.error('加载数据错误:', err);
            this.setData({ loading: false });
            wx.showToast({
                title: err.message || '加载失败',
                icon: 'none'
            });
        }
    },

    // 长按事件处理
    onLongPress: function(e) {
        const member = wx.getStorageSync('member');
        
        // 只检查is_admin值是否大于0，仅超级管理员可删除
        const isAdmin = member.is_admin && parseInt(member.is_admin) > 0;
        
        // 只有超级管理员可以看到操作菜单
        if (isAdmin) {
            this.setData({
                showActionMenu: true,
                selectedId: e.currentTarget.dataset.id
            });
        }
    },

    // 隐藏操作菜单
    hideActionMenu: function() {
        this.setData({
            showActionMenu: false,
            selectedId: null
        });
    },

    // 删除工单
    onDelete: function(e) {
        const id = this.data.selectedId;
        
        if (!id) {
            wx.showToast({
                title: '参数错误',
                icon: 'none'
            });
            return;
        }
        
        // 再次验证当前用户的超级管理员权限
        const member = wx.getStorageSync('member');
        const isAdmin = member.is_admin && parseInt(member.is_admin) > 0;
        
        if (!isAdmin) {
            wx.showToast({
                title: '没有删除权限',
                icon: 'none',
                duration: 2000
            });
            return;
        }
        
        const that = this;
        const member_auth = wx.getStorageSync('member_auth');
        const member_uid = wx.getStorageSync('member_uid');
        
        // 首先获取原始http_api地址
        let baseUrl = app.globalData.http_api;
        
        // 检查http_api是否已经包含查询参数
        if (baseUrl.includes('?')) {
            // 如果已经包含?但没有以&结尾，添加&
            if (!baseUrl.endsWith('&')) {
                baseUrl += '&';
            }
        } else {
            // 如果不包含?，添加?
            baseUrl += '?';
        }
        
        // 构建删除API URL
        const requestUrl = `${baseUrl}s=httpapi&m=delete&appid=${app.globalData.appid}&appsecret=${app.globalData.appsecret}&api_auth_code=${member_auth}&api_auth_uid=${member_uid}`;
        
        wx.showModal({
            title: '提示',
            content: '确定要删除这条工单吗？',
            success: function(res) {
                if (res.confirm) {
                    // 构建POST数据
                    const postData = `id=${id}&table=1_workorder&siteid=1&module=workorder`;
                    
                    wx.request({
                        url: requestUrl,
                        method: 'POST',
                        data: postData,
                        header: {
                            'Content-Type': 'application/x-www-form-urlencoded'
                        },
                        success: function(res) {
                            // 判断删除是否成功
                            if (res.data.code == 1 || res.data.code == 112 || (res.data.msg && res.data.msg.toLowerCase() === "ok")) {
                                // 弹窗提示并在确认后刷新列表
                                wx.showModal({
                                    title: '提示',
                                    content: '删除成功',
                                    showCancel: false,
                                    success: function(modalRes) {
                                        if (modalRes.confirm) {
                                            // 刷新列表数据
                                            that.setData({
                                                page: 1,
                                                listData: []
                                            });
                                            
                                            that.loadData();
                                        }
                                    }
                                });
                            } else {
                                wx.showToast({
                                    title: res.data.msg || '删除失败',
                                    icon: 'none',
                                    duration: 2000
                                });
                            }
                        },
                        fail: function(err) {
                            wx.showToast({
                                title: '网络错误，请重试',
                                icon: 'none',
                                duration: 2000
                            });
                        }
                    });
                }
            }
        });
        
        this.hideActionMenu();
    },
});
