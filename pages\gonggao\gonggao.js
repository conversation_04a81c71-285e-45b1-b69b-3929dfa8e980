var app = getApp();
var http_url = app.globalData.http_api;

Page({
  data: {
    notice: null
  },

  onLoad: function(options) {
    const noticeId = options.id;
    this.loadNoticeDetail(noticeId);
  },

  loadNoticeDetail: function(id) {
    var self = this;
    wx.showLoading({
      title: '加载中...',
      mask: true
    });

    wx.request({
      url: http_url + 'index.php',
      data: {
        s: 'news',
        c: 'show',
        id: id,
        api_call_function: 'module_show',
        appid: app.globalData.appid,
        appsecret: app.globalData.appsecret
      },
      method: 'GET',
      success: function(res) {
        if (res.data.code == 1) {
          self.setData({
            notice: res.data.data
          });
        } else {
          wx.showToast({
            title: '获取公告详情失败',
            icon: 'none'
          });
        }
      },
      fail: function() {
        wx.showToast({
          title: '网络错误',
          icon: 'none'
        });
      },
      complete: function() {
        wx.hideLoading();
      }
    });
  }
}); 