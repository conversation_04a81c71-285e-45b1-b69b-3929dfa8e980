/**
 * 财务记录添加页面
 * 
 * 功能：
 * - 添加财务收支记录
 * - 上传凭证图片
 * - 提交表单数据到服务器
 */
var app = getApp();

Page({
  /**
   * 页面初始数据
   */
  data: {
    // 账户与权限
    isAdmin: false,          // 是否为管理员
    isLoggedIn: false,       // 是否已登录
    
    // 基本信息
    catid: '',               // 年度栏目ID，初始为空，将在初始化中设置为当前年度
    date: '',                // 记账日期
    title: '',               // 项目名称
    amount: '',              // 金额
    author: '',              // 经手人
    
    // 收支分类信息
    szxm: '1',               // 收支类型：1-收入，2-支出，默认收入
    shouruleibie: '2',       // 收入类别：1-转移收入，2-经营收入，3-其它收入，4-上级补助，5-代收代付
    skfs: '1',               // 收款方式：1-转公账，2-存现金
    zhichuleibie: '1',       // 支出类别：1-办公支出，2-项目支出，3-代收代付，4-专项支出
    fkfs: '1',               // 付款方式：1-转账，2-现金，4-预备金，5-代收代付
    
    // 凭证与备注
    pingzhengImages: [],     // 凭证图片列表
    pingzhengIds: [],        // 已上传凭证的ID列表
    description: '',         // 描述/备注信息
    
    // 表单状态
    formValid: false,        // 表单是否有效
    isSubmitting: false,     // 是否正在提交
    isUploading: false,      // 是否正在上传
    
    // UI配置
    categoryList: [],        // 从接口获取的年度栏目列表
    yearOptions: ['2021年', '2022年', '2023年', '2024年', '2025年'], // 备用年度选项
    yearIndex: 0,            // 所选年度索引
    
    // 主题配色
    themeColors: {
      primary: '#1890ff',
      success: '#52c41a',
      warning: '#faad14',
      error: '#f5222d',
      background: '#f5f7fa'
    }
  },

  /**
   * ===============================================
   * 生命周期函数
   * ===============================================
   */
  
  /**
   * 页面加载
   */
  onLoad: function(options) {
    // 检查权限
    this.checkAdminPermission();
    
    // 设置默认日期与经手人
    this.initFormDefaults();
    
    // 获取年度栏目
    this.getCategoryList();
    
    // 验证表单
    this.validateForm();
  },
  
  /**
   * ===============================================
   * 初始化和数据加载函数
   * ===============================================
   */
  
  /**
   * 初始化表单默认值
   */
  initFormDefaults: function() {
    // 设置默认日期为当天
    const today = new Date();
    const year = today.getFullYear();
    const month = String(today.getMonth() + 1).padStart(2, '0');
    const day = String(today.getDate()).padStart(2, '0');
    const formattedDate = `${year}-${month}-${day}`;
    
    // 获取当前用户名作为默认经手人
    let username = '';
    try {
      const member = wx.getStorageSync('member');
      if (member && member.username) {
        username = member.username;
      }
    } catch (e) {
      console.error('获取用户信息失败', e);
    }
    
    // 设置当前年份对应的索引
    const yearIndex = Math.max(0, Math.min(year - 2021, this.data.yearOptions.length - 1));
    
    this.setData({
      date: formattedDate,
      author: username,
      yearIndex: yearIndex,
      catid: (yearIndex + 1).toString()
    });
    
    console.log('初始化表单默认值完成');
  },

  /**
   * 获取年度栏目列表
   */
  getCategoryList: function() {
    wx.showLoading({
      title: '加载年度数据...',
    });
    
    // 获取用户认证信息
    const authInfo = this.getAuthInfo();
    
    // 请求栏目数据
    wx.request({
      url: app.globalData.http_api,
      method: 'GET',
      data: {
        s: 'httpapi',
        m: 'category',
        mid: 'caiwu',
        pid: 0, // 获取顶级栏目（年度）
        ...authInfo
      },
      success: (res) => {
        console.log("获取栏目列表:", res.data);
        if (res.data && res.data.code == 1) {
          // 处理返回的栏目数据 - 数据是对象格式，需要转换为数组
          let categoryList = [];
          
          if (res.data.data && typeof res.data.data === 'object') {
            // 将对象转换为数组
            const dataArray = Object.values(res.data.data);
            if (dataArray.length > 0) {
              categoryList = dataArray;
            }
          }
          
          console.log("处理后的栏目列表:", categoryList);
          
          if (categoryList.length > 0) {
            // 提取名称作为选项
            const yearOptions = categoryList.map(item => item.name);
            
            // 查找当前年份对应的选项
            const currentYear = new Date().getFullYear();
            let yearIndex = yearOptions.findIndex(name => name.includes(currentYear.toString()));
            
            // 如果没找到当前年份，使用第一个
            if (yearIndex === -1) yearIndex = 0;
            
            this.setData({ 
              categoryList,
              yearOptions,
              yearIndex,
              catid: categoryList[yearIndex].id.toString()
            });
          }
        } else {
          wx.showToast({
            title: res.data.msg || '获取年度选项失败',
            icon: 'none'
          });
        }
      },
      fail: (err) => {
        console.error('请求栏目数据失败:', err);
      },
      complete: () => {
        wx.hideLoading();
      }
    });
  },
  
  /**
   * ===============================================
   * 权限与认证相关函数
   * ===============================================
   */
  
  /**
   * 获取用户认证信息
   */
  getAuthInfo: function() {
    return {
      member_auth: wx.getStorageSync('member_auth'),
      member_uid: wx.getStorageSync('member_uid')
    };
  },

  /**
   * 检查管理员权限
   */
  checkAdminPermission: function() {
    try {
      // 获取当前用户信息
      const member = wx.getStorageSync('member');
      const member_auth = wx.getStorageSync('member_auth');
      const member_uid = wx.getStorageSync('member_uid');
      
      if (member && member.id && member_auth && member_uid) {
        // 判断是否是管理员，is_admin > 0 表示是管理员
        const isAdmin = member.is_admin && parseInt(member.is_admin) > 0;
        this.setData({
          isAdmin: isAdmin,
          isLoggedIn: true
        });

        // 如果不是管理员，提示无权访问并返回
        if (!isAdmin) {
          this.showNoPermissionError();
        }
      } else {
        this.setData({
          isAdmin: false,
          isLoggedIn: false
        });
        
        // 未登录，提示登录并返回
        this.showLoginRequired();
      }
    } catch (err) {
      console.error('权限检查错误:', err);
      wx.showToast({
        title: '系统错误，请重试',
        icon: 'none'
      });
    }
  },
  
  /**
   * 显示无权限错误
   */
  showNoPermissionError: function() {
    wx.showModal({
      title: '权限提示',
      content: '您没有管理员权限，无法添加财务记录',
      showCancel: false,
      success: () => wx.navigateBack()
    });
  },
  
  /**
   * 显示需要登录提示
   */
  showLoginRequired: function() {
    wx.showModal({
      title: '登录提示',
      content: '请先登录并确认您拥有管理员权限',
      showCancel: false,
      success: () => wx.navigateBack()
    });
  },

  /**
   * 检查登录状态
   */
  checkLoginStatus: function() {
    const member = wx.getStorageSync('member');
    const member_auth = wx.getStorageSync('member_auth');
    const member_uid = wx.getStorageSync('member_uid');
    return member && member.id && member_auth && member_uid;
  },

  /**
   * ===============================================
   * 表单操作与数据输入函数
   * ===============================================
   */
  
  /**
   * 选择年份分类
   */
  bindCatidChange: function(e) {
    const index = e.detail.value;
    this.setData({
      yearIndex: index,
      catid: this.data.categoryList.length > 0 ? 
        this.data.categoryList[index].id.toString() : 
        (parseInt(index) + 1).toString()
    });
  },

  /**
   * 选择日期
   */
  bindDateChange: function(e) {
    this.setData({
      date: e.detail.value
    });
    this.validateForm();
  },

  /**
   * 切换收支类型
   */
  switchSzxm: function(e) {
    this.setData({
      szxm: e.currentTarget.dataset.type
    });
    this.validateForm();
  },
  
  /**
   * 切换收入类别
   */
  switchShouruleibie: function(e) {
    this.setData({
      shouruleibie: e.currentTarget.dataset.type
    });
  },
  
  /**
   * 切换收款方式
   */
  switchSkfs: function(e) {
    this.setData({
      skfs: e.currentTarget.dataset.type
    });
  },
  
  /**
   * 切换支出类别
   */
  switchZhichuleibie: function(e) {
    this.setData({
      zhichuleibie: e.currentTarget.dataset.type
    });
  },
  
  /**
   * 切换付款方式
   */
  switchFkfs: function(e) {
    this.setData({
      fkfs: e.currentTarget.dataset.type
    });
  },

  /**
   * 输入处理函数集合
   */
  inputHandler: function(e) {
    const { field } = e.currentTarget.dataset;
    const value = e.detail.value.trim();
    
    // 动态设置数据
    const data = {};
    data[field] = value;
    this.setData(data);
    
    // 如果是关键字段，则验证表单
    if (['title', 'amount', 'date'].includes(field)) {
      this.validateForm();
    }
  },
  
  /**
   * 表单验证
   */
  validateForm: function() {
    const { title, amount, date } = this.data;
    // 只验证必填字段：标题、金额和日期
    const isValid = title.length > 0 && amount.length > 0 && !isNaN(amount) && date.length > 0;
    this.setData({
      formValid: isValid
    });
  },

  /**
   * ===============================================
   * 凭证图片处理函数
   * ===============================================
   */
  
  /**
   * 选择凭证图片
   */
  chooseImage: function() {
    // 检查登录状态
    const member = wx.getStorageSync('member');
    const member_auth = wx.getStorageSync('member_auth');
    const member_uid = wx.getStorageSync('member_uid');
    
    console.log('登录状态检查:', {
      member: member ? '已存在' : '不存在',
      member_id: member ? member.id : '无',
      member_auth: member_auth ? '已存在' : '不存在',
      member_uid: member_uid ? '已存在' : '不存在',
      is_admin: member ? member.is_admin : '无'
    });
    
    if (!this.checkLoginStatus()) {
      wx.showModal({
        title: '提示',
        content: '请先登录后再操作',
        success: function(res) {
          if (res.confirm) {
            wx.navigateTo({
              url: '/pages/login/login'
            });
          }
        }
      });
      return;
    }
    
    const that = this;
    
    // 保存上传前的状态
    this.setData({
      isUploading: true
    });
    
    wx.chooseMedia({
      count: 9, // 每次最多可选9张，但不限制总数
      mediaType: ['image'],
      sourceType: ['album', 'camera'],
      camera: 'back',
      success: function(res) {
        console.log('选择图片成功:', res);
        
        // 处理临时图片数据
        let tempImages = res.tempFiles.map(file => ({
          path: file.tempFilePath,
          size: file.size,
          uploaded: false,
          id: ''
        }));
        
        // 先添加临时图片到预览
        const newImages = [...that.data.pingzhengImages, ...tempImages];
        that.setData({
          pingzhengImages: newImages
        });
        
        // 处理图片上传
        that.uploadPingzhengImages(tempImages);
      },
      fail: function(err) {
        that.setData({
          isUploading: false
        });
        console.error('选择图片失败:', err);
      }
    });
  },
  
  /**
   * 上传凭证图片
   */
  uploadPingzhengImages: function(images) {
    if (!images || images.length === 0) {
      this.setData({
        isUploading: false
      });
      return;
    }
    
    wx.showLoading({
      title: '上传凭证中...',
      mask: true
    });
    
    // 获取用户认证信息
    const memberAuth = wx.getStorageSync('member_auth');
    const memberUid = wx.getStorageSync('member_uid');
    
    console.log('上传凭证认证信息:', {
      memberAuth: memberAuth ? '已存在' : '不存在',
      memberUid: memberUid ? '已存在' : '不存在'
    });
    
    if (!memberAuth || !memberUid) {
      wx.hideLoading();
      this.setData({
        isUploading: false
      });
      this.showLoginRequired();
      return;
    }
    
    // 构建上传URL
    const uploadUrl = `${app.globalData.http_api}s=api&c=file&m=upload&fid=227&member_auth=${memberAuth}&member_uid=${memberUid}`;
    
    let uploadedCount = 0;
    let failedCount = 0;
    
    // 上传每张图片
    images.forEach((image, index) => {
      // 检查文件大小
      if (image.size > 10 * 1024 * 1024) { // 10MB限制
        this.handleImageUploadFailure(image, '图片大小不能超过10MB');
        uploadedCount++;
        failedCount++;
        
        // 检查是否全部处理完成
        if (uploadedCount === images.length) {
          this.finishUploadProcess(failedCount);
        }
        return;
      }
      
      // 执行上传
      wx.uploadFile({
        url: uploadUrl,
        filePath: image.path,
        name: 'file_data',
        formData: {
          is_ajax: 1,
          file_type: 'image'
        },
        header: {
          'Content-Type': 'multipart/form-data'
        },
        success: (res) => {
          // 解析返回的数据
          try {
            const responseData = JSON.parse(res.data);
            if (responseData.code) {
              // 上传成功，更新图片状态
              this.handleImageUploadSuccess(image, responseData);
            } else {
              // 上传失败
              failedCount++;
              this.handleImageUploadFailure(image, responseData.msg || '上传失败');
            }
          } catch (error) {
            failedCount++;
            console.error('解析上传响应失败:', error);
            this.handleImageUploadFailure(image, '响应解析失败');
          }
        },
        fail: (err) => {
          failedCount++;
          console.error('上传请求失败:', err);
          this.handleImageUploadFailure(image, '网络错误');
        },
        complete: () => {
          uploadedCount++;
          if (uploadedCount === images.length) {
            this.finishUploadProcess(failedCount);
          }
        }
      });
    });
  },
  
  /**
   * 处理图片上传成功
   */
  handleImageUploadSuccess: function(image, responseData) {
    const allImages = [...this.data.pingzhengImages];
    const imageIndex = allImages.findIndex(img => img.path === image.path);
    
    if (imageIndex !== -1) {
      // 更新图片状态
      allImages[imageIndex].uploaded = true;
      allImages[imageIndex].id = responseData.code;
      
      // 如果返回了URL，更新路径
      if (responseData.data && responseData.data.url) {
        allImages[imageIndex].path = responseData.data.url;
      }
      
      // 更新状态
      const pingzhengIds = [...this.data.pingzhengIds, responseData.code];
      this.setData({
        pingzhengImages: allImages,
        pingzhengIds: pingzhengIds
      });
    }
  },
  
  /**
   * 处理图片上传失败
   */
  handleImageUploadFailure: function(image, errorMsg) {
    // 显示错误信息
    console.error('上传图片失败:', errorMsg);
    
    // 更新界面，移除失败的图片
    const allImages = [...this.data.pingzhengImages];
    const imageIndex = allImages.findIndex(img => img.path === image.path);
    
    if (imageIndex !== -1) {
      allImages.splice(imageIndex, 1);
      this.setData({
        pingzhengImages: allImages
      });
    }
  },
  
  /**
   * 完成上传流程
   */
  finishUploadProcess: function(failedCount) {
    wx.hideLoading();
    this.setData({
      isUploading: false
    });
    
    if (failedCount > 0) {
      wx.showToast({
        title: `${failedCount}张图片上传失败`,
        icon: 'none'
      });
    }
  },
  
  /**
   * 预览图片
   */
  previewImage: function(e) {
    const index = e.currentTarget.dataset.index;
    const urls = this.data.pingzhengImages.map(img => img.path);
    
    wx.previewImage({
      current: urls[index],
      urls: urls
    });
  },
  
  /**
   * 删除图片
   */
  deleteImage: function(e) {
    const index = e.currentTarget.dataset.index;
    const images = this.data.pingzhengImages;
    const ids = [...this.data.pingzhengIds];
    
    // 如果已上传，从ids中也删除
    if (images[index].uploaded && images[index].id) {
      const idIndex = ids.indexOf(images[index].id);
      if (idIndex !== -1) {
        ids.splice(idIndex, 1);
      }
    }
    
    // 从图片数组中删除
    images.splice(index, 1);
    
    this.setData({
      pingzhengImages: images,
      pingzhengIds: ids
    });
  },

  /**
   * ===============================================
   * 表单提交相关函数
   * ===============================================
   */
  
  /**
   * 提交表单
   */
  submitForm: function() {
    if (!this.data.formValid) {
      wx.showToast({
        title: '请完善必填信息',
        icon: 'none'
      });
      return;
    }

    if (!this.data.isAdmin) {
      wx.showToast({
        title: '无权限操作',
        icon: 'none'
      });
      return;
    }

    const amount = parseFloat(this.data.amount);
    if (isNaN(amount) || amount <= 0) {
      wx.showToast({
        title: '请输入有效金额',
        icon: 'none'
      });
      return;
    }
    
    // 避免重复提交
    if (this.data.isSubmitting) {
      return;
    }
    
    this.setData({
      isSubmitting: true
    });

    // 显示加载中
    wx.showLoading({
      title: '正在保存...',
      mask: true
    });

    // 构建表单数据
    const formData = this.buildFormData();
    
    // 提交到服务器
    this.submitDataToServer(formData);
  },
  
  /**
   * 构建表单数据
   */
  buildFormData: function() {
    // 获取用户认证信息
    const member_auth = wx.getStorageSync('member_auth');
    const member_uid = wx.getStorageSync('member_uid');
    
    console.log('表单提交认证信息:', {
      member_auth: member_auth ? '已存在' : '不存在',
      member_uid: member_uid ? '已存在' : '不存在'
    });
    
    // 以字符串形式构建请求参数
    let postParams = "is_form=1"
      + "&is_admin=1"
      + "&is_tips="
      + "&is_draft=0"
      + "&module=caiwu"
      + "&id=0"
      + "&catid=" + this.data.catid
      + "&data[title]=" + encodeURIComponent(this.data.title)
      + "&data[money]=" + parseFloat(this.data.amount)
      + "&data[riqi]=" + encodeURIComponent(this.data.date)
      + "&data[author]=" + encodeURIComponent(this.data.author)
      + "&data[szxm]=" + this.data.szxm
      + "&data[shouruleibie]=" + (this.data.shouruleibie || '2')
      + "&data[skfs]=" + (this.data.skfs || '1')
      + "&data[zhichuleibie]=" + (this.data.zhichuleibie || '1')
      + "&data[fkfs]=" + (this.data.fkfs || '1');

    // 添加描述（如果有）
    if (this.data.description) {
      postParams += "&data[description]=" + encodeURIComponent(this.data.description);
    }
    
    // 添加凭证IDs（如果有）
    if (this.data.pingzhengIds && this.data.pingzhengIds.length > 0) {
      this.data.pingzhengIds.forEach(id => {
        postParams += "&data[pingzheng][]=" + id;
      });
    }

    // 添加认证信息
    postParams += "&member_auth=" + member_auth
                + "&member_uid=" + member_uid;
                
    return postParams;
  },
  
  /**
   * 提交数据到服务器
   */
  submitDataToServer: function(formData) {
    // 构建完整请求URL
    const requestUrl = app.globalData.http_api + 's=member&app=caiwu&c=home&m=add';
    
    wx.request({
      url: requestUrl,
      method: 'POST',
      header: {
        'Content-Type': 'application/x-www-form-urlencoded'
      },
      data: formData,
      success: (res) => {
        wx.hideLoading();
        console.log('服务器响应:', res.data);
        
        this.setData({
          isSubmitting: false
        });
        
        if (res.data && res.data.code == 1) {
          this.handleSubmitSuccess();
        } else {
          this.handleSubmitError(res.data);
        }
      },
      fail: (err) => {
        wx.hideLoading();
        console.error('提交财务记录失败:', err);
        
        this.setData({
          isSubmitting: false
        });
        
        wx.showModal({
          title: '网络错误',
          content: '请检查您的网络连接并重试',
          showCancel: false
        });
      }
    });
  },
  
  /**
   * 处理提交成功
   */
  handleSubmitSuccess: function() {
    // 成功提示
    wx.showToast({
      title: '添加成功',
      icon: 'success',
      duration: 2000
    });
    
    // 返回上一页
    setTimeout(() => {
      wx.navigateBack();
    }, 1500);
  },
  
  /**
   * 处理提交错误
   */
  handleSubmitError: function(response) {
    // 显示具体的错误信息
    let errorMsg = response.msg || '添加失败';
    if (response.data && response.data.field) {
      const fieldMap = {
        'riqi': '日期',
        'title': '项目名称',
        'money': '金额',
        'szxm': '收支类型',
        'shouruleibie': '收入类别',
        'skfs': '收款方式',
        'zhichuleibie': '支出类别',
        'fkfs': '付款方式'
      };
      const fieldName = fieldMap[response.data.field] || response.data.field;
      errorMsg += `(${fieldName})`;
    }
    
    wx.showModal({
      title: '提交失败',
      content: errorMsg,
      showCancel: false
    });
  },
  
  /**
   * 返回上一页
   */
  goBack: function() {
    wx.navigateBack();
  }
}) 