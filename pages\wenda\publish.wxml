<view class="container">
  <!-- 头部区域 - 更加现代化的设计 -->
  <view class="header">
    <view class="page-title">
      <text class="title-text">发布问题</text>
      <text class="title-description">分享您的问题，获得专业解答</text>
    </view>
  </view>

  <!-- 表单区域 - 增加卡片式设计 -->
  <view class="form-container">
    <!-- 问题分类 - 改进选择器样式 -->
    <view class="form-card">
      <view class="form-item">
        <view class="form-label">
          <text class="label-text">问题分类</text>
          <text class="label-required">*</text>
        </view>
        
        <!-- 重新设计的分类选择区域 -->
        <view wx:if="{{!isLoadingCategories && parentCategories.length > 0}}">
          <!-- 父分类选择器 -->
          <view class="category-container">
            <scroll-view scroll-x class="category-scroll">
              <view class="category-list">
                <block wx:for="{{parentCategories}}" wx:key="id">
                  <view class="category-item {{categoryIndex[0] === index ? 'active' : ''}}" 
                        bindtap="selectParentCategory" 
                        data-index="{{index}}">
                    {{item.name}}
                  </view>
                </block>
              </view>
            </scroll-view>
          </view>
          
          <!-- 子分类选择器 -->
          <view class="subcategory-container" wx:if="{{childCategories[parentCategories[categoryIndex[0]].id].length > 0}}">
            <scroll-view scroll-x class="category-scroll">
              <view class="category-list">
                <block wx:for="{{childCategories[parentCategories[categoryIndex[0]].id]}}" wx:key="id">
                  <view class="category-item {{categoryIndex[1] === index ? 'active' : ''}}" 
                        bindtap="selectChildCategory" 
                        data-index="{{index}}">
                    {{item.name}}
                  </view>
                </block>
              </view>
            </scroll-view>
          </view>
          
          <!-- 当前选择显示 -->
          <view class="selected-category">
            <view class="selected-category-label">当前选择：</view>
            <view class="selected-category-value">{{parentCategories[categoryIndex[0]].name}}{{childCategories[parentCategories[categoryIndex[0]].id] && childCategories[parentCategories[categoryIndex[0]].id][categoryIndex[1]] ? '>>'+childCategories[parentCategories[categoryIndex[0]].id][categoryIndex[1]].name : ''}}</view>
          </view>
        </view>

        <!-- 加载中显示 -->
        <view wx:elif="{{isLoadingCategories}}" class="loading-view">
          <text class="loading-text">正在加载分类...</text>
        </view>
        
        <!-- 错误显示 -->
        <view wx:else class="error-view">
          <text class="error-text">未能加载分类，请返回重试</text>
        </view>
      </view>
    </view>
    
    <!-- 问题标题 - 改进输入框样式 -->
    <view class="form-card">
      <view class="form-item">
        <view class="form-label">
          <text class="label-text">问题标题</text>
          <text class="label-required">*</text>
        </view>
        <input class="form-input" placeholder="简明扼要地描述您的问题（5-50字）" bindinput="onTitleInput" value="{{title}}" maxlength="50" />
        <view class="char-count {{titleLength >= 5 ? 'valid' : 'invalid'}}">{{titleLength}}/50</view>
      </view>
    
      <!-- 问题描述 - 改进文本域样式 -->
      <view class="form-item">
        <view class="form-label">
          <text class="label-text">问题详情</text>
          <text class="label-required">*</text>
        </view>
        <textarea class="form-textarea" placeholder="详细描述您的问题，提供相关背景信息以获得更准确的解答（10-500字）" bindinput="onContentInput" value="{{content}}" maxlength="500" />
        <view class="char-count {{contentLength >= 10 ? 'valid' : 'invalid'}}">{{contentLength}}/500</view>
      </view>
    </view>

    <!-- 添加图片区域 - 改进图片上传UI -->
    <view class="form-card">
      <view class="form-section-title">添加图片（选填）</view>
      
      <!-- 水印照片上传 -->
      <view class="form-item">
        <view class="form-label">
          <text class="label-text">水印照片</text>
          <text class="label-tip">最多30张，每张≤10M</text>
        </view>
        <view class="image-uploader">
          <view class="image-list">
            <block wx:for="{{watermarkImages}}" wx:key="index">
              <view class="image-item">
                <image src="{{item}}" mode="aspectFill" bindtap="previewImage" data-type="watermark" data-url="{{item}}"></image>
                <view class="delete-btn" catchtap="removeImage" data-type="watermark" data-index="{{index}}">×</view>
              </view>
            </block>
            <view class="upload-btn" bindtap="chooseImage" data-type="watermark" wx:if="{{watermarkImages.length < 30}}">
              <image src="../../icons/upphoto.png" mode="aspectFit"></image>
              <text class="upload-text">添加图片</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 原图照片上传 -->
      <view class="form-item">
        <view class="form-label">
          <text class="label-text">原图照片</text>
          <text class="label-tip">最多30张，每张≤10M</text>
        </view>
        <view class="image-uploader">
          <view class="image-list">
            <block wx:for="{{originalImages}}" wx:key="index">
              <view class="image-item">
                <image src="{{item}}" mode="aspectFill" bindtap="previewImage" data-type="original" data-url="{{item}}"></image>
                <view class="delete-btn" catchtap="removeImage" data-type="original" data-index="{{index}}">×</view>
              </view>
            </block>
            <view class="upload-btn" bindtap="chooseImage" data-type="original" wx:if="{{originalImages.length < 30}}">
              <image src="../../icons/upphoto.png" mode="aspectFit"></image>
              <text class="upload-text">添加图片</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 提示信息区域 -->
    <view class="tips-section">
      <view class="tip-item">
        <text class="tip-icon">i</text>
        <text class="tip-text">清晰的问题描述和相关图片有助于更快获得解答</text>
      </view>
      <view class="tip-item">
        <text class="tip-icon">i</text>
        <text class="tip-text">请确保您的问题不含敏感信息</text>
      </view>
    </view>

    <!-- 提交按钮 - 更加突出的按钮设计 -->
    <view class="form-action">
      <button class="submit-btn {{canSubmit ? 'active' : 'disabled'}}" bindtap="submitQuestion" disabled="{{!canSubmit}}">
        <text class="btn-text">发布问题</text>
      </button>
    </view>
  </view>
</view> 