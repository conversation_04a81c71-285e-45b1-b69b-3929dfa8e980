# 首页重新设计说明

## 设计要求
根据用户要求，重新设计首页，主要包含：
1. 搜索框功能
2. 最新更新列表

## 新增功能

### 1. 搜索框功能（村民档案搜索）
- **位置**: 页面顶部，取代了原来的轮播图
- **搜索对象**: 村民档案（姓名或身份证号）
- **权限要求**: 仅管理员可使用
- **功能特性**:
  - 实时搜索输入
  - 搜索历史记录（最多保存10条，显示8条）
  - 搜索历史管理（清空功能）
  - 获得焦点时自动显示搜索历史
  - 支持回车键搜索
  - 搜索按钮（输入内容时显示）
  - 权限验证（未登录或非管理员会提示）

### 2. 最新更新列表
- **位置**: 取代了原来的"公告通知"和"最新工单"两个独立区域
- **功能特性**:
  - 合并显示工单和公告的最新更新
  - 筛选功能：全部、工单、公告
  - 按更新时间排序（最新的在前）
  - 分页加载更多功能
  - 空状态提示
  - 点击跳转到对应详情页

### 3. 保留的原有功能
- 工单统计概览（总工单数、待处理、处理中、已完成）
- 快捷功能区（提交工单、村民档案、常见问题、我的工单）
- 下拉刷新功能
- 用户权限控制（保密工单的访问控制）

## 技术实现

### 数据结构变化
```javascript
// 新增搜索相关数据
searchKeyword: '',          // 搜索关键词
showSearchHistory: false,   // 是否显示搜索历史
searchHistory: [],          // 搜索历史记录

// 新增更新列表相关数据
updateFilter: 'all',        // 更新筛选器
allUpdates: [],            // 所有更新数据
filteredUpdates: [],       // 筛选后的更新数据
updatePage: 1,             // 更新列表当前页码
updateHasMore: false,      // 是否还有更多更新数据
updateIsLoading: false,    // 是否正在加载更新数据
```

### 主要方法
- `onSearchInput()` - 搜索输入处理
- `onSearchConfirm()` - 搜索确认
- `onSearchFocus()` / `onSearchBlur()` - 搜索框焦点处理
- `loadLatestUpdates()` - 加载最新更新数据
- `changeUpdateFilter()` - 切换更新筛选器
- `filterUpdates()` - 筛选更新数据
- `goToUpdateDetail()` - 跳转到更新详情
- 搜索历史管理相关方法

### 样式特点
- 现代化的搜索框设计，带有圆角和阴影效果
- 搜索历史标签式展示
- 更新列表卡片式设计
- 类型徽章区分工单和公告
- 响应式交互效果
- 统一的色彩方案（主色调：#1890ff）

## 使用说明

### 搜索功能（村民档案）
1. 点击搜索框开始输入村民姓名或身份证号
2. 输入时会显示搜索历史（如果有）
3. 点击历史标签可快速选择
4. 按回车或点击搜索按钮执行搜索
5. 系统会验证用户权限（仅管理员可用）
6. 搜索结果会跳转到村民档案列表页面

### 最新更新
1. 默认显示所有类型的更新
2. 点击筛选标签可切换显示内容
3. 点击更新项可跳转到详情页
4. 滚动到底部可加载更多内容
5. 下拉可刷新所有数据

## 兼容性
- 保持与原有API接口的兼容性
- 保留所有原有的用户权限控制逻辑
- 保持原有的页面跳转路径不变
