/* 整体容器 */
.container {
  padding: 30rpx 24rpx;
  background-color: #f7f7f7;
  min-height: 100vh;
}

/* 页头样式 */
.page-header {
  padding: 20rpx 10rpx 30rpx;
  position: relative;
}

.header-title {
  font-size: 38rpx;
  color: #333;
  font-weight: 600;
  margin-bottom: 8rpx;
}

.header-id {
  font-size: 26rpx;
  color: #888;
}

/* 主要卡片 */
.finance-card {
  background-color: #fff;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 16rpx rgba(0, 0, 0, 0.08);
  padding: 0;
  margin-bottom: 30rpx;
  overflow: hidden;
}

/* 基本信息区域 */
.finance-header {
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.title-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.finance-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  flex: 1;
}

.finance-badge {
  padding: 6rpx 20rpx;
  border-radius: 24rpx;
  font-size: 24rpx;
  font-weight: 500;
}

.finance-badge.income {
  background-color: rgba(16, 185, 129, 0.1);
  color: #10b981;
}

.finance-badge.expense {
  background-color: rgba(239, 68, 68, 0.1);
  color: #ef4444;
}

.finance-date {
  font-size: 24rpx;
  color: #666;
}

/* 金额区域 */
.amount-section {
  padding: 40rpx 30rpx;
  background-color: #fafafa;
  border-bottom: 1rpx solid #f0f0f0;
  display: flex;
  flex-direction: column;
  gap: 16rpx;
  overflow: hidden;
  max-height: none;
}

.amount-wrapper {
  display: flex;
  align-items: baseline;
  width: 100%;
  overflow: visible;
  max-width: 100%;
}

.amount-currency {
  font-size: 36rpx;
  font-weight: 600;
  margin-right: 4rpx;
  color: #333;
  flex-shrink: 0;
}

.amount-value {
  font-size: 60rpx;
  font-weight: 700;
  line-height: 1.2;
  max-width: calc(100% - 20rpx);
  overflow: visible;
  text-overflow: clip;
  white-space: normal;
  word-break: break-all;
}

.amount-value.income {
  color: #10b981;
}

.amount-value.expense {
  color: #ef4444;
}

.amount-category {
  display: flex;
  margin-top: 6rpx;
  width: 100%;
  overflow: hidden;
}

.category-tag {
  display: inline-block;
  padding: 6rpx 16rpx;
  background-color: #f0f0f0;
  color: #666;
  font-size: 24rpx;
  border-radius: 6rpx;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 详细信息表格 */
.info-table {
  padding: 20rpx 0;
}

.table-row {
  display: flex;
  padding: 24rpx 30rpx;
  border-bottom: 1rpx solid #f5f5f5;
}

.table-row:last-child {
  border-bottom: none;
}

.table-cell-label {
  width: 180rpx;
  font-size: 28rpx;
  color: #666;
  flex-shrink: 0;
}

.table-cell-value {
  flex: 1;
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.table-cell-value.income-text {
  color: #10b981;
}

.table-cell-value.expense-text {
  color: #ef4444;
}

/* 备注区域 */
.finance-remarks {
  padding: 30rpx;
  border-top: 1rpx solid #f0f0f0;
}

.remarks-title {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 16rpx;
  font-weight: 500;
}

.remarks-content {
  font-size: 28rpx;
  color: #333;
  line-height: 1.6;
  white-space: pre-wrap;
  word-break: break-all;
}

/* 附件区域 */
.finance-attachments {
  padding: 30rpx;
  border-top: 1rpx solid #f0f0f0;
}

.attachments-title {
  margin-bottom: 20rpx;
  display: flex;
  align-items: center;
}

.title-text {
  font-size: 28rpx;
  color: #666;
  font-weight: 500;
}

.title-count {
  font-size: 24rpx;
  color: #999;
  margin-left: 8rpx;
}

.attachments-grid {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -10rpx;
}

.attachment-item {
  width: 30%;
  padding: 10rpx;
  box-sizing: border-box;
  position: relative;
}

.attachment-image {
  width: 100%;
  height: 180rpx;
  border-radius: 8rpx;
  background-color: #f0f0f0;
}

.attachment-mask {
  position: absolute;
  top: 10rpx;
  left: 10rpx;
  right: 10rpx;
  bottom: 10rpx;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0.7;
  background-color: rgba(0, 0, 0, 0.2);
}

.attachment-icon {
  color: #fff;
  font-size: 24rpx;
  text-align: center;
}

.icon-zoom {
  background-color: rgba(0, 0, 0, 0.4);
  padding: 8rpx 20rpx;
  border-radius: 30rpx;
  font-size: 22rpx;
}

/* 修改鼠标点击状态 */
.attachment-item:active .attachment-mask {
  opacity: 1;
  background-color: rgba(0, 0, 0, 0.4);
}

/* 底部操作区 */
.footer-bar {
  display: flex;
  gap: 20rpx;
  padding: 10rpx 10rpx 50rpx;
}

.footer-btn {
  flex: 1;
  height: 88rpx;
  line-height: 88rpx;
  text-align: center;
  border-radius: 44rpx;
  font-size: 30rpx;
  margin: 0;
}

.btn-return {
  background-color: #f5f5f5;
  color: #666;
  border: 1rpx solid #e0e0e0;
}

.btn-print {
  background-color: #1677ff;
  color: #fff;
}

.btn-print:active {
  background-color: #0e63cc;
}

/* 加载中 */
.loading-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background-color: #f7f7f7;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #1677ff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}

/* 无权限页面 */
.no-permission {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  background-color: #f7f7f7;
  padding: 0 60rpx;
  text-align: center;
}

.permission-icon {
  margin-bottom: 40rpx;
  background-color: #f5f5f5;
  width: 160rpx;
  height: 160rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.lock-icon {
  width: 80rpx;
  height: 80rpx;
}

.permission-title {
  font-size: 34rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
}

.permission-text {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
} 