

<view class="container">
  <!-- 加载状态 -->
  <view class="loading-container" wx:if="{{loading}}">
    <view class="loading-spinner"></view>
    <text class="loading-text">正在加载...</text>
  </view>

  <!-- 错误状态 -->
  <view class="error-container" wx:elif="{{loadError}}">
    <text class="error-icon">⚠️</text>
    <text class="error-title">加载失败</text>
    <text class="error-message">{{errorMsg || '无法获取档案信息'}}</text>
    <button class="retry-button" bindtap="loadDetail">重新加载</button>
  </view>

  <!-- 主要内容 -->
  <view class="main-content" wx:elif="{{villager}}">
    <view class="content-wrapper">

      <!-- 用户信息卡片 -->
      <view class="user-card">
        <view class="user-avatar" wx:if="{{villager.thumb}}">
          <image src="{{villager.thumb}}" mode="aspectFill" class="avatar-image"></image>
        </view>
        <view class="user-avatar placeholder" wx:else>
          <text class="avatar-placeholder">{{villager.title ? villager.title.charAt(0) : '?'}}</text>
        </view>

        <view class="user-info">
          <view class="user-name">{{villager.title || '未知'}}</view>
          <view class="user-id">ID: {{villager.id}}</view>
          <view class="user-status {{villager.isXiaohu ? 'inactive' : 'active'}}">
            {{villager.isXiaohu ? '已销户' : '有效'}}
          </view>
        </view>
      </view>

      <!-- 基本信息卡片 -->
      <view class="info-card">
        <view class="card-title">基本信息</view>
        <view class="info-list">
          <view class="info-item">
            <text class="info-label">姓名</text>
            <text class="info-value">{{villager.title || '未知'}}</text>
          </view>
          <view class="info-item">
            <text class="info-label">性别</text>
            <text class="info-value">{{villager.xingbie || '未知'}}</text>
          </view>
          <view class="info-item">
            <text class="info-label">身份证号</text>
            <text class="info-value">{{villager.sfzhm || '未知'}}</text>
          </view>
          <view class="info-item">
            <text class="info-label">联系电话</text>
            <text class="info-value">{{villager.shoujihaoma || '未知'}}</text>
          </view>
          <view class="info-item" wx:if="{{villager.hujidizhi}}">
            <text class="info-label">户籍地址</text>
            <text class="info-value">{{villager.hujidizhi}}</text>
          </view>
        </view>
      </view>

      <!-- 图片资料卡片 -->
      <view class="photo-status-card">
        <view class="card-title">图片资料</view>
        <view class="photo-status-list">
          <view class="photo-status-item">
            <view class="photo-status-info">
              <text class="photo-status-label">个人照片</text>
              <view class="photo-status-right">
                <view class="photo-status-indicator {{villager.thumb ? 'has-photo' : 'no-photo'}}">
                  <text class="status-text">{{villager.thumb ? '有' : '无'}}</text>
                  <text class="photo-count" wx:if="{{villager.thumb}}">(1张)</text>
                </view>
                <button class="photo-action-btn {{villager.thumb ? 'update' : 'upload'}}"
                        bindtap="goToPhotoPage" data-type="thumb">
                  {{villager.thumb ? '更新' : '上传'}}
                </button>
              </view>
            </view>
          </view>

          <view class="photo-status-item">
            <view class="photo-status-info">
              <text class="photo-status-label">身份证照片</text>
              <view class="photo-status-right">
                <view class="photo-status-indicator {{villager.grzpList && villager.grzpList.length > 0 ? 'has-photo' : 'no-photo'}}">
                  <text class="status-text">{{villager.grzpList && villager.grzpList.length > 0 ? '有' : '无'}}</text>
                  <text class="photo-count" wx:if="{{villager.grzpList && villager.grzpList.length > 0}}">({{villager.grzpList.length}}张)</text>
                </view>
                <button class="photo-action-btn {{villager.grzpList && villager.grzpList.length > 0 ? 'update' : 'upload'}}"
                        bindtap="goToPhotoPage" data-type="grzp">
                  {{villager.grzpList && villager.grzpList.length > 0 ? '更新' : '上传'}}
                </button>
              </view>
            </view>
          </view>

          <view class="photo-status-item">
            <view class="photo-status-info">
              <text class="photo-status-label">户口簿照片</text>
              <view class="photo-status-right">
                <view class="photo-status-indicator {{villager.qtzjzpList && villager.qtzjzpList.length > 0 ? 'has-photo' : 'no-photo'}}">
                  <text class="status-text">{{villager.qtzjzpList && villager.qtzjzpList.length > 0 ? '有' : '无'}}</text>
                  <text class="photo-count" wx:if="{{villager.qtzjzpList && villager.qtzjzpList.length > 0}}">({{villager.qtzjzpList.length}}张)</text>
                </view>
                <button class="photo-action-btn {{villager.qtzjzpList && villager.qtzjzpList.length > 0 ? 'update' : 'upload'}}"
                        bindtap="goToPhotoPage" data-type="qtzjzp">
                  {{villager.qtzjzpList && villager.qtzjzpList.length > 0 ? '更新' : '上传'}}
                </button>
              </view>
            </view>
          </view>

          <view class="photo-status-item">
            <view class="photo-status-info">
              <text class="photo-status-label">房屋照片</text>
              <view class="photo-status-right">
                <view class="photo-status-indicator {{villager.fwzpList && villager.fwzpList.length > 0 ? 'has-photo' : 'no-photo'}}">
                  <text class="status-text">{{villager.fwzpList && villager.fwzpList.length > 0 ? '有' : '无'}}</text>
                  <text class="photo-count" wx:if="{{villager.fwzpList && villager.fwzpList.length > 0}}">({{villager.fwzpList.length}}张)</text>
                </view>
                <button class="photo-action-btn {{villager.fwzpList && villager.fwzpList.length > 0 ? 'update' : 'upload'}}"
                        bindtap="goToPhotoPage" data-type="fwzp">
                  {{villager.fwzpList && villager.fwzpList.length > 0 ? '更新' : '上传'}}
                </button>
              </view>
            </view>
          </view>

          <view class="photo-status-item">
            <view class="photo-status-info">
              <text class="photo-status-label">改厕照片</text>
              <view class="photo-status-right">
                <view class="photo-status-indicator {{villager.gczpList && villager.gczpList.length > 0 ? 'has-photo' : 'no-photo'}}">
                  <text class="status-text">{{villager.gczpList && villager.gczpList.length > 0 ? '有' : '无'}}</text>
                  <text class="photo-count" wx:if="{{villager.gczpList && villager.gczpList.length > 0}}">({{villager.gczpList.length}}张)</text>
                </view>
                <button class="photo-action-btn {{villager.gczpList && villager.gczpList.length > 0 ? 'update' : 'upload'}}"
                        bindtap="goToPhotoPage" data-type="gczp">
                  {{villager.gczpList && villager.gczpList.length > 0 ? '更新' : '上传'}}
                </button>
              </view>
            </view>
          </view>

        </view>
      </view>

      <!-- 操作卡片 -->
      <view class="action-card">
        <view class="card-title">快速操作</view>
        <view class="action-grid">
          <view class="action-item primary" bindtap="viewDetails">
            <view class="action-icon">📋</view>
            <text class="action-text">查看详情</text>
          </view>
          <view class="action-item" bindtap="updateInfo">
            <view class="action-icon">✏️</view>
            <text class="action-text">编辑信息</text>
          </view>
          <view class="action-item" bindtap="updateImages">
            <view class="action-icon">📷</view>
            <text class="action-text">更新图片</text>
          </view>
        </view>
      </view>

      <!-- 时间信息卡片 -->
      <view class="time-card" wx:if="{{villager.inputtime || villager.updatetime}}">
        <view class="time-item" wx:if="{{villager.inputtime}}">
          <text class="time-label">创建时间</text>
          <text class="time-value">{{villager.inputtime}}</text>
        </view>
        <view class="time-item" wx:if="{{villager.updatetime}}">
          <text class="time-label">更新时间</text>
          <text class="time-value">{{villager.updatetime}}</text>
        </view>
      </view>

    </view>
  </view>

  <!-- 底部操作栏 -->
  <view class="bottom-bar" wx:if="{{villager}}">
    <button class="bottom-btn secondary" bindtap="goBack">返回</button>
    <button class="bottom-btn primary" open-type="share">分享</button>
  </view>
</view>