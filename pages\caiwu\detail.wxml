<view class="container" wx:if="{{isAdmin && !loading && recordData}}">
  <view class="page-header">
    <view class="header-title">财务凭证详情</view>
    <view class="header-id">凭证编号: {{recordData.id}}</view>
  </view>

  <view class="finance-card">
    <!-- 基本信息区域 -->
    <view class="finance-header">
      <view class="title-section">
        <view class="finance-title">{{recordData.title || '未命名记录'}}</view>
        <view class="finance-badge {{recordData.type == 'income' ? 'income' : 'expense'}}">
          {{recordData.type == 'income' ? '收入' : '支出'}}
        </view>
      </view>
      <view class="finance-date">{{recordData.update_time}}</view>
    </view>

    <!-- 金额信息区域 - 突出显示 -->
    <view class="amount-section">
      <view class="amount-wrapper">
        <view class="amount-currency">¥</view>
        <view class="amount-value {{recordData.type == 'income' ? 'income' : 'expense'}}">
          {{recordData.amount || '0.00'}}
        </view>
      </view>
      <view class="amount-category">
        <view class="category-tag">{{recordData.remark || '未分类'}}</view>
      </view>
    </view>

    <!-- 详细信息表格 -->
    <view class="info-table">
      <view class="table-row">
        <view class="table-cell-label">交易类型</view>
        <view class="table-cell-value {{recordData.type == 'income' ? 'income-text' : 'expense-text'}}">
          {{recordData.type == 'income' ? '收入' : '支出'}}
        </view>
      </view>
      
      <block wx:if="{{recordData.payment_method}}">
        <view class="table-row">
          <view class="table-cell-label">付款方式</view>
          <view class="table-cell-value">{{recordData.payment_method}}</view>
        </view>
      </block>
      
      <block wx:if="{{recordData.receipt_method}}">
        <view class="table-row">
          <view class="table-cell-label">收款方式</view>
          <view class="table-cell-value">{{recordData.receipt_method}}</view>
        </view>
      </block>
      
      <view class="table-row">
        <view class="table-cell-label">记账人</view>
        <view class="table-cell-value">{{recordData.creator_name || '未知'}}</view>
      </view>
      
      <view class="table-row">
        <view class="table-cell-label">创建时间</view>
        <view class="table-cell-value">{{recordData.create_time || '未知'}}</view>
      </view>
      
      <block wx:if="{{recordData.update_time}}">
        <view class="table-row">
          <view class="table-cell-label">业务日期</view>
          <view class="table-cell-value">{{recordData.update_time}}</view>
        </view>
      </block>
    </view>

    <!-- 备注信息 -->
    <block wx:if="{{recordData.description}}">
      <view class="finance-remarks">
        <view class="remarks-title">备注说明</view>
        <view class="remarks-content">{{recordData.description}}</view>
      </view>
    </block>

    <!-- 凭证图片 -->
    <block wx:if="{{recordData.pingzheng && recordData.pingzheng.length > 0}}">
      <view class="finance-attachments">
        <view class="attachments-title">
          <text class="title-text">凭证附件</text>
          <text class="title-count">({{recordData.pingzheng.length}}份)</text>
        </view>
        <view class="attachments-grid">
          <view class="attachment-item" wx:for="{{recordData.pingzheng}}" wx:key="id" bindtap="previewImage" data-url="{{item.file}}" data-urls="{{recordData.pingzhengUrls}}">
            <image 
              src="{{item.file}}" 
              class="attachment-image" 
              mode="aspectFill">
            </image>
            <view class="attachment-mask">
              <view class="attachment-icon">
                <view class="icon-zoom">点击查看</view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </block>
  </view>

  <!-- 页脚操作区 -->
  <view class="footer-bar">
    <button class="footer-btn btn-return" bindtap="goBack">返回列表</button>
    <button class="footer-btn btn-print" bindtap="printRecord">复制数据</button>
  </view>
</view>

<!-- 加载中 -->
<view class="loading-container" wx:if="{{isAdmin && loading}}">
  <view class="loading-spinner"></view>
  <view class="loading-text">正在加载凭证信息...</view>
</view>

<!-- 非管理员提示 -->
<view class="no-permission" wx:if="{{!isAdmin}}">
  <view class="permission-icon">
    <image src="/icons/lock.png" class="lock-icon"></image>
  </view>
  <view class="permission-title">权限受限</view>
  <view class="permission-text">只有管理员才能查看财务凭证详情</view>
</view> 