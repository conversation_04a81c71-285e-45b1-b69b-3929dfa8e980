<view class="container" wx:if="{{isAdmin}}">
  <view class="page-header">
    <view class="header-title">财务记录录入</view>
    <view class="header-subtitle">请填写以下必要信息，<text class="required-mark">*</text>为必填项</view>
  </view>

  <view class="form-container">
    <!-- 收支类型选择（顶部突出显示） -->
    <view class="form-section income-expense-selector">
      <view class="form-label required">收支类型</view>
      <view class="type-tabs">
        <view class="type-tab {{szxm == '1' ? 'active' : ''}}" bindtap="switchSzxm" data-type="1">
          <view class="tab-icon income-icon"></view>
        <text>收入</text>
      </view>
        <view class="type-tab {{szxm == '2' ? 'active' : ''}}" bindtap="switchSzxm" data-type="2">
          <view class="tab-icon expense-icon"></view>
        <text>支出</text>
        </view>
      </view>
    </view>

    <!-- 基本信息区域 -->
    <view class="form-section">
      <view class="section-title">基本信息</view>
      
      <view class="form-item">
        <view class="form-label required">项目名称</view>
        <input class="form-input" placeholder="请输入项目名称" value="{{title}}" bindinput="inputHandler" data-field="title" maxlength="30" />
      </view>
      
      <view class="form-row">
        <view class="form-item half-width">
          <view class="form-label required">记账日期</view>
          <picker mode="date" value="{{date}}" start="2021-01-01" end="2025-12-31" bindchange="bindDateChange">
            <view class="picker-view">
              <text class="picker-text">{{date || '请选择日期'}}</text>
              <view class="picker-icon"></view>
            </view>
          </picker>
        </view>
        
        <view class="form-item half-width">
          <view class="form-label required">金额</view>
          <view class="amount-input-container">
            <view class="currency-symbol">¥</view>
            <input class="form-input amount-input" type="digit" placeholder="请输入金额" value="{{amount}}" bindinput="inputHandler" data-field="amount" />
          </view>
        </view>
      </view>
      
      <view class="form-item">
        <view class="form-label">经手人</view>
        <input class="form-input" placeholder="请输入经手人" value="{{author}}" bindinput="inputHandler" data-field="author" maxlength="30" />
      </view>
      
    <view class="form-item">
        <view class="form-label">所属年度</view>
        <picker bindchange="bindCatidChange" value="{{yearIndex}}" range="{{yearOptions}}">
          <view class="picker-view">
            <text class="picker-text">{{yearOptions[yearIndex] || '请选择年度'}}</text>
            <view class="picker-icon"></view>
          </view>
        </picker>
      </view>
    </view>
    
    <!-- 收入专属字段 -->
    <view class="form-section" wx:if="{{szxm == '1'}}">
      <view class="section-title">收入信息</view>
      
      <view class="form-item">
        <view class="form-label">收入类别</view>
        <view class="option-grid">
          <view class="option-item {{shouruleibie == '1' ? 'active' : ''}}" bindtap="switchShouruleibie" data-type="1">
            <text>转移收入</text>
          </view>
          <view class="option-item {{shouruleibie == '2' ? 'active' : ''}}" bindtap="switchShouruleibie" data-type="2">
            <text>经营收入</text>
          </view>
          <view class="option-item {{shouruleibie == '3' ? 'active' : ''}}" bindtap="switchShouruleibie" data-type="3">
            <text>其它收入</text>
          </view>
          <view class="option-item {{shouruleibie == '4' ? 'active' : ''}}" bindtap="switchShouruleibie" data-type="4">
            <text>上级补助</text>
          </view>
          <view class="option-item {{shouruleibie == '5' ? 'active' : ''}}" bindtap="switchShouruleibie" data-type="5">
            <text>代收代付</text>
          </view>
        </view>
      </view>

      <view class="form-item">
        <view class="form-label">收款方式</view>
        <view class="option-row">
          <view class="option-item {{skfs == '1' ? 'active' : ''}}" bindtap="switchSkfs" data-type="1">
            <text>转公账</text>
          </view>
          <view class="option-item {{skfs == '2' ? 'active' : ''}}" bindtap="switchSkfs" data-type="2">
            <text>存现金</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 支出专属字段 -->
    <view class="form-section" wx:if="{{szxm == '2'}}">
      <view class="section-title">支出信息</view>
      
      <view class="form-item">
        <view class="form-label">支出类别</view>
        <view class="option-grid">
          <view class="option-item {{zhichuleibie == '1' ? 'active' : ''}}" bindtap="switchZhichuleibie" data-type="1">
            <text>办公支出</text>
          </view>
          <view class="option-item {{zhichuleibie == '2' ? 'active' : ''}}" bindtap="switchZhichuleibie" data-type="2">
            <text>项目支出</text>
          </view>
          <view class="option-item {{zhichuleibie == '3' ? 'active' : ''}}" bindtap="switchZhichuleibie" data-type="3">
            <text>代收代付</text>
          </view>
          <view class="option-item {{zhichuleibie == '4' ? 'active' : ''}}" bindtap="switchZhichuleibie" data-type="4">
            <text>专项支出</text>
          </view>
        </view>
      </view>

      <view class="form-item">
        <view class="form-label">付款方式</view>
        <view class="option-grid">
          <view class="option-item {{fkfs == '1' ? 'active' : ''}}" bindtap="switchFkfs" data-type="1">
            <text>转账</text>
          </view>
          <view class="option-item {{fkfs == '2' ? 'active' : ''}}" bindtap="switchFkfs" data-type="2">
            <text>现金</text>
          </view>
          <view class="option-item {{fkfs == '4' ? 'active' : ''}}" bindtap="switchFkfs" data-type="4">
            <text>预备金</text>
          </view>
          <view class="option-item {{fkfs == '5' ? 'active' : ''}}" bindtap="switchFkfs" data-type="5">
            <text>代收代付</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 备注信息区域 -->
    <view class="form-section">
      <view class="section-title">备注与附件</view>
      
      <!-- 凭证上传 -->
      <view class="form-item">
        <view class="form-label">凭证上传</view>
        <view class="upload-area" bindtap="chooseImage">
          <view class="upload-hint" wx:if="{{!pingzhengImages || pingzhengImages.length === 0}}">
            <view class="upload-icon"></view>
            <text>点击上传凭证图片</text>
            <text class="upload-desc">每张图片最大10MB，最多可上传9张</text>
          </view>
          
          <!-- 已上传图片预览 -->
          <view class="image-list" wx:if="{{pingzhengImages && pingzhengImages.length > 0}}">
            <view class="image-item" wx:for="{{pingzhengImages}}" wx:key="index">
              <image src="{{item.path}}" mode="aspectFill" bindtap="previewImage" data-index="{{index}}"></image>
              <view class="image-delete" catchtap="deleteImage" data-index="{{index}}">×</view>
            </view>
            
            <!-- 添加更多按钮 -->
            <view class="image-item add-more" bindtap="chooseImage" wx:if="{{pingzhengImages.length < 9}}">
              <view class="add-icon">+</view>
            </view>
          </view>
        </view>
      </view>
      
      <view class="form-item">
        <view class="form-label">描述</view>
        <textarea class="form-textarea" placeholder="请输入详细描述或备注信息" value="{{description}}" bindinput="inputHandler" data-field="description" maxlength="200" />
        <view class="textarea-counter">{{description.length}}/200</view>
      </view>
    </view>

    <!-- 提交按钮 -->
    <view class="action-bar">
      <button class="btn btn-cancel" bindtap="goBack">取消</button>
      <button class="btn btn-submit {{formValid ? '' : 'disabled'}}" bindtap="submitForm" disabled="{{!formValid}}">
        提交保存
        <view class="btn-arrow" wx:if="{{formValid}}"></view>
      </button>
    </view>
  </view>
</view>

<!-- 非管理员提示 -->
<view class="no-permission" wx:else>
  <image src="/icons/lock.png" class="lock-icon"></image>
  <view class="no-permission-text">只有管理员才能添加财务记录</view>
  <button class="btn btn-back" bindtap="goBack">返回</button>
</view> 