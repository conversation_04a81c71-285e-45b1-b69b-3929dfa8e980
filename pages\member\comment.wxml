<import src="../../wxParse/wxParse.wxml"/>
<view class="container">
  <scroll-view 
    class="content-list" 
    scroll-y="true" 
    enable-back-to-top="true"
    bindscrolltolower="loadMore"
    refresher-enabled="{{true}}"
    refresher-triggered="{{isRefreshing}}"
    bindrefresherrefresh="onRefresh"
  >
    <block wx:for="{{listData}}" wx:key="id">
      <view class="comment-card">
        <view class="comment-header">
          <view class="comment-meta">
            <view class="meta-item">
              <text class="meta-label">评分：</text>
              <text class="meta-value">{{item.avgsort}}</text>
            </view>
            <text class="meta-time">{{item.inputtime}}</text>
          </view>
        </view>
        
        <view class="comment-content">
          <template is="wxParse" data="{{wxParseData:item.content}}"/>
        </view>
        
        <navigator url="../workorder/show?id={{item.cid}}" hover-class="link-hover">
          <view class="article-link">
            <text class="link-label">工单：</text>
            <text class="link-text">{{item.title}}</text>
            <image class="link-icon" src="../../icons/arrow-right.png" mode="aspectFit"></image>
          </view>
        </navigator>
      </view>
    </block>

    <!-- 加载状态 -->
    <view class="loading-state" hidden="{{hidden}}">
      <view class="loading-content" wx:if="{{hasMore!='true'}}">
        <view class="loading-spinner"></view>
        <text class="loading-text">正在加载...</text>
      </view>
      <view class="no-more" wx:else>
        <view class="divider">
          <view class="divider-line"></view>
          <text class="no-more-text">没有更多数据了</text>
          <view class="divider-line"></view>
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <view class="empty-state" wx:if="{{listData.length === 0 && hidden}}">
      <image class="empty-icon" src="../../icons/empty-comment.png" mode="aspectFit"></image>
      <text class="empty-text">暂无评论记录</text>
    </view>
  </scroll-view>
</view>