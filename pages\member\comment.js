var app=getApp();//获取appid
var http_url = app.globalData.http_api + "s=member&app=comment&c=content&m=index&field=id&module=workorder";
http_url+= '&api_call_function=member_content_comment';
var WxParse = require('../../wxParse/wxParse.js');

Page({

    /**
     * 页面的初始数据
     */
    data: {

        listData:[],
        hidden: true,
        page: 1,
        hasMore:"false"
    },

    onLoad:function(options){
        var self=this;
        wx.request({
            url: http_url + '&api_auth_uid='+wx.getStorageSync('member_uid') + '&api_auth_code='+wx.getStorageSync('member_auth'),
            method: 'GET',
            success: function(res){
                if (res.data.code == 1) {
                    // 加强过滤条件，确保工单的完整性
                    var listData = res.data.data.filter(function(item) {
                        return item && 
                               item.id && 
                               item.content && 
                               item.content.trim() !== '' && 
                               item.status !== '0' && // 假设status为0表示已删除
                               item.title && // 确保有标题
                               item.title.trim() !== '';
                    });
                    
                    // 使用 wxParse 解析内容
                    for(var i = 0; i < listData.length; i++) {
                        WxParse.wxParse('content', 'html', listData[i].content, self, i);
                    }
                    self.setData({
                        listData: listData,
                        page: 1
                    });
                } else {
                    console.log(res.data.msg);
                    wx.showModal({
                      showCancel: false,
                      content: res.data.msg
                    })
                }
            }
        })
    },
    onReachBottom:function(){
        this.setData({hidden:false});
        var self=this;
        var pageid = self.data.page + 1;

        wx.request({
            url: http_url + '&api_auth_uid='+wx.getStorageSync('member_uid') + '&api_auth_code='+wx.getStorageSync('member_auth'),
            method: 'GET',
            data: {
                page: pageid
            },
            success: function(res){
                if (res.data.code == 1) {
                    if(res.data.data.length==0){
                        self.setData({
                            hasMore:"true",
                            hidden:false
                        });
                        setTimeout(function(){
                            self.setData({
                                hasMore:"false",
                                hidden:true
                            });
                        },900)
                    }else{
                        // 加强过滤条件，确保工单的完整性
                        var newData = res.data.data.filter(function(item) {
                            return item && 
                                   item.id && 
                                   item.content && 
                                   item.content.trim() !== '' && 
                                   item.status !== '0' && // 假设status为0表示已删除
                                   item.title && // 确保有标题
                                   item.title.trim() !== '';
                        });
                        
                        if(newData.length > 0) {
                            var currentData = self.data.listData;
                            for(var i = 0; i < newData.length; i++) {
                                WxParse.wxParse('content', 'html', newData[i].content, self, currentData.length + i);
                            }
                            // 合并数据
                            self.setData({
                                listData: currentData.concat(newData),
                                hidden:true,
                                page:pageid
                            });
                        } else {
                            self.setData({
                                hasMore:"true",
                                hidden:false
                            });
                            setTimeout(function(){
                                self.setData({
                                    hasMore:"false",
                                    hidden:true
                                });
                            },900)
                        }
                    }
                } else {
                    console.log(res.data.msg);
                    wx.showModal({
                      showCancel: false,
                      content: res.data.msg
                    })
                }
            }
        })
    }

})