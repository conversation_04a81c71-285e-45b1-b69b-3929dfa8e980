App({
  onError(err) {
    // 处理全局错误
    console.error('全局错误:', err);
  },
  
  onLaunch() {
    // 初始化时检查网络状态
    wx.getNetworkType({
      success: (res) => {
        if (res.networkType === 'none') {
          wx.showToast({
            title: '网络连接失败',
            icon: 'none'
          });
        }
      }
    });

    // 检查并创建必要的目录
    this.checkAndCreateDirectory();

    // 初始化后台数据获取
    this.initBackgroundFetch();
    
    // 清除可能存在的过期登录态，确保获取手机号时code有效
    this.cleanupLoginState();
  },

  // 清理登录状态，避免影响获取手机号
  cleanupLoginState: function() {
    try {
      // 获取上次登录的时间
      const lastLoginTime = wx.getStorageSync('last_login_time');
      const now = new Date().getTime();
      
      // 如果上次登录超过24小时，则清除登录态
      if (!lastLoginTime || (now - lastLoginTime > 24 * 60 * 60 * 1000)) {
        console.log('清除过期的登录态');
        wx.removeStorageSync('member_uid');
        wx.removeStorageSync('member_auth');
      }
      
      // 更新登录时间
      wx.setStorageSync('last_login_time', now);
    } catch (e) {
      console.error('清理登录状态失败', e);
    }
  },
  
  // 检查并创建必要的目录
  checkAndCreateDirectory: function() {
    const fs = wx.getFileSystemManager();
    try {
      // 尝试创建临时目录，用于存储各种临时文件
      fs.mkdir({
        dirPath: `${wx.env.USER_DATA_PATH}/temp`,
        recursive: true,
        success: () => {
          console.log('创建临时目录成功');
        },
        fail: (err) => {
          // 如果是目录已存在的错误，可以忽略
          if (err.errMsg.indexOf('file already exists') === -1) {
            console.error('创建临时目录失败', err);
          }
        }
      });
    } catch (e) {
      console.error('操作文件系统失败', e);
    }
  },

  // 初始化后台数据获取
  initBackgroundFetch() {
    try {
      // 先检查是否支持后台数据获取
      if (wx.getBackgroundFetchData) {
        wx.getBackgroundFetchData({
          fetchType: 'pre',
          success: (res) => {
            console.log('获取后台数据成功:', res);
            if (res.fetchedData) {
              this.handleBackgroundData(res.fetchedData);
            }
          },
          fail: (err) => {
            // 如果是数据未找到的错误，可以忽略
            if (err.errMsg && err.errMsg.indexOf('data not found') !== -1) {
              console.log('暂无后台数据');
            } else {
              console.error('获取后台数据失败:', err);
            }
          }
        });

        // 设置后台数据获取
        wx.setBackgroundFetchToken({
          token: 'workorder_background_fetch',
          success: () => {
            console.log('设置后台数据获取token成功');
          },
          fail: (err) => {
            console.error('设置后台数据获取token失败:', err);
          }
        });
      } else {
        console.log('当前环境不支持后台数据获取');
      }
    } catch (err) {
      console.error('初始化后台数据获取失败:', err);
    }
  },

  // 处理后台数据
  handleBackgroundData(data) {
    try {
      if (data && typeof data === 'string') {
        const parsedData = JSON.parse(data);
        // 处理解析后的数据
        console.log('解析后的后台数据:', parsedData);
        
        // 更新全局数据
        if (parsedData && typeof parsedData === 'object') {
          this.globalData = {
            ...this.globalData,
            ...parsedData
          };
        }
      }
    } catch (err) {
      console.error('处理后台数据失败:', err);
    }
  },

  showModel: function() {
    wx.showToast({
      title: '正在加载....',
      icon: 'loading',
      duration: 1000
    });
  },

  //请自行修改服务端API接口
  globalData: {
    appid: '2',  // 你的应用ID
    appsecret: 'PHPCMFAE9FFC56BDD07',  // 你的应用密钥
    http_api: "https://p.hnzbz.net/index.php?v=1&appid=2&appsecret=PHPCMFAE9FFC56BDD07&",
    token: "9b2e6d938624cd6069711cc9ae14f57c", // 上传文件需要的token
    needRefreshWorkorderList: false,  // 添加刷新工单列表的标记
    // 添加用户条款和隐私政策的路径
    terms_url: "/pages/common/terms",
    privacy_url: "/pages/common/privacy",
    // 微信小程序的真实appid和secret，用于服务端解密手机号
    wx_appid: "wxfd5ec730bb6cd9c1", // 已更新为实际的微信小程序appid
    wx_secret: "f348eac25dc97721f454012d3ff1df2b" // 已更新为实际的微信小程序secret
  }
})