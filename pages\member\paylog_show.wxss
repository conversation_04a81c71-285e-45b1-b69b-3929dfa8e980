.portlet {
  background: #fff;
  margin: 20rpx;
  border-radius: 8rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}

.portlet-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 2rpx solid #eee;
}

.caption {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.actions {
  color: #666;
}

.back-icon {
  font-size: 28rpx;
}

.back-icon::before {
  content: '←';
  margin-right: 10rpx;
}

.portlet-body {
  padding: 20rpx;
}

.form-group {
  display: flex;
  padding: 24rpx 20rpx;
  border-bottom: 2rpx solid #f5f5f5;
}

.form-label {
  width: 180rpx;
  color: #666;
  font-size: 28rpx;
}

.form-value {
  flex: 1;
  color: #333;
  font-size: 28rpx;
}

.link-text {
  color: #3598dc;
}

.income {
  color: #36c6d3;
  font-weight: bold;
}

.expense {
  color: #ed6b75;
  font-weight: bold;
}

.status-box {
  display: flex;
  align-items: center;
}

.status-paid {
  color: #36c6d3;
  background-color: rgba(54, 198, 211, 0.1);
  padding: 6rpx 20rpx;
  border-radius: 6rpx;
}

.status-unpaid {
  color: #ed6b75;
  background-color: rgba(237, 107, 117, 0.1);
  padding: 6rpx 20rpx;
  border-radius: 6rpx;
}

.pay-now {
  margin-left: 20rpx;
  background-color: #ed6b75;
  color: #fff;
  padding: 6rpx 20rpx;
  border-radius: 6rpx;
  font-size: 24rpx;
} 