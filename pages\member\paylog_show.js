var app = getApp();
var http_url = app.globalData.http_api + "s=member&app=pay&c=paylog&m=show";
http_url += '&api_call_function=';

Page({
  data: {
    detail: {}
  },

  onLoad: function(options) {
    if (!options.id) {
      wx.showToast({
        title: '参数错误',
        icon: 'none'
      });
      wx.navigateBack();
      return;
    }
    this.loadDetail(options.id);
  },

  loadDetail: function(id) {
    var member_uid = wx.getStorageSync('member_uid');
    var member_auth = wx.getStorageSync('member_auth');
    
    if (!member_uid || !member_auth) {
      wx.reLaunch({ 
        url: "../login/login",
        success: () => {
          wx.showToast({
            title: '登录信息已过期',
            icon: 'none'
          });
        }
      });
      return;
    }

    wx.showLoading({
      title: '加载中...',
    });

    var requestUrl = http_url + 
      '&api_auth_uid=' + member_uid + 
      '&api_auth_code=' + member_auth +
      '&id=' + id;

    wx.request({
      url: requestUrl,
      method: 'GET',
      success: (res) => {
        if (res.data.code == 1) {
          this.setData({
            detail: res.data.data
          });
        } else {
          wx.showToast({
            title: res.data.msg || '加载失败',
            icon: 'none'
          });
        }
      },
      fail: () => {
        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        });
      },
      complete: () => {
        wx.hideLoading();
      }
    });
  },

  // 返回上一页
  goBack: function() {
    wx.navigateBack();
  },

  // 打开链接
  openUrl: function(e) {
    const url = e.currentTarget.dataset.url;
    // 在小程序中打开网页需要使用 web-view 组件或复制链接
    wx.setClipboardData({
      data: url,
      success: () => {
        wx.showToast({
          title: '链接已复制',
          icon: 'success'
        });
      }
    });
  },

  // 处理支付
  handlePay: function(e) {
    const id = e.currentTarget.dataset.id;
    // 跳转到支付页面
    wx.navigateTo({
      url: '/pages/member/recharge?id=' + id
    });
  }
}); 