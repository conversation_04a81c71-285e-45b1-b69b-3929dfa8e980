const app = getApp();

Page({
  data: {
    cmList: [], // 村民档案列表
    page: 1,
    limit: 20,
    loading: false,
    hasMore: true,
    member: null, // 当前会员信息
    ssczName: "未设置", // 所属村组名称
    searchKeyword: '', // 搜索关键词
    isSearching: false, // 是否正在搜索中
    retryCount: 0, // 重试计数
    filterType: '', // 筛选类型
    filterField: 'hujishuxing', // 筛选字段，默认为户籍属性
    showFilterTag: false, // 是否显示筛选标签
    specialFilterHandled: false, // 是否已经处理过特殊的筛选请求
    totalFilterCount: 0, // 筛选结果的总数量
    hasFetchedTotal: false // 是否已经获取过总数量
  },

  onLoad: function(options) {
    // 输出调试信息
    console.log('list页面接收到的options:', options);

    // 检查是否有搜索关键词（来自首页搜索）
    if (options && options.search) {
      const searchKeyword = decodeURIComponent(options.search);
      this.setData({
        searchKeyword: searchKeyword,
        isSearching: true
      });
      console.log('设置搜索关键词:', searchKeyword);
    }

    // 设置筛选类型
    if (options && options.type) {
      // 如果有指定筛选字段，则使用指定字段
      const filterField = options.field || 'hujishuxing';
      const filterType = decodeURIComponent(options.type);

      this.setData({
        filterType: filterType,
        filterField: filterField,
        showFilterTag: true,
        hasFetchedTotal: false, // 重置总数获取状态
        totalFilterCount: 0 // 清空上次的总数
      });

      console.log('设置筛选类型:', filterType, '筛选字段:', filterField);

      // 使用专门的API处理筛选请求
      this.loadFilteredData(filterType, filterField);
      return; // 直接返回，不执行getMemberInfo
    }

    // 获取会员信息
    this.getMemberInfo();
  },

  onShow: function() {
    // 刷新数据
    this.setData({
      page: 1,
      cmList: [],
      hasMore: true
    });
    
    // 检查会员信息
    var member = wx.getStorageSync('member');
    var member_auth = wx.getStorageSync('member_auth');
    var member_uid = wx.getStorageSync('member_uid');
    
    if (member == "" || !member_auth || !member_uid) {
      wx.showModal({
        title: '提示',
        content: '请先登录',
        showCancel: false,
        success: function() {
          wx.reLaunch({ url: "../login/login" });
        }
      });
      return;
    }

    // 设置会员信息
    if (member) {
      this.setData({
        member: member
      });
      
      // 如果有所属村组，获取村组名称
      if (member.sscz) {
        this.loadSsczName(member.sscz);
      }
      
      // 加载村民档案列表
      this.loadCmdaList(this.data.isSearching);
    }
  },

  // 获取会员信息
  getMemberInfo: function() {
    var member = wx.getStorageSync('member');
    var member_auth = wx.getStorageSync('member_auth');
    var member_uid = wx.getStorageSync('member_uid');
    
    console.log('会员信息:', member);
    console.log('会员认证:', member_auth);
    console.log('会员ID:', member_uid);
    
    if (member == "" || !member_auth || !member_uid) {
      wx.showModal({
        title: '提示',
        content: '请先登录',
        showCancel: false,
        success: function() {
          wx.reLaunch({ url: "../login/login" });
        }
      });
      return;
    }
    
    // 检查是否为管理员
    if (!member.is_admin || member.is_admin <= 0) {
      wx.showModal({
        title: '提示',
        content: '此功能仅管理员可用',
        showCancel: false,
        success: function() {
          wx.navigateBack();
        }
      });
      return;
    }
    
    // 设置会员信息
    this.setData({
      member: member
    });
    
    // 如果有所属村组，获取村组名称
    if (member.sscz) {
      this.loadSsczName(member.sscz);
    }
    
    // 加载村民档案列表
    this.loadCmdaList(this.data.isSearching);
  },
  
  // 获取村组名称
  loadSsczName: function(ssczId) {
    if (!ssczId) {
      this.setData({ ssczName: '未设置' });
      return;
    }

    const ssczIdStr = String(ssczId);
    
    wx.request({
      url: app.globalData.http_api + 's=api&c=api&m=linkage_ld&mid=member&file=&code=sscz&parent_id=0',
      method: 'GET',
      success: (res) => {
        if (res.data && res.data.data && Array.isArray(res.data.data)) {
          const level1Item = res.data.data.find(item => String(item.region_id) === ssczIdStr);
          
          if (level1Item) {
            this.setData({ ssczName: level1Item.region_name });
            return;
          }
          
          this.checkLevel2Sscz(res.data.data, ssczIdStr);
        }
      },
      fail: (err) => {
        this.setData({ ssczName: ssczIdStr + ' (查询失败)' });
      }
    });
  },
  
  // 检查二级村组
  checkLevel2Sscz: function(level1Options, ssczIdStr) {
    this.sequentialCheckLevel2(level1Options, ssczIdStr, 0);
  },
  
  sequentialCheckLevel2: function(level1Options, ssczIdStr, index) {
    if (index >= level1Options.length) {
      this.setData({ ssczName: ssczIdStr });
      return;
    }
    
    const parent = level1Options[index];
    wx.request({
      url: app.globalData.http_api + 's=api&c=api&m=linkage_ld&mid=member&file=&code=sscz&parent_id=' + parent.region_id,
      method: 'GET',
      success: (res) => {
        if (res.data && res.data.data && Array.isArray(res.data.data)) {
          const level2Item = res.data.data.find(item => String(item.region_id) === ssczIdStr);
          
          if (level2Item) {
            this.setData({ 
              ssczName: parent.region_name + '' + level2Item.region_name 
            });
            return;
          }
        }
        
        this.sequentialCheckLevel2(level1Options, ssczIdStr, index + 1);
      },
      fail: (err) => {
        this.sequentialCheckLevel2(level1Options, ssczIdStr, index + 1);
      }
    });
  },

  // 加载特定筛选条件的数据（使用POST请求尝试获取更准确的结果）
  loadFilteredData: function(filterType, filterField) {
    this.setData({ loading: true });
    
    wx.showLoading({
      title: '筛选中...'
    });
    
    var member_uid = wx.getStorageSync('member_uid');
    var member_auth = wx.getStorageSync('member_auth');
    
    if (!member_uid || !member_auth) {
      wx.hideLoading();
      wx.showModal({
        title: '提示',
        content: '请先登录',
        showCancel: false,
        success: function() {
          wx.reLaunch({ url: "../login/login" });
        }
      });
      return;
    }
    
    // 获取会员信息
    var member = wx.getStorageSync('member');
    this.setData({ member: member });
    
    // 如果有所属村组，获取村组名称
    if (member && member.sscz) {
      this.loadSsczName(member.sscz);
    }
    
    // 构建API请求URL
    const requestUrl = app.globalData.http_api + 's=cmda&c=search&';
    
    // 构建POST数据，尝试各种可能的参数格式
    const postData = {
      api_call_function: 'module_list',
      appid: app.globalData.appid,
      appsecret: app.globalData.appsecret,
      more: 1,
      page: this.data.page,
      pagesize: this.data.limit,
      api_auth_uid: member_uid,
      api_auth_code: member_auth,
      // 1. 直接使用字段名作为参数
      [filterField]: filterType,
      // 2. 使用filter_前缀
      filter_type: filterType,
      filter_field: filterField,
      // 3. 使用field_前缀
      ['field_' + filterField]: filterType,
      // 4. 使用search_前缀
      ['search_' + filterField]: filterType,
      // 5. 添加特殊筛选标志
      is_filter: 1,
      filter_by: filterField,
      filter_value: filterType
    };
    
    console.log('筛选请求POST数据:', postData);
    
    wx.request({
      url: requestUrl,
      method: 'POST', // 使用POST尝试更可靠地传递参数
      data: postData,
      header: {
        'content-type': 'application/x-www-form-urlencoded'
      },
      success: res => {
        wx.hideLoading();
        console.log('筛选请求结果:', res.data);
        
        if (res.data.code == 1 && res.data.data && Array.isArray(res.data.data)) {
          const dataList = res.data.data;
          
          // 进一步筛选，确保数据正确
          const filteredList = dataList.filter(item => {
            // 宽松匹配：检查字段是否包含目标类型，或者字段值转为字符串后是否相等
            return item[filterField] === filterType || 
                  (item[filterField] && String(item[filterField]).includes(filterType)) ||
                  (item.hujishuxing === filterType) || // 直接检查hujishuxing字段
                  (item.hujishuxing && String(item.hujishuxing).includes(filterType));
          });
          
          console.log('前端二次筛选后的数据:', filteredList);
          
          // 如果前端筛选后有数据，使用前端筛选后的数据
          const finalList = filteredList.length > 0 ? filteredList : dataList;
          
          // 处理数据
          const processedList = finalList.map((item, index) => {
            return {
              ...item,
              title: item.title || '未命名',
              sfzhm: item.sfzhm || '无身份证信息',
              // 添加唯一key避免wx:key警告
              uniqueKey: `${item.id}_${this.data.page}_${index}`
            };
          });
          
          // 尝试从响应中获取总数量
          let totalCount = 0;
          
          // 检查不同可能的总数字段格式
          if (res.data.total !== undefined) {
            totalCount = parseInt(res.data.total) || 0;
          } else if (res.data.count !== undefined) {
            totalCount = parseInt(res.data.count) || 0;
          } else if (res.data.totalcount !== undefined) {
            totalCount = parseInt(res.data.totalcount) || 0;
          } else if (res.data.total_count !== undefined) {
            totalCount = parseInt(res.data.total_count) || 0;
          } else if (res.data.summary && res.data.summary.total_records !== undefined) {
            totalCount = parseInt(res.data.summary.total_records) || 0;
          }
          
          // 如果获取不到API返回的总数，则使用筛选后的长度
          if (totalCount === 0 && !this.data.hasFetchedTotal) {
            // 只有在还没有获取过总数的情况下才更新
            this.setData({
              totalFilterCount: filteredList.length
            });
          }
          
          console.log('筛选结果总数量:', totalCount);
          
          // 合并数据
          const concatList = this.data.page === 1 ? processedList : this.data.cmList.concat(processedList);
          
          // 构建要更新的数据对象
          const updateData = {
            cmList: concatList,
            loading: false,
            hasMore: filteredList.length >= this.data.limit, // 根据筛选后的长度判断是否还有更多
            page: this.data.page + 1,
            specialFilterHandled: true
          };
          
          // 如果是第一页且没有获取过总数，临时设置totalFilterCount
          if (this.data.page === 1 && !this.data.hasFetchedTotal) {
            // 这只是临时值，fetchFilterTotalCount稍后会获取更准确的值
            updateData.totalFilterCount = totalCount > 0 ? totalCount : filteredList.length;
          }
          
          this.setData(updateData);
          
          // 立即尝试获取更精确的总数
          this.fetchFilterTotalCount();
        } else {
          // 尝试使用普通的GET请求方式
          console.log('POST请求未返回有效数据，尝试GET请求');
          this.useGetRequestForFilter(filterType, filterField);
        }
      },
      fail: err => {
        console.error('筛选请求失败:', err);
        wx.hideLoading();
        
        // 尝试使用普通的GET请求方式
        console.log('POST请求失败，尝试GET请求');
        this.useGetRequestForFilter(filterType, filterField);
      }
    });
  },
  
  // 使用GET请求尝试筛选
  useGetRequestForFilter: function(filterType, filterField) {
    let requestUrl = app.globalData.http_api + 
      's=cmda&c=search&api_call_function=module_list' + 
      '&appid=' + app.globalData.appid + 
      '&appsecret=' + app.globalData.appsecret + 
      '&more=1' + 
      '&page=' + this.data.page + 
      '&pagesize=100' + // 增大页面大小，尝试一次获取更多数据
      '&api_auth_uid=' + wx.getStorageSync('member_uid') + 
      '&api_auth_code=' + wx.getStorageSync('member_auth');
    
    // 尝试各种参数格式
    requestUrl += `&${encodeURIComponent(filterField)}=${encodeURIComponent(filterType)}`;
    requestUrl += `&filter_type=${encodeURIComponent(filterType)}`;
    requestUrl += `&filter_field=${encodeURIComponent(filterField)}`;
    requestUrl += `&field_${encodeURIComponent(filterField)}=${encodeURIComponent(filterType)}`;
    requestUrl += `&search_${encodeURIComponent(filterField)}=${encodeURIComponent(filterType)}`;
    requestUrl += `&is_filter=1`;
    requestUrl += `&filter_by=${encodeURIComponent(filterField)}`;
    requestUrl += `&filter_value=${encodeURIComponent(filterType)}`;
    
    console.log('GET筛选请求URL:', requestUrl);
    
    wx.request({
      url: requestUrl,
      method: 'GET',
      header: {
        'content-type': 'application/json'
      },
      success: res => {
        wx.hideLoading();
        console.log('筛选请求结果:', res.data);
        
        if (res.data.code == 1 && res.data.data && Array.isArray(res.data.data)) {
          const dataList = res.data.data;
          
          // 尝试获取名为"total"或"count"的字段，查看API是否返回了总数
          console.log('API返回的原始数据结构:', Object.keys(res.data));
          
          // 前端筛选，确保数据正确
          const filteredList = dataList.filter(item => {
            // 宽松匹配：检查字段是否包含目标类型，或者字段值转为字符串后是否相等
            return item[filterField] === filterType || 
                  (item[filterField] && String(item[filterField]).includes(filterType)) ||
                  (item.hujishuxing === filterType) || // 直接检查hujishuxing字段
                  (item.hujishuxing && String(item.hujishuxing).includes(filterType));
          });
          
          console.log('筛选请求前端二次筛选后的数据:', filteredList);
          
          // 如果获取不到API返回的总数，则使用筛选后的长度
          if (res.data.total !== undefined) {
            this.setData({
              totalFilterCount: parseInt(res.data.total) || 0
            });
          } else if (res.data.count !== undefined) {
            this.setData({
              totalFilterCount: parseInt(res.data.count) || 0
            });
          } else if (res.data.totalcount !== undefined) {
            this.setData({
              totalFilterCount: parseInt(res.data.totalcount) || 0
            });
          } else if (res.data.total_count !== undefined) {
            this.setData({
              totalFilterCount: parseInt(res.data.total_count) || 0
            });
          } else if (res.data.summary && res.data.summary.total_records !== undefined) {
            this.setData({
              totalFilterCount: parseInt(res.data.summary.total_records) || 0
            });
          } else {
            this.setData({
              totalFilterCount: filteredList.length
            });
          }
          
          // 如果前端筛选后有数据，使用前端筛选后的数据
          const finalList = filteredList.length > 0 ? filteredList : dataList;
          
          // 处理数据
          const processedList = finalList.map((item, index) => {
            return {
              ...item,
              title: item.title || '未命名',
              sfzhm: item.sfzhm || '无身份证信息',
              // 添加唯一key避免wx:key警告
              uniqueKey: `${item.id}_${this.data.page}_${index}`
            };
          });
          
          this.setData({
            cmList: processedList,
            loading: false,
            hasMore: filteredList.length >= this.data.limit, // 根据筛选后的数量判断
            page: this.data.page + 1,
            specialFilterHandled: true
          });
          
          // 立即尝试获取更精确的总数
          this.fetchFilterTotalCount();
        } else if (this.data.filterType && this.data.page === 1) {
          // 第一次加载时，尝试获取总数量
          this.fetchFilterTotalCount();
        } else {
          // 如果所有尝试都失败了，显示错误
          this.setData({
            loading: false,
            hasMore: false
          });
          
          wx.showToast({
            title: '未找到匹配的村民档案',
            icon: 'none'
          });
        }
      },
      fail: err => {
        console.error('筛选请求失败:', err);
        wx.hideLoading();
        
        this.setData({
          loading: false,
          hasMore: false
        });
        
        wx.showToast({
          title: '获取筛选数据失败',
          icon: 'none'
        });
      }
    });
  },
  
  // 获取筛选结果的总数量（单独请求一次数据，尝试获取更完整的计数）
  fetchFilterTotalCount: function() {
    if (!this.data.filterType || this.data.hasFetchedTotal) return;
    
    // 如果已经获取过总数且总数大于0，不再重复请求
    if (this.data.hasFetchedTotal && this.data.totalFilterCount > 0) {
      console.log('已获取过总数:', this.data.totalFilterCount, '跳过重复请求');
      return;
    }
    
    // 如果当前页数是1，表示刚加载，避免重复请求
    if (this.data.loading) return;
    
    console.log('开始获取精确筛选总数...');
    
    const filterType = this.data.filterType;
    const filterField = this.data.filterField;
    
    // 构建API请求URL，使用较大的pagesize尝试获取所有数据
    let requestUrl = app.globalData.http_api + 
      's=cmda&c=search&api_call_function=module_list' + 
      '&appid=' + app.globalData.appid + 
      '&appsecret=' + app.globalData.appsecret + 
      '&more=1' + 
      '&page=1' + 
      '&pagesize=9999' + // 使用更大的页面大小尝试获取所有数据
      '&api_auth_uid=' + wx.getStorageSync('member_uid') + 
      '&api_auth_code=' + wx.getStorageSync('member_auth') +
      `&${encodeURIComponent(filterField)}=${encodeURIComponent(filterType)}` +
      `&filter_type=${encodeURIComponent(filterType)}` +
      `&filter_field=${encodeURIComponent(filterField)}` +
      `&field_${encodeURIComponent(filterField)}=${encodeURIComponent(filterType)}` +
      `&search_${encodeURIComponent(filterField)}=${encodeURIComponent(filterType)}` +
      `&is_filter=1` +
      `&filter_by=${encodeURIComponent(filterField)}` +
      `&filter_value=${encodeURIComponent(filterType)}`;
    
    console.log('获取筛选总数请求URL:', requestUrl);
    
    wx.request({
      url: requestUrl,
      method: 'GET',
      header: {
        'content-type': 'application/json'
      },
      success: res => {
        wx.hideLoading();
        console.log('筛选总数请求结果:', res.data);
        
        if (res.data.code == 1 && res.data.data && Array.isArray(res.data.data)) {
          const dataList = res.data.data;
          
          // 尝试从API获取总数，优先使用API提供的数据
          let totalCount = 0;
          if (res.data.total !== undefined) {
            totalCount = parseInt(res.data.total) || 0;
          } else if (res.data.count !== undefined) {
            totalCount = parseInt(res.data.count) || 0;
          } else if (res.data.totalcount !== undefined) {
            totalCount = parseInt(res.data.totalcount) || 0;
          } else if (res.data.total_count !== undefined) {
            totalCount = parseInt(res.data.total_count) || 0;
          } else if (res.data.summary && res.data.summary.total_records !== undefined) {
            totalCount = parseInt(res.data.summary.total_records) || 0;
          }
          
          // 如果API没有提供总数，则前端筛选计算
          if (totalCount <= 0) {
            // 前端筛选
            const filteredList = dataList.filter(item => {
              // 宽松匹配
              return item[filterField] === filterType || 
                     (item[filterField] && String(item[filterField]).includes(filterType)) ||
                     (item.hujishuxing === filterType) || 
                     (item.hujishuxing && String(item.hujishuxing).includes(filterType));
            });
            
            totalCount = filteredList.length;
          }
          
          console.log('筛选总数获取结果:', totalCount);
          
          // 更新总数和标记
          this.setData({
            totalFilterCount: totalCount,
            hasFetchedTotal: true
          });
        } else {
          // 如果API请求失败，至少显示当前加载的数量
          this.setData({
            totalFilterCount: this.data.cmList.length
          });
        }
      },
      fail: err => {
        wx.hideLoading();
        console.error('获取筛选总数失败:', err);
        
        // 如果请求失败，至少显示当前加载的数量
        this.setData({
          totalFilterCount: this.data.cmList.length
        });
      }
    });
  },

  // 搜索输入处理
  onSearchInput: function(e) {
    this.setData({
      searchKeyword: e.detail.value
    });
  },
  
  // 搜索确认
  onSearchConfirm: function() {
    const keyword = this.data.searchKeyword.trim();
    
    if (keyword === '') {
      // 如果关键词为空且当前在搜索状态，恢复到列表状态
      if (this.data.isSearching) {
        this.clearSearch();
      }
      return;
    }
    
    // 设置搜索状态并重置列表
    this.setData({
      isSearching: true,
      page: 1,
      cmList: [],
      hasMore: true
    });
    
    // 执行搜索
    this.loadCmdaList(true);
  },
  
  // 清除搜索
  clearSearch: function() {
    this.setData({
      searchKeyword: '',
      isSearching: false,
      page: 1,
      cmList: [],
      hasMore: true
    });
    
    // 重新加载全部数据
    this.loadCmdaList(false);
  },
  
  // 清除筛选
  clearFilter: function() {
    this.setData({
      filterType: '',
      filterField: 'hujishuxing', // 重置为默认值
      showFilterTag: false,
      page: 1,
      cmList: [],
      hasMore: true,
      hasFetchedTotal: false // 重置总数获取标记
    });
    
    // 重新加载全部数据
    this.loadCmdaList(this.data.isSearching);
  },
  
  // 查看详情
  viewDetail: function(e) {
    const id = e.currentTarget.dataset.id;
    if (!id) return;
    
    wx.navigateTo({
      url: './cmdashow?id=' + id
    });
  },
  
  // 添加新档案
  addNew: function() {
    wx.navigateTo({
      url: './cmdaedit'
    });
  },
  
  // 返回统计页面
  backToStats: function() {
    wx.redirectTo({
      url: './index'
    });
  },
  
  // 下拉刷新
  onPullDownRefresh: function() {
    this.setData({
      page: 1,
      cmList: [],
      hasMore: true
    });
    
    // 重新加载数据
    this.loadCmdaList(this.data.isSearching).then(() => {
      wx.stopPullDownRefresh();
    }).catch(() => {
      wx.stopPullDownRefresh();
    });
  },
  
  // 触底加载更多
  onReachBottom: function() {
    if (this.data.hasMore && !this.data.loading) {
      this.loadCmdaList(this.data.isSearching);
    }
  },
  
  // 加载村民档案列表
  loadCmdaList: function(isSearch = false, retryCount = 0) {
    if (this.data.loading || !this.data.hasMore) return;
    
    if (retryCount >= 3) {
      wx.showToast({
        title: '加载失败，请检查网络',
        icon: 'none'
      });
      return;
    }
    
    this.setData({ loading: true });
    
    wx.showLoading({
      title: '加载中...'
    });
    
    var member_uid = wx.getStorageSync('member_uid');
    var member_auth = wx.getStorageSync('member_auth');
    
    if (!member_uid || !member_auth) {
      wx.hideLoading();
      wx.showModal({
        title: '提示',
        content: '请先登录',
        showCancel: false,
        success: function() {
          wx.reLaunch({ url: "../login/login" });
        }
      });
      return;
    }
    
    try {
      // 如果当前是搜索状态，则覆盖传入的isSearch参数
      if (this.data.isSearching) {
        isSearch = true;
      }

      // 构建API请求URL
      let requestUrl = app.globalData.http_api + 
        's=cmda&c=search&api_call_function=module_list' + 
        '&appid=' + app.globalData.appid + 
        '&appsecret=' + app.globalData.appsecret + 
        '&more=1' + 
        '&page=' + this.data.page + 
        '&pagesize=' + this.data.limit + 
        '&api_auth_uid=' + member_uid + 
        '&api_auth_code=' + member_auth;
      
      // 如果是搜索模式，添加搜索关键词参数
      if (isSearch && this.data.searchKeyword) {
        requestUrl += `&keyword=${encodeURIComponent(this.data.searchKeyword)}`;
        console.log('执行搜索请求，关键词:', this.data.searchKeyword);
      }
      
      // 如果有筛选类型但不是通过专门的筛选函数处理的情况
      // 或者是第二页及之后的加载，也要加上筛选参数
      if (this.data.filterType) {
        // 特殊处理筛选请求，类似useGetRequestForFilter函数中的方式
        requestUrl += `&${encodeURIComponent(this.data.filterField)}=${encodeURIComponent(this.data.filterType)}`;
        requestUrl += `&filter_type=${encodeURIComponent(this.data.filterType)}`;
        requestUrl += `&filter_field=${encodeURIComponent(this.data.filterField)}`;
        requestUrl += `&field_${encodeURIComponent(this.data.filterField)}=${encodeURIComponent(this.data.filterType)}`;
        requestUrl += `&search_${encodeURIComponent(this.data.filterField)}=${encodeURIComponent(this.data.filterType)}`;
        console.log('加载更多时带上筛选参数，类型:', this.data.filterType, '字段:', this.data.filterField);
      }
      
      console.log('常规请求URL:', requestUrl);
      
      wx.request({
        url: requestUrl,
        method: 'GET',
        header: {
          'content-type': 'application/json'
        },
        success: res => {
          wx.hideLoading();
          console.log('获取村民档案列表结果:', res.data);
          
          if (res.data.code == 1 && res.data.data && Array.isArray(res.data.data)) {
            const newList = res.data.data;
            
            // 如果是筛选状态，则需要前端二次筛选
            let filteredList = newList;
            if (this.data.filterType) {
              // 对新加载的数据进行二次筛选
              filteredList = newList.filter(item => {
                // 宽松匹配：检查字段是否包含目标类型，或者字段值转为字符串后是否相等
                return item[this.data.filterField] === this.data.filterType || 
                      (item[this.data.filterField] && String(item[this.data.filterField]).includes(this.data.filterType)) ||
                      (item.hujishuxing === this.data.filterType) || // 直接检查hujishuxing字段
                      (item.hujishuxing && String(item.hujishuxing).includes(this.data.filterType));
              });
              console.log('加载更多时前端二次筛选，筛选前:', newList.length, '筛选后:', filteredList.length);
            }
            
            // 处理数据，确保必要的字段存在并添加唯一key
            const processedList = filteredList.map((item, index) => {
              return {
                ...item,
                title: item.title || '未命名',
                sfzhm: item.sfzhm || '无身份证信息',
                // 添加唯一key避免wx:key警告
                uniqueKey: `${item.id}_${this.data.page}_${index}`
              };
            });
            
            // 尝试从响应中获取总数量
            let totalCount = 0;
            
            // 检查不同可能的总数字段格式
            if (res.data.total !== undefined) {
              totalCount = parseInt(res.data.total) || 0;
            } else if (res.data.count !== undefined) {
              totalCount = parseInt(res.data.count) || 0;
            } else if (res.data.totalcount !== undefined) {
              totalCount = parseInt(res.data.totalcount) || 0;
            } else if (res.data.total_count !== undefined) {
              totalCount = parseInt(res.data.total_count) || 0;
            } else if (res.data.summary && res.data.summary.total_records !== undefined) {
              totalCount = parseInt(res.data.summary.total_records) || 0;
            }
            
            // 如果获取不到API返回的总数，则使用筛选后的长度
            if (totalCount === 0 && !this.data.hasFetchedTotal) {
              // 只有在还没有获取过总数的情况下才更新
              this.setData({
                totalFilterCount: filteredList.length
              });
            }
            
            console.log('筛选结果总数量:', totalCount);
            
            // 合并数据
            const concatList = this.data.page === 1 ? processedList : this.data.cmList.concat(processedList);
            
            // 构建要更新的数据对象
            const updateData = {
              cmList: concatList,
              loading: false,
              hasMore: filteredList.length >= this.data.limit, // 根据筛选后的长度判断是否还有更多
              page: this.data.page + 1,
              specialFilterHandled: true
            };
            
            // 如果是第一页且没有获取过总数，临时设置totalFilterCount
            if (this.data.page === 1 && !this.data.hasFetchedTotal) {
              // 这只是临时值，fetchFilterTotalCount稍后会获取更准确的值
              updateData.totalFilterCount = totalCount > 0 ? totalCount : filteredList.length;
            }
            
            this.setData(updateData);
          } else {
            // 接口返回错误或无数据
            this.setData({
              loading: false,
              hasMore: false
            });
            
            // 如果是第一次加载并失败，显示提示
            if (this.data.page === 1 && (!res.data.data || !Array.isArray(res.data.data) || res.data.data.length === 0)) {
              wx.showToast({
                title: isSearch ? '未找到匹配的村民档案' : '暂无村民档案数据',
                icon: 'none'
              });
            }
          }
        },
        fail: err => {
          console.error('请求村民档案列表失败:', err);
          wx.hideLoading();
          
          this.setData({
            loading: false
          });
          
          // 重试机制
          setTimeout(() => {
            this.loadCmdaList(isSearch, retryCount + 1);
          }, 1000);
        }
      });
    } catch (err) {
      console.error('加载村民档案列表异常:', err);
      wx.hideLoading();
      
      this.setData({
        loading: false
      });
      
      wx.showToast({
        title: '数据加载异常',
        icon: 'none'
      });
    }
  }
}); 