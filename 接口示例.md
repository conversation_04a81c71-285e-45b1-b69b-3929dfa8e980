# 工单小程序API接口调用示例文档

本文档提供工单小程序各API接口的具体调用示例代码和实现注意事项，方便开发者快速集成和使用这些接口。

## 目录

- [1. 基础配置](#1-基础配置)
- [2. 登录注册相关接口](#2-登录注册相关接口)
- [3. 工单管理相关接口](#3-工单管理相关接口)
- [4. 首页和统计相关接口](#4-首页和统计相关接口)
- [5. 会员中心相关接口](#5-会员中心相关接口)
- [6. 村民管理相关接口](#6-村民管理相关接口)
- [7. 公告通知相关接口](#7-公告通知相关接口)
- [8. 文件上传相关接口](#8-文件上传相关接口)
- [9. 积分兑换相关接口](#9-积分兑换相关接口)
- [10. 财务管理相关接口](#10-财务管理相关接口)
- [11. 微信小程序特有接口](#11-微信小程序特有接口)

## 1. 基础配置

### 1.1 全局配置

在应用初始化时设置全局配置：

```javascript
// app.js
App({
  globalData: {
    appid: '2',  // 应用ID
    appsecret: 'PHPCMFAE9FFC56BDD07',  // 应用密钥
    http_api: "https://p.hnzbz.net/index.php?v=1&appid=2&appsecret=PHPCMFAE9FFC56BDD07&",
    wx_appid: "wxfd5ec730bb6cd9c1", // 微信小程序appid
    wx_secret: "f348eac25dc97721f454012d3ff1df2b" // 微信小程序secret
  }
})
```

### 1.2 API请求封装

建议创建API请求的通用封装函数：

```javascript
// utils/api.js
const app = getApp();

// 构建认证字符串
function getAuthString() {
  const memberAuth = wx.getStorageSync('member_auth');
  const memberUid = wx.getStorageSync('member_uid');
  
  if (!memberAuth || !memberUid) {
    return '';
  }
  
  return `&api_auth_code=${memberAuth}&api_auth_uid=${memberUid}`;
}

// GET请求封装
function get(options) {
  const { path, data = {}, needAuth = false } = options;
  
  return new Promise((resolve, reject) => {
    let url = app.globalData.http_api + path;
    
    // 添加认证信息
    if (needAuth) {
      url += getAuthString();
    }
    
    wx.request({
      url,
      method: 'GET',
      data,
      success: (res) => {
        if (res.data.code === 1) {
          resolve(res.data);
        } else {
          reject(new Error(res.data.msg || '请求失败'));
        }
      },
      fail: reject
    });
  });
}

// POST请求封装
function post(options) {
  const { path, data = {}, needAuth = false, contentType = 'application/json' } = options;
  
  return new Promise((resolve, reject) => {
    let url = app.globalData.http_api + path;
    
    // 添加认证信息
    if (needAuth) {
      url += getAuthString();
    }
    
    // 处理不同的Content-Type
    let header = {};
    if (contentType === 'application/x-www-form-urlencoded') {
      header['content-type'] = 'application/x-www-form-urlencoded';
    } else {
      header['content-type'] = 'application/json';
    }
    
    wx.request({
      url,
      method: 'POST',
      data,
      header,
      success: (res) => {
        if (res.data.code === 1) {
          resolve(res.data);
        } else {
          reject(new Error(res.data.msg || '请求失败'));
        }
      },
      fail: reject
    });
  });
}

module.exports = {
  get,
  post
};
```

**注意事项**：
- 根据API需求，选择使用GET或POST方法
- 认证信息从本地缓存获取
- 处理不同的Content-Type，特别是表单提交
- 统一处理请求成功和失败的情况
- 提供Promise接口便于异步处理

## 2. 登录注册相关接口

### 2.1 用户登录

```javascript
const api = require('../../utils/api.js');

// 登录请求
function login(username, password) {
  // 表单验证
  if (!username || !password) {
    wx.showToast({
      title: '账号和密码不能为空',
      icon: 'none'
    });
    return Promise.reject(new Error('账号和密码不能为空'));
  }
  
  // 构建请求数据
  const data = {
    'is_ajax': 1,
    'data[username]': username,
    'data[password]': password
  };
  
  return api.post({
    path: 's=member&c=login',
    data,
    contentType: 'application/x-www-form-urlencoded'
  }).then(res => {
    // 保存登录信息
    if (res.data && res.data.member && res.data.auth) {
      wx.setStorageSync('member_uid', res.data.member.id);
      wx.setStorageSync('member_auth', res.data.auth);
      wx.setStorageSync('member', res.data.member);
      
      wx.showToast({
        title: '登录成功',
        icon: 'success'
      });
      
      return res.data;
    } else {
      throw new Error('登录响应数据不完整');
    }
  });
}

// 调用示例
Page({
  data: {
    username: '',
    password: '',
    loading: false
  },
  
  // 输入处理
  onInput(e) {
    const field = e.currentTarget.dataset.field;
    this.setData({
      [field]: e.detail.value
    });
  },
  
  // 登录按钮点击
  onLogin() {
    if (this.data.loading) return;
    
    this.setData({ loading: true });
    
    login(this.data.username, this.data.password)
      .then(() => {
        // 跳转到首页
        setTimeout(() => {
          wx.switchTab({
            url: '/pages/home/<USER>'
          });
        }, 1000);
      })
      .catch(err => {
        console.error('登录失败:', err);
        wx.showToast({
          title: err.message || '登录失败',
          icon: 'none'
        });
      })
      .finally(() => {
        this.setData({ loading: false });
      });
  }
});
```

**注意事项**：
- 使用表单格式提交数据
- 登录成功后保存三个关键信息：member_uid、member_auth和member对象
- 跳转到首页使用switchTab而非navigateTo
- 表单数据需要进行基础验证
- 处理登录失败情况

### 2.2 用户注册

```javascript
const api = require('../../utils/api.js');

// 获取村组数据
function getVillageGroups() {
  return api.get({
    path: 's=api&c=api&m=linkage_ld&mid=member&file=&code=sscz&parent_id=0'
  });
}

// 用户注册
function register(userData) {
  // 构建请求数据
  const data = {
    'is_ajax': 1,
    'is_protocol': 1,
    'data[username]': userData.username,
    'data[name]': userData.name,
    'data[password]': userData.password,
    'data[password2]': userData.password2,
    'data[email]': userData.email || '',
    'data[phone]': userData.phone,
    'data[sscz]': userData.sscz
  };
  
  return api.post({
    path: 's=member&c=register',
    data,
    contentType: 'application/x-www-form-urlencoded'
  });
}

// 调用示例
Page({
  data: {
    formData: {
      username: '',
      password: '',
      password2: '',
      name: '',
      email: '',
      phone: '',
      sscz: ''
    },
    villageGroups: [],
    loading: false
  },
  
  // 页面加载
  onLoad() {
    this.loadVillageGroups();
  },
  
  // 加载村组数据
  loadVillageGroups() {
    getVillageGroups()
      .then(res => {
        this.setData({ villageGroups: res.data || [] });
      })
      .catch(err => {
        console.error('获取村组数据失败:', err);
        wx.showToast({
          title: '获取村组数据失败',
          icon: 'none'
        });
      });
  },
  
  // 表单输入处理
  onInput(e) {
    const field = e.currentTarget.dataset.field;
    const value = e.detail.value;
    
    this.setData({
      [`formData.${field}`]: value
    });
  },
  
  // 村组选择
  onVillageChange(e) {
    this.setData({
      'formData.sscz': e.detail.value
    });
  },
  
  // 表单验证
  validateForm() {
    const { username, password, password2, name, phone, sscz } = this.data.formData;
    
    if (!username.trim()) {
      wx.showToast({ title: '请输入用户名', icon: 'none' });
      return false;
    }
    
    if (!password.trim()) {
      wx.showToast({ title: '请输入密码', icon: 'none' });
      return false;
    }
    
    if (password !== password2) {
      wx.showToast({ title: '两次密码输入不一致', icon: 'none' });
      return false;
    }
    
    if (!name.trim()) {
      wx.showToast({ title: '请输入真实姓名', icon: 'none' });
      return false;
    }
    
    if (!phone.trim()) {
      wx.showToast({ title: '请输入手机号码', icon: 'none' });
      return false;
    }
    
    if (!sscz) {
      wx.showToast({ title: '请选择所属村组', icon: 'none' });
      return false;
    }
    
    return true;
  },
  
  // 注册按钮点击
  onRegister() {
    if (this.data.loading) return;
    
    if (!this.validateForm()) {
      return;
    }
    
    this.setData({ loading: true });
    
    register(this.data.formData)
      .then(() => {
        wx.showToast({
          title: '注册成功',
          icon: 'success'
        });
        
        // 延迟跳转到登录页
        setTimeout(() => {
          wx.navigateTo({
            url: '/pages/login/login'
          });
        }, 1500);
      })
      .catch(err => {
        console.error('注册失败:', err);
        wx.showToast({
          title: err.message || '注册失败',
          icon: 'none'
        });
      })
      .finally(() => {
        this.setData({ loading: false });
      });
  }
});
```

**注意事项**：
- 页面加载时需要获取村组数据
- 注册表单需要进行详细验证，特别是密码一致性和手机号格式
- 注册成功后跳转到登录页而不是自动登录
- 使用表单格式提交数据
- 处理注册失败情况

### 2.3 微信登录

```javascript
const api = require('../../utils/api.js');

// 微信登录流程
function wxLogin() {
  // 显示加载提示
  wx.showLoading({ title: '登录中...' });
  
  // 获取微信登录凭证
  return new Promise((resolve, reject) => {
    wx.login({
      success: (loginRes) => {
        if (loginRes.code) {
          // 获取用户信息
          wx.getUserProfile({
            desc: '用于完善会员资料',
            success: (userRes) => {
              // 发送登录请求
              api.post({
                path: 's=weixin&c=member&m=xcx',
                data: {
                  json: userRes.rawData,
                  js_code: loginRes.code
                },
                contentType: 'application/x-www-form-urlencoded'
              })
              .then(resolve)
              .catch(reject)
              .finally(() => {
                wx.hideLoading();
              });
            },
            fail: (err) => {
              wx.hideLoading();
              reject(new Error('获取用户信息失败: ' + (err.errMsg || '')));
            }
          });
        } else {
          wx.hideLoading();
          reject(new Error('微信登录失败: ' + (loginRes.errMsg || '')));
        }
      },
      fail: (err) => {
        wx.hideLoading();
        reject(new Error('微信登录接口调用失败: ' + (err.errMsg || '')));
      }
    });
  });
}

// 调用示例
Page({
  data: {
    loading: false
  },
  
  // 微信登录按钮点击
  onWxLogin() {
    if (this.data.loading) return;
    
    this.setData({ loading: true });
    
    wxLogin()
      .then(res => {
        if (res.msg === 'login') {
          // 直接登录成功
          if (res.data && res.data.member && res.data.auth) {
            // 保存登录信息
            wx.setStorageSync('member_uid', res.data.member.id);
            wx.setStorageSync('member_auth', res.data.auth);
            wx.setStorageSync('member', res.data.member);
            
            wx.showToast({
              title: '登录成功',
              icon: 'success'
            });
            
            // 跳转到首页
            setTimeout(() => {
              wx.switchTab({
                url: '/pages/home/<USER>'
              });
            }, 1500);
          }
        } else {
          // 需要绑定账号，保存授权ID
          if (res.data && res.data.id) {
            wx.setStorageSync('oauth_id', res.data.id);
            
            wx.showModal({
              title: '提示',
              content: '您尚未绑定账号，请先绑定已有账号或注册新账号',
              confirmText: '去绑定',
              cancelText: '去注册',
              success: (modalRes) => {
                if (modalRes.confirm) {
                  // 跳转到绑定页面
                  wx.navigateTo({
                    url: '/pages/login/bang'
                  });
                } else {
                  // 跳转到注册页面
                  wx.navigateTo({
                    url: '/pages/login/register'
                  });
                }
              }
            });
          }
        }
      })
      .catch(err => {
        console.error('微信登录流程失败:', err);
        wx.showToast({
          title: err.message || '登录失败',
          icon: 'none'
        });
      })
      .finally(() => {
        this.setData({ loading: false });
      });
  }
});
```

**注意事项**：
- wx.getUserProfile必须在用户点击事件中调用
- 处理两种情况：直接登录成功和需要绑定账号
- 需要保存oauth_id用于后续账号绑定
- 给用户提供绑定已有账号和注册新账号两个选项
- 微信登录API可能随版本变化，需注意兼容性

### 2.4 微信账号绑定

```javascript
const api = require('../../utils/api.js');

// 绑定账号
function bindAccount(username, password, oauthId) {
  // 参数验证
  if (!username || !password || !oauthId) {
    return Promise.reject(new Error('参数不完整'));
  }
  
  // 构建请求数据
  const data = {
    'is_ajax': 1,
    'data[username]': username,
    'data[password]': password,
    'oid': oauthId
  };
  
  return api.post({
    path: 's=weixin&c=member&m=xcx_bang',
    data,
    contentType: 'application/x-www-form-urlencoded'
  });
}

// 调用示例
Page({
  data: {
    username: '',
    password: '',
    loading: false
  },
  
  // 页面加载检查
  onLoad() {
    const oauthId = wx.getStorageSync('oauth_id');
    if (!oauthId) {
      wx.showModal({
        title: '提示',
        content: '授权信息已失效，请重新授权',
        showCancel: false,
        success: () => {
          wx.navigateBack();
        }
      });
    }
  },
  
  // 输入处理
  onInput(e) {
    const field = e.currentTarget.dataset.field;
    this.setData({
      [field]: e.detail.value
    });
  },
  
  // 绑定按钮点击
  onBind() {
    if (this.data.loading) return;
    
    const { username, password } = this.data;
    
    if (!username.trim()) {
      wx.showToast({ title: '请输入用户名', icon: 'none' });
      return;
    }
    
    if (!password.trim()) {
      wx.showToast({ title: '请输入密码', icon: 'none' });
      return;
    }
    
    const oauthId = wx.getStorageSync('oauth_id');
    if (!oauthId) {
      wx.showToast({ title: '授权信息已失效', icon: 'none' });
      return;
    }
    
    this.setData({ loading: true });
    
    bindAccount(username, password, oauthId)
      .then(res => {
        if (res.data && res.data.member && res.data.auth) {
          // 保存登录信息
          wx.setStorageSync('member_uid', res.data.member.id);
          wx.setStorageSync('member_auth', res.data.auth);
          wx.setStorageSync('member', res.data.member);
          
          // 清除授权ID
          wx.removeStorageSync('oauth_id');
          
          wx.showToast({
            title: '绑定成功',
            icon: 'success'
          });
          
          // 延迟跳转到首页
          setTimeout(() => {
            wx.switchTab({
              url: '/pages/home/<USER>'
            });
          }, 1500);
        } else {
          throw new Error('绑定响应数据不完整');
        }
      })
      .catch(err => {
        console.error('绑定失败:', err);
        wx.showToast({
          title: err.message || '绑定失败',
          icon: 'none'
        });
      })
      .finally(() => {
        this.setData({ loading: false });
      });
  }
});
```

**注意事项**：
- 页面加载时检查是否有授权ID
- 绑定成功后需要清除授权ID
- 绑定成功后保存登录信息并跳转到首页
- 表单数据需要进行基本验证
- 处理绑定失败情况

## 3. 工单管理相关接口

### 3.1 工单列表

```javascript
const api = require('../../utils/api.js');

// 获取工单列表
function getWorkOrderList(options = {}) {
  const { page = 1, pageSize = 10, status = 0, keyword = '' } = options;
  
  let path = `s=workorder&c=search&api_call_function=module_list&pagesize=${pageSize}&page=${page}`;
  
  // 添加状态筛选
  if (status > 0) {
    path += `&jindu=${status}`;
  }
  
  // 添加关键词搜索
  if (keyword) {
    path += `&keyword=${encodeURIComponent(keyword)}`;
  }
  
  return api.get({ path });
}

// 调用示例
Page({
  data: {
    list: [],
    page: 1,
    pageSize: 10,
    currentStatus: 0,
    keyword: '',
    hasMore: true,
    loading: false,
    statusMap: {
      '1': { text: '待受理', class: 'status-pending' },
      '2': { text: '处理中', class: 'status-processing' },
      '3': { text: '已完毕', class: 'status-completed' },
      '4': { text: '已回复', class: 'status-replied' }
    }
  },
  
  // 页面初始化加载
  onLoad() {
    this.loadList();
  },
  
  // 加载工单列表
  loadList(isRefresh = false) {
    if (this.data.loading) return;
    
    const page = isRefresh ? 1 : this.data.page;
    
    this.setData({ loading: true });
    
    getWorkOrderList({
      page: page,
      pageSize: this.data.pageSize,
      status: this.data.currentStatus,
      keyword: this.data.keyword
    })
    .then(res => {
      const newList = res.data || [];
      
      // 处理数据，添加状态样式类
      const processedList = newList.map(item => {
        const status = this.data.statusMap[item.jindu] || { text: '未知状态', class: '' };
        return {
          ...item,
          statusText: status.text,
          statusClass: status.class,
          // 格式化日期
          formattedTime: this.formatTime(item.inputtime)
        };
      });
      
      this.setData({
        list: isRefresh ? processedList : [...this.data.list, ...processedList],
        page: page + 1,
        hasMore: newList.length >= this.data.pageSize
      });
    })
    .catch(err => {
      console.error('获取工单列表失败:', err);
      wx.showToast({
        title: '获取列表失败',
        icon: 'none'
      });
    })
    .finally(() => {
      this.setData({ loading: false });
      wx.stopPullDownRefresh();
    });
  },
  
  // 下拉刷新
  onPullDownRefresh() {
    this.loadList(true);
  },
  
  // 上拉加载更多
  onReachBottom() {
    if (this.data.hasMore && !this.data.loading) {
      this.loadList();
    }
  },
  
  // 状态筛选切换
  onStatusChange(e) {
    const status = parseInt(e.currentTarget.dataset.status || 0);
    
    this.setData({
      currentStatus: status,
      page: 1,
      list: [],
      hasMore: true
    });
    
    this.loadList(true);
  },
  
  // 搜索输入
  onSearchInput(e) {
    this.setData({ keyword: e.detail.value });
  },
  
  // 搜索按钮点击
  onSearch() {
    this.setData({
      page: 1,
      list: [],
      hasMore: true
    });
    
    this.loadList(true);
  },
  
  // 查看详情
  onViewDetail(e) {
    const id = e.currentTarget.dataset.id;
    if (!id) return;
    
    wx.navigateTo({
      url: `/pages/workorder/show?id=${id}`
    });
  },
  
  // 格式化时间
  formatTime(timestamp) {
    if (!timestamp) return '';
    
    const date = new Date(timestamp * 1000);
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    
    return `${year}-${month}-${day}`;
  }
});
```

**注意事项**：
- 支持按状态筛选和关键词搜索
- 实现了下拉刷新和上拉加载更多
- 处理工单状态，添加样式类
- 增加日期格式化
- 对非公开工单应该在列表中进行权限判断

### 3.2 工单详情

```javascript
const api = require('../../utils/api.js');

// 获取工单详情
function getWorkOrderDetail(id) {
  if (!id) {
    return Promise.reject(new Error('工单ID不能为空'));
  }
  
  const path = `s=workorder&c=show&api_call_function=module_show&id=${id}`;
  
  return api.get({
    path,
    needAuth: true
  });
}

// 获取工单评论列表
function getWorkOrderComments(id, page = 1) {
  if (!id) {
    return Promise.reject(new Error('工单ID不能为空'));
  }
  
  const path = `s=workorder&c=comment&page=${page}&api_call_function=module_comment_list&id=${id}`;
  
  return api.get({
    path,
    needAuth: true
  });
}

// 检查是否已收藏
function checkFavorite(id) {
  if (!id) {
    return Promise.reject(new Error('工单ID不能为空'));
  }
  
  return api.get({
    path: `s=api&app=workorder&c=module&m=is_favorite&id=${id}`,
    needAuth: true
  });
}

// 调用示例
Page({
  data: {
    id: '',
    detail: null,
    comments: [],
    loading: true,
    commentPage: 1,
    hasMoreComments: true,
    isFavorite: false,
    
    // 是否是管理员
    isAdmin: false,
    // 是否是工单创建者
    isCreator: false
  },
  
  // 页面加载
  onLoad(options) {
    if (options.id) {
      this.setData({ id: options.id });
      this.loadDetail();
      
      // 检查用户权限
      this.checkUserPermission();
    } else {
      wx.showToast({
        title: '参数错误',
        icon: 'none'
      });
      
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }
  },
  
  // 检查用户权限
  checkUserPermission() {
    const member = wx.getStorageSync('member');
    if (member) {
      // 检查是否是管理员
      this.setData({
        isAdmin: member.is_admin && parseInt(member.is_admin) > 0
      });
    }
  },
  
  // 加载工单详情
  loadDetail() {
    this.setData({ loading: true });
    
    // 并行请求详情、评论和收藏状态
    Promise.all([
      getWorkOrderDetail(this.data.id),
      getWorkOrderComments(this.data.id),
      checkFavorite(this.data.id).catch(() => ({ code: 0 })) // 默认未收藏
    ])
    .then(([detailRes, commentsRes, favoriteRes]) => {
      // 处理详情数据
      const detail = detailRes.data;
      
      // 检查是否是创建者
      const member = wx.getStorageSync('member');
      const isCreator = member && detail.uid === member.id;
      
      this.setData({
        detail,
        comments: commentsRes.data || [],
        hasMoreComments: (commentsRes.data || []).length >= 10,
        commentPage: 2,
        isFavorite: favoriteRes.code === 1,
        isCreator,
        loading: false
      });
      
      // 处理HTML内容，可使用wxParse等库
      this.parseContent(detail.content);
    })
    .catch(err => {
      console.error('加载工单详情失败:', err);
      wx.showToast({
        title: err.message || '加载失败',
        icon: 'none'
      });
      
      this.setData({ loading: false });
    });
  },
  
  // 处理HTML内容
  parseContent(content) {
    // 这里可以使用wxParse等第三方库处理HTML
    // WxParse.wxParse('content', 'html', content, this, 0);
  },
  
  // 加载更多评论
  loadMoreComments() {
    if (!this.data.hasMoreComments || this.data.loading) return;
    
    this.setData({ loading: true });
    
    getWorkOrderComments(this.data.id, this.data.commentPage)
      .then(res => {
        const newComments = res.data || [];
        
        this.setData({
          comments: [...this.data.comments, ...newComments],
          hasMoreComments: newComments.length >= 10,
          commentPage: this.data.commentPage + 1,
          loading: false
        });
      })
      .catch(err => {
        console.error('加载评论失败:', err);
        wx.showToast({
          title: '加载评论失败',
          icon: 'none'
        });
        
        this.setData({ loading: false });
      });
  },
  
  // 收藏/取消收藏工单
  toggleFavorite() {
    if (this.data.loading) return;
    
    this.setData({ loading: true });
    
    api.post({
      path: 's=api&app=workorder&c=module&m=favorite',
      data: {
        id: this.data.id,
        title: this.data.detail.title,
        url: `/pages/workorder/show?id=${this.data.id}`
      },
      needAuth: true
    })
    .then(res => {
      this.setData({
        isFavorite: !this.data.isFavorite,
        loading: false
      });
      
      wx.showToast({
        title: this.data.isFavorite ? '收藏成功' : '取消收藏',
        icon: 'success'
      });
    })
    .catch(err => {
      console.error('收藏操作失败:', err);
      wx.showToast({
        title: err.message || '操作失败',
        icon: 'none'
      });
      
      this.setData({ loading: false });
    });
  }
});
```

**注意事项**：
- 并行请求详情、评论和收藏状态，提高加载效率
- 检查用户权限，决定显示哪些操作按钮
- 需要处理HTML内容，建议使用wxParse等第三方库
- 实现评论分页加载
- 收藏功能是切换操作，同一接口可添加或取消

## 4. 首页和统计相关接口

### 4.1 工单统计数据

```javascript
const api = require('../../utils/api.js');

// 并行获取工单统计数据
async function getWorkOrderStats() {
  try {
    // 并行请求多个接口
    const [totalRes, pendingRes, processingRes, completedRes] = await Promise.all([
      api.get({ path: 's=httpapi&id=4' }),  // 总工单数
      api.get({ path: 's=httpapi&id=5' }),  // 待处理工单数
      api.get({ path: 's=httpapi&id=6' }),  // 处理中工单数
      api.get({ path: 's=httpapi&id=7' })   // 已完成工单数
    ]);
    
    return {
      total: totalRes.data.total || 0,
      pending: pendingRes.data.total || 0,
      processing: processingRes.data.total || 0,
      completed: completedRes.data.total || 0
    };
  } catch (error) {
    console.error('获取工单统计数据失败:', error);
    throw error;
  }
}

// 调用示例
Page({
  data: {
    stats: {
      total: 0,
      pending: 0,
      processing: 0,
      completed: 0
    },
    loading: true
  },
  
  // 页面加载
  onLoad() {
    this.loadStatistics();
    this.loadRecentWorkOrders();
    this.loadBanners();
  },
  
  // 加载统计数据
  async loadStatistics() {
    try {
      const stats = await getWorkOrderStats();
      
      this.setData({
        stats,
        loading: false
      });
    } catch (err) {
      console.error('加载统计数据失败:', err);
      wx.showToast({
        title: '加载统计数据失败',
        icon: 'none'
      });
      
      this.setData({ loading: false });
    }
  },
  
  // 错误重试
  onRetry() {
    this.setData({ loading: true });
    this.loadStatistics();
  }
});
```

**注意事项**：
- 使用Promise.all并行请求多个接口，提高加载效率
- 添加错误处理和重试机制
- 数据加载中应显示加载状态
- 无需登录认证即可访问这些接口

### 4.2 获取最新工单

```javascript
const api = require('../../utils/api.js');

// 获取最新工单
function getRecentWorkOrders(pageSize = 5) {
  return api.get({
    path: `s=workorder&c=search&pagesize=${pageSize}&page=1&api_call_function=module_list`
  });
}

// 调用示例
Page({
  data: {
    recentOrders: [],
    loadingRecent: true,
    statusMap: {
      '1': { text: '待受理', class: 'status-pending' },
      '2': { text: '处理中', class: 'status-processing' },
      '3': { text: '已完毕', class: 'status-completed' },
      '4': { text: '已回复', class: 'status-replied' }
    }
  },
  
  // 加载最新工单
  loadRecentWorkOrders() {
    this.setData({ loadingRecent: true });
    
    getRecentWorkOrders()
      .then(res => {
        const orders = res.data || [];
        
        // 处理数据，添加状态样式类
        const processedOrders = orders.map(item => {
          const status = this.data.statusMap[item.jindu] || { text: '未知状态', class: '' };
          return {
            ...item,
            statusText: status.text,
            statusClass: status.class,
            // 格式化日期
            formattedTime: this.formatTime(item.inputtime)
          };
        });
        
        this.setData({
          recentOrders: processedOrders,
          loadingRecent: false
        });
      })
      .catch(err => {
        console.error('获取最新工单失败:', err);
        wx.showToast({
          title: '获取最新工单失败',
          icon: 'none'
        });
        
        this.setData({ loadingRecent: false });
      });
  },
  
  // 查看工单详情
  onViewOrder(e) {
    const id = e.currentTarget.dataset.id;
    if (!id) return;
    
    wx.navigateTo({
      url: `/pages/workorder/show?id=${id}`
    });
  },
  
  // 格式化时间
  formatTime(timestamp) {
    if (!timestamp) return '';
    
    const date = new Date(timestamp * 1000);
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    
    return `${year}-${month}-${day}`;
  }
});
```

**注意事项**：
- 数据处理需要添加状态样式和日期格式化
- 点击工单跳转到详情页
- 无需登录认证即可访问

### 4.3 获取首页轮播图

```javascript
const api = require('../../utils/api.js');

// 获取轮播图
function getBanners() {
  return api.get({
    path: 's=httpapi&id=1'
  });
}

// 调用示例
Page({
  data: {
    banners: [],
    loadingBanners: true
  },
  
  // 加载轮播图
  loadBanners() {
    this.setData({ loadingBanners: true });
    
    getBanners()
      .then(res => {
        // 过滤掉空URL
        const banners = (res.data || []).filter(url => url && url.trim());
        
        this.setData({
          banners,
          loadingBanners: false
        });
      })
      .catch(err => {
        console.error('获取轮播图失败:', err);
        this.setData({ loadingBanners: false });
      });
  },
  
  // 轮播图点击事件
  onBannerTap(e) {
    const index = e.currentTarget.dataset.index;
    // 可以根据需要处理轮播图点击，如跳转到详情页等
  }
});
```

**注意事项**：
- 轮播图URL数组可能包含空值，需要过滤
- 使用wx.swiper组件展示轮播图
- 可以根据需求添加轮播图点击事件处理

### 4.4 完整的首页实现示例

```javascript
// 首页完整示例
Page({
  data: {
    // 统计数据
    stats: {
      total: 0,
      pending: 0,
      processing: 0,
      completed: 0
    },
    // 轮播图
    banners: [],
    // 最新工单
    recentOrders: [],
    // 加载状态
    loading: true,
    loadingBanners: true,
    loadingRecent: true,
    // 状态映射
    statusMap: {
      '1': { text: '待受理', class: 'status-pending' },
      '2': { text: '处理中', class: 'status-processing' },
      '3': { text: '已完毕', class: 'status-completed' },
      '4': { text: '已回复', class: 'status-replied' }
    }
  },
  
  onLoad() {
    // 并行加载所有数据
    this.loadAll();
  },
  
  onPullDownRefresh() {
    this.loadAll(true);
  },
  
  // 并行加载所有数据
  loadAll(isRefresh = false) {
    if (isRefresh) {
      wx.showNavigationBarLoading();
    }
    
    this.setData({
      loading: true,
      loadingBanners: true,
      loadingRecent: true
    });
    
    // 使用Promise.all并行加载所有数据
    Promise.all([
      this.getWorkOrderStats().catch(() => ({ total: 0, pending: 0, processing: 0, completed: 0 })),
      this.getBanners().catch(() => []),
      this.getRecentWorkOrders().catch(() => [])
    ])
    .then(([stats, banners, recentOrders]) => {
      this.setData({
        stats,
        banners,
        recentOrders,
        loading: false,
        loadingBanners: false,
        loadingRecent: false
      });
    })
    .finally(() => {
      if (isRefresh) {
        wx.stopPullDownRefresh();
        wx.hideNavigationBarLoading();
      }
    });
  },
  
  // 获取工单统计
  async getWorkOrderStats() {
    try {
      const [totalRes, pendingRes, processingRes, completedRes] = await Promise.all([
        api.get({ path: 's=httpapi&id=4' }),
        api.get({ path: 's=httpapi&id=5' }),
        api.get({ path: 's=httpapi&id=6' }),
        api.get({ path: 's=httpapi&id=7' })
      ]);
      
      return {
        total: totalRes.data.total || 0,
        pending: pendingRes.data.total || 0,
        processing: processingRes.data.total || 0,
        completed: completedRes.data.total || 0
      };
    } catch (error) {
      console.error('获取工单统计数据失败:', error);
      throw error;
    }
  },
  
  // 获取轮播图
  async getBanners() {
    const res = await api.get({ path: 's=httpapi&id=1' });
    // 过滤掉空URL
    return (res.data || []).filter(url => url && url.trim());
  },
  
  // 获取最新工单
  async getRecentWorkOrders() {
    const res = await api.get({
      path: 's=workorder&c=search&pagesize=5&page=1&api_call_function=module_list'
    });
    
    const orders = res.data || [];
    
    // 处理数据，添加状态样式类
    return orders.map(item => {
      const status = this.data.statusMap[item.jindu] || { text: '未知状态', class: '' };
      return {
        ...item,
        statusText: status.text,
        statusClass: status.class,
        formattedTime: this.formatTime(item.inputtime)
      };
    });
  },
  
  // 格式化时间
  formatTime(timestamp) {
    if (!timestamp) return '';
    
    const date = new Date(timestamp * 1000);
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    
    return `${year}-${month}-${day}`;
  },
  
  // 查看工单详情
  onViewOrder(e) {
    const id = e.currentTarget.dataset.id;
    if (!id) return;
    
    wx.navigateTo({
      url: `/pages/workorder/show?id=${id}`
    });
  }
});
```

**注意事项**：
- 使用Promise.all同时加载多个数据源
- 实现下拉刷新功能
- 每个请求都包含错误处理
- 添加加载状态指示器
- 对数据进行必要的处理和格式化

## 5. 会员中心相关接口

### 5.1 获取用户信息

```javascript
const api = require('../../utils/api.js');

// 获取用户信息
function getUserInfo() {
  const memberUid = wx.getStorageSync('member_uid');
  if (!memberUid) {
    return Promise.reject(new Error('用户未登录'));
  }
  
  return api.get({
    path: `s=member&c=api&m=userinfo&uid=${memberUid}`,
    needAuth: true
  });
}

// 调用示例
Page({
  data: {
    userInfo: null,
    loading: true
  },
  
  // 页面加载
  onLoad() {
    this.checkLoginStatus();
  },
  
  // 检查登录状态
  checkLoginStatus() {
    const memberUid = wx.getStorageSync('member_uid');
    const memberAuth = wx.getStorageSync('member_auth');
    
    if (!memberUid || !memberAuth) {
      // 未登录，跳转到登录页
      wx.navigateTo({
        url: '/pages/login/login'
      });
      return;
    }
    
    this.loadUserInfo();
  },
  
  // 加载用户信息
  loadUserInfo() {
    this.setData({ loading: true });
    
    getUserInfo()
      .then(res => {
        const userInfo = res.data;
        
        this.setData({
          userInfo,
          loading: false
        });
        
        // 更新本地缓存的用户信息
        wx.setStorageSync('member', userInfo);
      })
      .catch(err => {
        console.error('获取用户信息失败:', err);
        wx.showToast({
          title: '获取用户信息失败',
          icon: 'none'
        });
        
        this.setData({ loading: false });
        
        // 可能是登录态失效，清除登录信息
        if (err.message === '用户未登录') {
          wx.removeStorageSync('member_uid');
          wx.removeStorageSync('member_auth');
          wx.removeStorageSync('member');
          
          setTimeout(() => {
            wx.navigateTo({
              url: '/pages/login/login'
            });
          }, 1500);
        }
      });
  },
  
  // 退出登录
  onLogout() {
    wx.showModal({
      title: '提示',
      content: '确定要退出登录吗？',
      success: (res) => {
        if (res.confirm) {
          // 清除登录信息
          wx.removeStorageSync('member_uid');
          wx.removeStorageSync('member_auth');
          wx.removeStorageSync('member');
          
          wx.showToast({
            title: '已退出登录',
            icon: 'success'
          });
          
          // 跳转到登录页
          setTimeout(() => {
            wx.navigateTo({
              url: '/pages/login/login'
            });
          }, 1500);
        }
      }
    });
  }
});
```

**注意事项**：
- 页面加载时需要检查登录状态
- 获取用户信息失败可能是登录态失效
- 定期更新本地缓存的用户信息
- 退出登录需要清除所有本地缓存的用户信息

### 5.2 我的评论列表

```javascript
const api = require('../../utils/api.js');

// 获取我的评论列表
function getMyComments(page = 1, pageSize = 10) {
  return api.get({
    path: `s=member&app=comment&c=content&m=index&field=id&module=workorder&api_call_function=member_content_comment&page=${page}&pagesize=${pageSize}`,
    needAuth: true
  });
}

// 调用示例
Page({
  data: {
    comments: [],
    page: 1,
    pageSize: 10,
    hasMore: true,
    loading: false
  },
  
  // 页面加载
  onLoad() {
    this.loadComments(true);
  },
  
  // 加载评论列表
  loadComments(isRefresh = false) {
    if (this.data.loading) return;
    
    const page = isRefresh ? 1 : this.data.page;
    
    this.setData({ loading: true });
    
    getMyComments(page, this.data.pageSize)
      .then(res => {
        const newComments = res.data || [];
        
        // 格式化日期
        const processedComments = newComments.map(item => {
          return {
            ...item,
            formattedTime: this.formatTime(item.inputtime)
          };
        });
        
        this.setData({
          comments: isRefresh ? processedComments : [...this.data.comments, ...processedComments],
          page: page + 1,
          hasMore: newComments.length >= this.data.pageSize,
          loading: false
        });
      })
      .catch(err => {
        console.error('获取评论列表失败:', err);
        wx.showToast({
          title: '获取评论列表失败',
          icon: 'none'
        });
        
        this.setData({ loading: false });
      });
  },
  
  // 下拉刷新
  onPullDownRefresh() {
    this.loadComments(true);
    wx.stopPullDownRefresh();
  },
  
  // 上拉加载更多
  onReachBottom() {
    if (this.data.hasMore && !this.data.loading) {
      this.loadComments();
    }
  },
  
  // 查看工单详情
  onViewWorkOrder(e) {
    const id = e.currentTarget.dataset.id;
    if (!id) return;
    
    wx.navigateTo({
      url: `/pages/workorder/show?id=${id}`
    });
  },
  
  // 格式化时间
  formatTime(timestamp) {
    if (!timestamp) return '';
    
    const date = new Date(timestamp * 1000);
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    const hour = date.getHours().toString().padStart(2, '0');
    const minute = date.getMinutes().toString().padStart(2, '0');
    
    return `${year}-${month}-${day} ${hour}:${minute}`;
  }
});
```

**注意事项**：
- 实现下拉刷新和上拉加载更多功能
- 日期格式化显示更友好
- 处理评论为空的情况
- 支持点击跳转到对应工单

### 5.3 我的收藏列表

```javascript
const api = require('../../utils/api.js');

// 获取我的收藏列表
function getMyFavorites(page = 1, pageSize = 10) {
  return api.get({
    path: `s=member&app=favorite&c=content&m=index&field=id&module=workorder&api_call_function=member_content_favorite&page=${page}&pagesize=${pageSize}`,
    needAuth: true
  });
}

// 调用示例
Page({
  data: {
    favorites: [],
    page: 1,
    pageSize: 10,
    hasMore: true,
    loading: false
  },
  
  // 页面加载
  onLoad() {
    this.loadFavorites(true);
  },
  
  // 加载收藏列表
  loadFavorites(isRefresh = false) {
    if (this.data.loading) return;
    
    const page = isRefresh ? 1 : this.data.page;
    
    this.setData({ loading: true });
    
    getMyFavorites(page, this.data.pageSize)
      .then(res => {
        const newFavorites = res.data || [];
        
        // 格式化日期
        const processedFavorites = newFavorites.map(item => {
          return {
            ...item,
            formattedTime: this.formatTime(item.inputtime)
          };
        });
        
        this.setData({
          favorites: isRefresh ? processedFavorites : [...this.data.favorites, ...processedFavorites],
          page: page + 1,
          hasMore: newFavorites.length >= this.data.pageSize,
          loading: false
        });
      })
      .catch(err => {
        console.error('获取收藏列表失败:', err);
        wx.showToast({
          title: '获取收藏列表失败',
          icon: 'none'
        });
        
        this.setData({ loading: false });
      });
  },
  
  // 下拉刷新
  onPullDownRefresh() {
    this.loadFavorites(true);
    wx.stopPullDownRefresh();
  },
  
  // 上拉加载更多
  onReachBottom() {
    if (this.data.hasMore && !this.data.loading) {
      this.loadFavorites();
    }
  },
  
  // 查看收藏详情
  onViewItem(e) {
    const url = e.currentTarget.dataset.url;
    if (!url) return;
    
    // 解析URL参数
    let pageUrl = url;
    if (url.startsWith('/')) {
      // 微信小程序页面路由
      wx.navigateTo({ url });
    } else if (url.includes('id=')) {
      // 处理工单URL
      const matches = url.match(/id=(\d+)/);
      if (matches && matches[1]) {
        wx.navigateTo({
          url: `/pages/workorder/show?id=${matches[1]}`
        });
      }
    }
  },
  
  // 取消收藏
  onRemoveFavorite(e) {
    const id = e.currentTarget.dataset.id;
    if (!id) return;
    
    wx.showModal({
      title: '提示',
      content: '确定要取消收藏吗？',
      success: (res) => {
        if (res.confirm) {
          this.removeFavorite(id);
        }
      }
    });
  },
  
  // 执行取消收藏
  removeFavorite(id) {
    api.post({
      path: 's=api&app=workorder&c=module&m=favorite',
      data: { id },
      needAuth: true
    })
    .then(() => {
      // 从列表中移除
      const favorites = this.data.favorites.filter(item => item.id !== id);
      
      this.setData({ favorites });
      
      wx.showToast({
        title: '已取消收藏',
        icon: 'success'
      });
    })
    .catch(err => {
      console.error('取消收藏失败:', err);
      wx.showToast({
        title: '取消收藏失败',
        icon: 'none'
      });
    });
  },
  
  // 格式化时间
  formatTime(timestamp) {
    if (!timestamp) return '';
    
    const date = new Date(timestamp * 1000);
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    
    return `${year}-${month}-${day}`;
  }
});
```

**注意事项**：
- 实现下拉刷新和上拉加载更多功能
- 支持取消收藏操作
- 处理不同类型的URL跳转
- 空列表时显示提示信息

### 5.4 我的工单列表

```javascript
const api = require('../../utils/api.js');

// 获取我的工单列表
function getMyWorkOrders(page = 1, pageSize = 10) {
  return api.get({
    path: `s=member&app=workorder&c=content&m=index&field=id&page=${page}&pagesize=${pageSize}`,
    needAuth: true
  });
}

// 调用示例
Page({
  data: {
    orders: [],
    page: 1,
    pageSize: 10,
    hasMore: true,
    loading: false,
    statusMap: {
      '1': { text: '待受理', class: 'status-pending' },
      '2': { text: '处理中', class: 'status-processing' },
      '3': { text: '已完毕', class: 'status-completed' },
      '4': { text: '已回复', class: 'status-replied' }
    }
  },
  
  // 页面加载
  onLoad() {
    this.loadOrders(true);
  },
  
  // 加载工单列表
  loadOrders(isRefresh = false) {
    if (this.data.loading) return;
    
    const page = isRefresh ? 1 : this.data.page;
    
    this.setData({ loading: true });
    
    getMyWorkOrders(page, this.data.pageSize)
      .then(res => {
        const newOrders = res.data || [];
        
        // 处理数据，添加状态样式类
        const processedOrders = newOrders.map(item => {
          const status = this.data.statusMap[item.jindu] || { text: '未知状态', class: '' };
          return {
            ...item,
            statusText: status.text,
            statusClass: status.class,
            formattedTime: this.formatTime(item.inputtime)
          };
        });
        
        this.setData({
          orders: isRefresh ? processedOrders : [...this.data.orders, ...processedOrders],
          page: page + 1,
          hasMore: newOrders.length >= this.data.pageSize,
          loading: false
        });
      })
      .catch(err => {
        console.error('获取工单列表失败:', err);
        wx.showToast({
          title: '获取工单列表失败',
          icon: 'none'
        });
        
        this.setData({ loading: false });
      });
  },
  
  // 下拉刷新
  onPullDownRefresh() {
    this.loadOrders(true);
    wx.stopPullDownRefresh();
  },
  
  // 上拉加载更多
  onReachBottom() {
    if (this.data.hasMore && !this.data.loading) {
      this.loadOrders();
    }
  },
  
  // 查看工单详情
  onViewOrder(e) {
    const id = e.currentTarget.dataset.id;
    if (!id) return;
    
    wx.navigateTo({
      url: `/pages/workorder/show?id=${id}`
    });
  },
  
  // 格式化时间
  formatTime(timestamp) {
    if (!timestamp) return '';
    
    const date = new Date(timestamp * 1000);
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    
    return `${year}-${month}-${day}`;
  }
});
```

**注意事项**：
- 实现下拉刷新和上拉加载更多功能
- 处理不同状态工单的显示样式
- 空列表时显示提示信息
- 需要用户登录认证才能访问

### 5.5 会员消息通知列表

```javascript
const api = require('../../utils/api.js');

// 获取消息通知列表
function getNotifications(options = {}) {
  const { page = 1, pageSize = 10, type = 0 } = options;
  
  let path = `s=member&app=notice&c=home&m=index&page=${page}&pagesize=${pageSize}&api_call_function=member_content_comment`;
  
  // 添加通知类型筛选
  if (type > 0) {
    path += `&tid=${type}`;
  }
  
  return api.get({
    path,
    needAuth: true
  });
}

// 标记消息已读
function markAsRead(noticeId) {
  return api.post({
    path: 's=member&app=notice&c=home&m=read',
    data: { id: noticeId },
    needAuth: true
  });
}

// 调用示例
Page({
  data: {
    notifications: [],
    page: 1,
    pageSize: 10,
    currentType: 0,
    hasMore: true,
    loading: false,
    typeOptions: [
      { id: 0, name: '全部' },
      { id: 1, name: '系统通知' },
      { id: 2, name: '工单回复' }
      // 可能会有更多类型
    ]
  },
  
  // 页面加载
  onLoad() {
    this.loadNotifications(true);
  },
  
  // 加载通知列表
  loadNotifications(isRefresh = false) {
    if (this.data.loading) return;
    
    const page = isRefresh ? 1 : this.data.page;
    
    this.setData({ loading: true });
    
    getNotifications({
      page,
      pageSize: this.data.pageSize,
      type: this.data.currentType
    })
    .then(res => {
      const newNotifications = res.data || [];
      
      // 处理数据，添加格式化日期
      const processedNotifications = newNotifications.map(item => {
        return {
          ...item,
          formattedTime: this.formatTime(item.inputtime)
        };
      });
      
      this.setData({
        notifications: isRefresh ? processedNotifications : [...this.data.notifications, ...processedNotifications],
        page: page + 1,
        hasMore: newNotifications.length >= this.data.pageSize,
        loading: false
      });
    })
    .catch(err => {
      console.error('获取通知列表失败:', err);
      wx.showToast({
        title: '获取通知列表失败',
        icon: 'none'
      });
      
      this.setData({ loading: false });
    });
  },
  
  // 下拉刷新
  onPullDownRefresh() {
    this.loadNotifications(true);
    wx.stopPullDownRefresh();
  },
  
  // 上拉加载更多
  onReachBottom() {
    if (this.data.hasMore && !this.data.loading) {
      this.loadNotifications();
    }
  },
  
  // 类型筛选切换
  onTypeChange(e) {
    const typeId = parseInt(e.currentTarget.dataset.type || 0);
    
    this.setData({
      currentType: typeId,
      page: 1,
      notifications: [],
      hasMore: true
    });
    
    this.loadNotifications(true);
  },
  
  // 查看通知详情
  onViewNotification(e) {
    const notice = e.currentTarget.dataset.notice;
    if (!notice) return;
    
    // 标记为已读
    if (notice.is_read === '0') {
      markAsRead(notice.id)
        .then(() => {
          // 更新本地通知状态
          const notifications = this.data.notifications.map(item => {
            if (item.id === notice.id) {
              return { ...item, is_read: '1' };
            }
            return item;
          });
          
          this.setData({ notifications });
        })
        .catch(err => {
          console.error('标记已读失败:', err);
        });
    }
    
    // 显示详情对话框
    wx.showModal({
      title: notice.title,
      content: notice.content,
      showCancel: false
    });
  },
  
  // 格式化时间
  formatTime(timestamp) {
    if (!timestamp) return '';
    
    const date = new Date(timestamp * 1000);
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    const hour = date.getHours().toString().padStart(2, '0');
    const minute = date.getMinutes().toString().padStart(2, '0');
    
    return `${year}-${month}-${day} ${hour}:${minute}`;
  }
});
```

**注意事项**：
- 支持按通知类型筛选
- 查看通知时自动标记为已读
- 已读和未读状态需要不同的样式显示
- 支持下拉刷新和上拉加载更多
- 需要用户登录认证才能访问

## 6. 村民管理相关接口

## 7. 公告通知相关接口

## 8. 文件上传相关接口

## 9. 积分兑换相关接口

## 10. 财务管理相关接口

## 11. 微信小程序特有接口 