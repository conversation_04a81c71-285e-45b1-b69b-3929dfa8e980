var WxParse = require('../../wxParse/wxParse.js');

const app = getApp();

Page({
  data: {
    villager: null, // 村民详情
    loading: true, // 加载状态
    loadError: false, // 加载错误
    errorMsg: '', // 错误信息
    hasImages: false, // 是否有图片
    currentImageIndex: 0, // 当前图片索引
    
    // 评论相关
    commentList: [], // 评论列表
    commentPage: 1, // 评论分页
    commentLimit: 10, // 每页评论数量
    hasMore: true, // 是否有更多评论
    isLoading: false, // 是否正在加载评论
    isCommentBarShow: false, // 是否显示评论输入框
    commentText: '', // 评论内容
    tempImagePaths: [], // 临时图片路径
    member: null, // 当前用户信息
    activePhotoTab: 'grzp', // 默认选中身份证件照片标签
  },

  onLoad: function(options) {
    // 检查传入的ID参数
    if (!options.id) {
      this.setData({
        loading: false,
        loadError: true,
        errorMsg: '参数错误，缺少村民ID'
      });
      return;
    }
    
    this.villagerId = options.id;
    
    // 获取会员信息
    var member = wx.getStorageSync('member');
    if (member) {
      this.setData({ member: member });
    }
    
    // 加载村民详情
    this.loadDetail();
    
    // 加载评论
    this.loadComments();
  },
  
  // 加载村民详情
  loadDetail: function() {
    this.setData({
      loading: true,
      loadError: false,
      errorMsg: ''
    });
    
    // 获取验证信息
    var member_uid = wx.getStorageSync('member_uid');
    var member_auth = wx.getStorageSync('member_auth');
    
    if (!member_uid || !member_auth) {
      this.setData({
        loading: false,
        loadError: true,
        errorMsg: '请先登录'
      });
      return;
    }
    
    // 构建API请求URL
    const requestUrl = app.globalData.http_api + 
      's=cmda&c=show&id=' + this.villagerId + 
      '&api_call_function=module_show' + 
      '&api_auth_uid=' + member_uid + 
      '&api_auth_code=' + member_auth;
    
    console.log('请求详情URL:', requestUrl);
    
    wx.showLoading({
      title: '加载中...'
    });
    
    wx.request({
      url: requestUrl,
      method: 'GET',
      header: {
        'content-type': 'application/json'
      },
      success: res => {
        console.log('获取村民详情结果:', res.data);
        
        if (res.data.code == 1 && res.data.data) {
          // 处理村民详情数据
          const villagerData = res.data.data;
          
          // 打印原始数据中的thumb字段
          console.log('原始数据中的thumb字段:', villagerData.thumb);
          
          // 提取可能的其他信息字段
          const otherInfo = [];
          const mainFields = ['id', 'title', 'sfzhm', 'hujidizhi', 'hujishuxing', 'shoujihaoma', 
                             'updatetime', 'inputtime', 'grzp', 'fwzp', 'qtzjzp', 'gczp', 
                             'hits', 'comments', 'xingbie', 'fankui', 'thumb'];
          
          // 预处理处理图片数据
          let allImages = [];
          
          // 处理缩略图
          if (villagerData.thumb) {
            // 如果thumb是对象，则取其file属性
            if (typeof villagerData.thumb === 'object' && villagerData.thumb.file) {
              villagerData.thumb = villagerData.thumb.file;
            } else if (Array.isArray(villagerData.thumb) && villagerData.thumb.length > 0) {
              // 如果是数组，取第一个元素
              if (typeof villagerData.thumb[0] === 'object') {
                villagerData.thumb = villagerData.thumb[0].file;
              } else {
                villagerData.thumb = villagerData.thumb[0];
              }
            }
            console.log('处理后的thumb字段:', villagerData.thumb);
          }
          
          // 身份证件照片
          if (villagerData.grzp && Array.isArray(villagerData.grzp) && villagerData.grzp.length > 0) {
            villagerData.grzpList = villagerData.grzp.map(item => item.file);
            allImages = allImages.concat(villagerData.grzpList);
          }
          
          // 房屋照片
          if (villagerData.fwzp && Array.isArray(villagerData.fwzp) && villagerData.fwzp.length > 0) {
            villagerData.fwzpList = villagerData.fwzp.map(item => item.file);
            allImages = allImages.concat(villagerData.fwzpList);
          }
          
          // 其他证件照片
          if (villagerData.qtzjzp && Array.isArray(villagerData.qtzjzp) && villagerData.qtzjzp.length > 0) {
            villagerData.qtzjzpList = villagerData.qtzjzp.map(item => item.file);
            allImages = allImages.concat(villagerData.qtzjzpList);
          }
          
          // 耕地照片
          if (villagerData.gczp && Array.isArray(villagerData.gczp) && villagerData.gczp.length > 0) {
            villagerData.gczpList = villagerData.gczp.map(item => item.file);
            allImages = allImages.concat(villagerData.gczpList);
          }
          
          // 计算照片总数
          const grzpCount = villagerData.grzpList ? villagerData.grzpList.length : 0;
          const fwzpCount = villagerData.fwzpList ? villagerData.fwzpList.length : 0;
          const qtzjzpCount = villagerData.qtzjzpList ? villagerData.qtzjzpList.length : 0;
          const gczpCount = villagerData.gczpList ? villagerData.gczpList.length : 0;
          villagerData.photoCount = grzpCount + fwzpCount + qtzjzpCount + gczpCount;
          
          // 设置所有图片列表
          villagerData.allImages = allImages;
          
          // 遍历所有字段，找出需要显示的信息
          for (const key in villagerData) {
            // 只处理有值的字段，且不在主字段列表中，并且排除photoCount
            if (villagerData.hasOwnProperty(key) && 
                !mainFields.includes(key) && 
                villagerData[key] && 
                !Array.isArray(villagerData[key]) && // 排除数组类型
                typeof villagerData[key] !== 'object' && // 排除对象类型
                key !== 'code' &&  // 排除状态码
                key !== 'msg' &&   // 排除消息
                !key.endsWith('List') && 
                key !== 'photoCount') {  // 排除处理后的列表和photoCount
              
              otherInfo.push({
                label: this.getFieldName(key),
                value: villagerData[key]
              });
            }
          }
          
          if (otherInfo.length > 0) {
            villagerData.otherInfo = otherInfo;
          }
          
          // 补充可能缺失的字段
          const processedData = {
            id: villagerData.id || this.villagerId,
            title: villagerData.title || villagerData.name || '未知姓名',
            sfzhm: villagerData.sfzhm || villagerData.keywords || villagerData.idcard || '未知',
            hujidizhi: villagerData.hujidizhi || villagerData.address || '未知',
            hujishuxing: villagerData.hujishuxing || '一般户',
            shoujihaoma: villagerData.shoujihaoma || villagerData.phone || villagerData.mobile || '未知',
            xingbie: villagerData.xingbie || '未知',
            hits: villagerData.hits || 0,
            comments: villagerData.comments || 0,
            updatetime: villagerData.updatetime || '未知',
            inputtime: villagerData.inputtime || '未知',
            otherInfo: villagerData.otherInfo || [],
            // 个人照片缩略图
            thumb: villagerData.thumb || '',
            // 图片相关
            grzpList: villagerData.grzpList || [],
            fwzpList: villagerData.fwzpList || [],
            qtzjzpList: villagerData.qtzjzpList || [],
            gczpList: villagerData.gczpList || [],
            allImages: villagerData.allImages || [],
            // 反馈信息
            fankui: villagerData.fankui || [],
            photoCount: villagerData.photoCount || 0
          };
          
          // 判断是否为销户状态
          if (processedData.hujishuxing && typeof processedData.hujishuxing === 'string') {
            processedData.isXiaohu = processedData.hujishuxing.indexOf('销户') >= 0 || 
                                      processedData.hujishuxing.indexOf('已销户') >= 0;
          } else {
            processedData.isXiaohu = false;
          }
          
          // 是否有图片
          const hasImages = processedData.allImages.length > 0;
          
          this.setData({
            villager: processedData,
            loading: false,
            hasImages: hasImages
          });
          
          this.setDefaultPhotoTab(processedData);
        } else {
          this.setData({
            loading: false,
            loadError: true,
            errorMsg: res.data.msg || '获取村民信息失败'
          });
        }
      },
      fail: (err) => {
        console.log('请求失败:', err);
        this.setData({
          loading: false,
          loadError: true,
          errorMsg: '网络错误，请重试'
        });
      },
      complete: () => {
        wx.hideLoading();
      }
    });
  },
  
  // 获取字段名称
  getFieldName: function(key) {
    const fieldNames = {
      // 根据实际情况添加字段名称映射
      xingbie: '性别',
      birthday: '出生日期',
      xianjudi: '现居地',
      jtgx: '家庭关系',
      huhao: '户号',
      fwxtbh: '房屋系统编号',
      bdcdjh: '不动产登记号',
      yktzh: '一卡通账号',
      ylbxkyx: '医疗保险卡银行',
      ylbxkzh: '医疗保险卡账号'
    };
    
    return fieldNames[key] || key;
  },
  
  // 预览图片
  previewImage: function(e) {
    const index = e.currentTarget.dataset.index;
    const type = e.currentTarget.dataset.type || 'all';
    const current = e.currentTarget.dataset.current;
    const urls = e.currentTarget.dataset.urls;
    
    // 如果是从评论中预览图片
    if (current && urls) {
      wx.previewImage({
        current: current,
        urls: urls
      });
      return;
    }
    
    // 村民照片预览
    let photoUrls = [];
    let currentPhoto = '';
    
    if (type === 'grzp' && this.data.villager.grzpList.length > 0) {
      photoUrls = this.data.villager.grzpList;
      currentPhoto = photoUrls[index];
    } else if (type === 'fwzp' && this.data.villager.fwzpList.length > 0) {
      photoUrls = this.data.villager.fwzpList;
      currentPhoto = photoUrls[index];
    } else if (type === 'qtzjzp' && this.data.villager.qtzjzpList.length > 0) {
      photoUrls = this.data.villager.qtzjzpList;
      currentPhoto = photoUrls[index];
    } else if (type === 'gczp' && this.data.villager.gczpList.length > 0) {
      photoUrls = this.data.villager.gczpList;
      currentPhoto = photoUrls[index];
    } else if (type === 'all' && this.data.villager.allImages.length > 0) {
      photoUrls = this.data.villager.allImages;
      currentPhoto = photoUrls[index];
    }
    
    if (photoUrls.length > 0) {
      wx.previewImage({
        current: currentPhoto,
        urls: photoUrls
      });
    }
  },
  
  // 编辑村民信息
  editVillager: function() {
    if (!this.villagerId) return;
    
    // 获取验证信息
    var member_uid = wx.getStorageSync('member_uid');
    var member_auth = wx.getStorageSync('member_auth');
    
    if (!member_uid || !member_auth) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }
    
    // 跳转到本地编辑页面
    wx.navigateTo({
      url: './cmdaedit?id=' + this.villagerId
    });
  },
  
  // 返回列表
  goBack: function() {
    wx.navigateBack();
  },
  
  // 打开反馈链接
  openFeedback: function(e) {
    const url = e.currentTarget.dataset.url;
    if (!url) return;
    
    wx.navigateTo({
      url: '../common/webview?url=' + encodeURIComponent(url)
    });
  },
  
  // 加载评论
  loadComments: function() {
    if (this.data.isLoading || !this.data.hasMore) return;
    
    this.setData({ isLoading: true });
    
    // 获取验证信息
    var member_uid = wx.getStorageSync('member_uid');
    var member_auth = wx.getStorageSync('member_auth');
    
    if (!member_uid || !member_auth) {
      this.setData({ isLoading: false });
      return;
    }
    
    // 构建API请求URL - 参考workorder/show.js
    const requestUrl = app.globalData.http_api + 
      "s=cmda&c=comment" + 
      "&api_call_function=module_comment_list";
    
    console.log('获取评论URL:', requestUrl);
    
    wx.request({
      url: requestUrl,
      method: 'GET',
      data: {
        id: this.villagerId,
        page: this.data.commentPage
      },
      header: {
        'content-type': 'application/json'
      },
      success: res => {
        console.log('获取评论结果:', res.data);
        
        if (res.data.code == 1) {
          let comments = res.data.data;
          if (!Array.isArray(comments)) {
            if (comments && comments.list) {
              comments = comments.list;
            } else {
              comments = [];
            }
          }
          
          // 处理评论内容，为每条评论添加唯一标识，并使用wxParse解析
          const newComments = comments.map((item, index) => {
            const uniqueKey = `${item.id || ''}_${item.inputtime || ''}_${Date.now() + index}`;
            const nodeName = `comment_${uniqueKey}`;
            
            // 处理评论图片
            if (item.images && typeof item.images === 'string' && item.images.length > 0) {
              try {
                item.images = JSON.parse(item.images);
              } catch (e) {
                item.images = [];
              }
            } else {
              item.images = [];
            }
            
            // 使用wxParse解析评论内容
            if (item.content) {
              WxParse.wxParse(nodeName, 'html', item.content, this, 5);
            }
            
            return {
              ...item,
              uniqueKey,
              parsedContent: this.data[nodeName]
            };
          });
          
          this.setData({
            commentList: this.data.commentPage === 1 ? newComments : this.data.commentList.concat(newComments),
            hasMore: newComments.length >= this.data.commentLimit,
            commentPage: this.data.commentPage + 1,
            isLoading: false
          });
        } else {
          this.setData({ isLoading: false });
          wx.showToast({
            title: res.data.msg || '获取评论失败',
            icon: 'none'
          });
        }
      },
      fail: err => {
        console.log('获取评论失败:', err);
        this.setData({ isLoading: false });
      }
    });
  },
  
  // 显示完整评论栏
  showFullCommentBar: function() {
    this.setData({ isCommentBarShow: true });
  },
  
  // 隐藏完整评论栏
  hideFullCommentBar: function() {
    this.setData({ 
      isCommentBarShow: false,
      commentText: '',
      tempImagePaths: []
    });
  },
  
  // 获取评论文本
  getText: function(e) {
    this.setData({ commentText: e.detail.value });
  },
  
  // 选择图片
  chooseImage: function() {
    wx.chooseMedia({
      count: 9 - this.data.tempImagePaths.length,
      mediaType: ['image'],
      sourceType: ['album', 'camera'],
      success: res => {
        let tempFiles = res.tempFiles;
        let newPaths = tempFiles.map(file => file.tempFilePath);
        
        this.setData({
          tempImagePaths: this.data.tempImagePaths.concat(newPaths)
        });
      }
    });
  },
  
  // 删除图片
  deleteImage: function(e) {
    const index = e.currentTarget.dataset.index;
    let paths = this.data.tempImagePaths;
    paths.splice(index, 1);
    this.setData({ tempImagePaths: paths });
  },
  
  // 防止触摸滑动
  preventTouchMove: function() {
    return false;
  },
  
  // 保存评论
  saveComment: function() {
    if ((!this.data.commentText || this.data.commentText.trim() === '') && this.data.tempImagePaths.length === 0) {
      wx.showToast({
        title: '请输入评论内容或上传图片',
        icon: 'none'
      });
      return;
    }
    
    // 获取验证信息
    var member_uid = wx.getStorageSync('member_uid');
    var member_auth = wx.getStorageSync('member_auth');
    var member = wx.getStorageSync('member');
    
    if (!member_uid || !member_auth || !member) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }
    
    wx.showLoading({ title: '提交中...' });
    
    // 先上传图片，再提交评论
    if (this.data.tempImagePaths.length > 0) {
      this.uploadImages(0, []);
    } else {
      this.submitComment('');
    }
  },
  
  // 递归上传图片
  uploadImages: function(index, uploadedImages) {
    if (index >= this.data.tempImagePaths.length) {
      // 所有图片上传完成，提交评论
      let imagesHtml = '';
      
      // 构建图片HTML内容
      if (uploadedImages.length > 0) {
        // 创建一个包裹所有图片的容器div
        imagesHtml = '<div class="comment-images-container wxParse-p-with-img">';
        
        // 添加所有图片
        uploadedImages.forEach(url => {
          if (uploadedImages.length === 1) {
            // 单图样式
            imagesHtml += `<img src="${url}" class="wxParse-img wxParse-img-single" mode="widthFix">`;
          } else if (uploadedImages.length === 2) {
            // 双图样式
            imagesHtml += `<img src="${url}" class="wxParse-img wxParse-img-double" mode="aspectFill">`;
          } else {
            // 多图样式
            imagesHtml += `<img src="${url}" class="wxParse-img" mode="aspectFill">`;
          }
        });
        
        // 如果超过3张图片，添加计数指示器
        if (uploadedImages.length > 3) {
          imagesHtml += `<div class="image-count-indicator">共${uploadedImages.length}张</div>`;
        }
        
        // 关闭容器
        imagesHtml += '</div>';
      }
      
      this.submitComment(imagesHtml);
      return;
    }
    
    const imgPath = this.data.tempImagePaths[index];
    
    // 获取验证信息
    var member_uid = wx.getStorageSync('member_uid');
    var member_auth = wx.getStorageSync('member_auth');
    
    wx.uploadFile({
      url: app.globalData.http_api + 's=api&c=file&m=ueditor&action=uploadimage&encode=utf-8',
      filePath: imgPath,
      name: 'upfile',
      formData: {
        'api_auth_uid': member_uid,
        'api_auth_code': member_auth
      },
      success: res => {
        try {
          console.log('图片上传结果:', res.data);
          // 尝试解析JSON
          let data = JSON.parse(res.data);
          if (data && data.url) {
            uploadedImages.push(data.url);
          } else if (data && data.state === 'SUCCESS' && data.url) {
            uploadedImages.push(data.url);
          }
        } catch (e) {
          console.error('解析上传结果失败:', e);
          // 检查返回的数据是否是HTML格式(通常由 '<' 开始)
          if (res.data && typeof res.data === 'string' && res.data.startsWith('<')) {
            console.log('服务器返回了HTML而非JSON');
            wx.showToast({
              title: '图片上传失败',
              icon: 'none'
            });
          }
        }
      },
      fail: err => {
        console.error('图片上传请求失败:', err);
      },
      complete: () => {
        // 上传下一张，无论成功失败都继续
        this.uploadImages(index + 1, uploadedImages);
      }
    });
  },
  
  // 提交评论
  submitComment: function(imagesHtml) {
    // 获取验证信息
    var member_uid = wx.getStorageSync('member_uid');
    var member_auth = wx.getStorageSync('member_auth');
    var member = wx.getStorageSync('member');
    
    // 构建内容
    let content = this.data.commentText || '';
    if (imagesHtml) {
      if (content) {
        content = `<p>${content}</p>${imagesHtml}`;
      } else {
        content = imagesHtml;
      }
    }
    
    // 构建请求URL和参数 - 参考workorder/show.js
    const url = app.globalData.http_api + 
      "s=cmda&m=post&c=comment" + 
      "&api_auth_uid=" + member_uid + 
      "&api_auth_code=" + member_auth + 
      "&id=" + this.villagerId;
    
    // 构建请求参数，模仿workorder的评论提交参数
    const params = {
      content: content,
      module: 'cmda',
      mid: 'cmda',
      cid: this.villagerId,
      uid: member_uid,
      cuid: this.data.villager.uid || 0, // 关联uid，即档案创建者的uid
      author: member.username || '匿名用户',
      pinglunzhe: member_uid,
      status: 1
    };
    
    console.log('提交评论URL:', url);
    console.log('提交评论参数:', params);
    
    wx.request({
      url: url,
      method: 'POST',
      header: {
        'content-type': 'application/x-www-form-urlencoded'
      },
      data: params,
      success: res => {
        console.log('评论结果:', res.data);
        
        if (res.data.code == 1) {
          wx.showToast({
            title: '评论成功',
            icon: 'success'
          });
          
          // 重置数据
          this.setData({
            commentText: '',
            tempImagePaths: [],
            isCommentBarShow: false,
            commentList: [],
            commentPage: 1,
            hasMore: true
          });
          
          // 重新加载评论
          this.loadComments();
          
          // 刷新主数据，更新评论数量
          this.loadDetail();
        } else {
          wx.showToast({
            title: res.data.msg || '评论失败',
            icon: 'none'
          });
        }
      },
      fail: err => {
        console.log('评论失败:', err);
        wx.showToast({
          title: '网络错误',
          icon: 'none'
        });
      },
      complete: () => {
        wx.hideLoading();
      }
    });
  },
  
  // 下拉刷新
  onPullDownRefresh: function() {
    this.loadDetail();
    this.setData({
      commentList: [],
      commentPage: 1,
      hasMore: true
    });
    this.loadComments();
    wx.stopPullDownRefresh();
  },
  
  // 上拉加载更多
  onReachBottom: function() {
    this.loadComments();
  },
  
  // 分享
  onShareAppMessage: function() {
    if (this.data.villager) {
      return {
        title: this.data.villager.title + '的村民档案',
        path: '/pages/cmda/cmdashow?id=' + this.villagerId
      };
    }
    return {
      title: '村民档案详情',
      path: '/pages/cmda/index'
    };
  },
  
  // WxParse的图片点击预览
  wxParseImgTap: function(e) {
    const src = e.currentTarget.dataset.src;
    if (!src) return;
    
    // 收集当前评论中的所有图片URL
    const commentList = this.data.commentList;
    const allImgUrls = [];
    
    // 遍历所有评论，查找所有图片
    for (let i = 0; i < commentList.length; i++) {
      const comment = commentList[i];
      if (comment.parsedContent && comment.parsedContent.nodes) {
        this.findImgUrls(comment.parsedContent.nodes, allImgUrls);
      }
    }
    
    // 如果没有找到图片，至少显示当前图片
    const imageUrls = allImgUrls.length > 0 ? allImgUrls : [src];
    
    wx.previewImage({
      current: src,
      urls: imageUrls
    });
  },
  
  // 递归查找所有图片URL
  findImgUrls: function(nodes, result) {
    if (!nodes) return;
    
    for (let i = 0; i < nodes.length; i++) {
      const node = nodes[i];
      if (node.tag === 'img' && node.attr && node.attr.src) {
        result.push(node.attr.src);
      }
      
      if (node.nodes && node.nodes.length > 0) {
        this.findImgUrls(node.nodes, result);
      }
    }
  },
  
  // WxParse的图片加载函数
  wxParseImgLoad: function(e) {
    // 图片加载完成时的处理
  },
  
  // 预览缩略图
  previewThumb: function() {
    if (this.data.villager && this.data.villager.thumb) {
      wx.previewImage({
        current: this.data.villager.thumb,
        urls: [this.data.villager.thumb]
      });
    }
  },
  
  // 添加切换照片标签页方法
  switchPhotoTab: function(e) {
    const tab = e.currentTarget.dataset.tab;
    this.setData({
      activePhotoTab: tab
    });
  },
  
  // 处理村民详情数据后，设置默认选中的照片标签页
  setDefaultPhotoTab: function(villagerData) {
    let defaultTab = '';
    
    // 按照顺序检查哪个类型的照片存在，优先选择有照片的类型
    if (villagerData.grzpList && villagerData.grzpList.length > 0) {
      defaultTab = 'grzp';
    } else if (villagerData.fwzpList && villagerData.fwzpList.length > 0) {
      defaultTab = 'fwzp';
    } else if (villagerData.qtzjzpList && villagerData.qtzjzpList.length > 0) {
      defaultTab = 'qtzjzp';
    } else if (villagerData.gczpList && villagerData.gczpList.length > 0) {
      defaultTab = 'gczp';
    }
    
    // 设置默认选中的标签
    this.setData({
      activePhotoTab: defaultTab
    });
  },
  
  // 新增方法，用于检查照片是否为空
  checkEmptyPhotos: function() {
    // 获取当前标签页和数据
    const tab = this.data.activePhotoTab;
    const villager = this.data.villager;

    if (!tab || !villager) return true;

    // 根据标签页类型检查相应的照片列表
    switch(tab) {
      case 'grzp':
        return !villager.grzpList || villager.grzpList.length === 0;
      case 'fwzp':
        return !villager.fwzpList || villager.fwzpList.length === 0;
      case 'qtzjzp':
        return !villager.qtzjzpList || villager.qtzjzpList.length === 0;
      case 'gczp':
        return !villager.gczpList || villager.gczpList.length === 0;
      default:
        return true;
    }
  },

  /**
   * 更新图片功能
   */
  updateImages: function() {
    console.log('updateImages 方法被调用');
    console.log('villager数据:', this.data.villager);
    console.log('villagerId:', this.villagerId);

    if (!this.data.villager || !this.villagerId) {
      console.log('村民信息检查失败');
      wx.showToast({
        title: '村民信息不完整',
        icon: 'none'
      });
      return;
    }

    // 检查权限
    const member = wx.getStorageSync('member');
    console.log('用户权限信息:', member);

    if (!member || !member.is_admin || member.is_admin <= 0) {
      console.log('权限检查失败');
      wx.showModal({
        title: '权限不足',
        content: '只有管理员可以更新村民图片',
        showCancel: false
      });
      return;
    }

    // 跳转到图片更新页面
    const targetUrl = './cmdapic?id=' + this.villagerId;
    console.log('准备跳转到:', targetUrl);

    wx.navigateTo({
      url: targetUrl,
      success: function() {
        console.log('页面跳转成功');
      },
      fail: function(err) {
        console.error('页面跳转失败:', err);
        wx.showToast({
          title: '页面跳转失败',
          icon: 'none'
        });
      }
    });
  },

  /**
   * 更新信息功能
   */
  updateInfo: function() {
    if (!this.data.villager || !this.villagerId) {
      wx.showToast({
        title: '村民信息不完整',
        icon: 'none'
      });
      return;
    }

    // 检查权限
    const member = wx.getStorageSync('member');
    if (!member || !member.is_admin || member.is_admin <= 0) {
      wx.showModal({
        title: '权限不足',
        content: '只有管理员可以更新村民信息',
        showCancel: false
      });
      return;
    }

    // 跳转到编辑页面
    wx.navigateTo({
      url: './cmdaedit?id=' + this.villagerId
    });
  },

  /**
   * 新增档案功能
   */
  addNewArchive: function() {
    // 检查权限
    const member = wx.getStorageSync('member');
    if (!member || !member.is_admin || member.is_admin <= 0) {
      wx.showModal({
        title: '权限不足',
        content: '只有管理员可以新增村民档案',
        showCancel: false
      });
      return;
    }

    // 跳转到新增档案页面
    wx.navigateTo({
      url: './cmdaedit'
    });
  },

  /**
   * 新增备注功能
   */
  addNewNote: function() {
    if (!this.data.villager || !this.villagerId) {
      wx.showToast({
        title: '村民信息不完整',
        icon: 'none'
      });
      return;
    }

    // 检查权限
    const member = wx.getStorageSync('member');
    if (!member) {
      wx.showModal({
        title: '请先登录',
        content: '需要登录后才能添加备注',
        showCancel: false,
        success: () => {
          wx.navigateTo({
            url: '/pages/login/login'
          });
        }
      });
      return;
    }

    // 显示评论输入框
    this.showFullCommentBar();
  }
});