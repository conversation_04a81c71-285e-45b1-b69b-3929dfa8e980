<!-- 村民档案图片管理页面 -->
<view class="container">
  

  <!-- 加载状态 -->
  <view class="loading-container" wx:if="{{loading}}">
    <view class="loading-spinner"></view>
    <text class="loading-text">正在加载...</text>
  </view>

  <!-- 主要内容 -->
  <view class="main-content" wx:elif="{{villager}}">
    <!-- 用户信息 -->
    <view class="user-header">
      <view class="user-avatar">
        <image src="{{thumbUrl || '/images/default-avatar.png'}}" mode="aspectFill" class="avatar-img"></image>
      </view>
      <view class="user-info">
        <text class="user-name">{{villager.title}}</text>
        <text class="user-id">ID: {{villager.id}}</text>
        <text class="photo-count">共 {{totalPhotos}} 张照片</text>
      </view>
    </view>

    <!-- 图片分类管理 -->
    <view class="photo-sections">

      <!-- 1. 个人照片 -->
      <view class="photo-section" id="avatar">
        <view class="section-header">
          <view class="section-title">
            <text class="title-icon">👤</text>
            <text class="title-text">个人照片</text>
          </view>
          <text class="section-count">{{thumbUrl ? '1/1' : '0/1'}}</text>
        </view>

        <view class="avatar-section">
          <view class="avatar-preview" wx:if="{{thumbUrl}}">
            <image src="{{thumbUrl}}" mode="aspectFill" class="avatar-large"></image>
            <view class="avatar-actions">
              <button class="action-btn primary" bindtap="chooseThumb">更换</button>
              <button class="action-btn secondary" bindtap="deleteThumb">删除</button>
            </view>
          </view>

          <view class="avatar-upload" wx:else bindtap="chooseThumb">
            <view class="upload-icon">📷</view>
            <text class="upload-text">设置个人照片</text>
            <text class="upload-desc">用作档案头像显示</text>
          </view>
        </view>
      </view>

      <!-- 2. 身份证照片 -->
      <view class="photo-section" id="identity">
        <view class="section-header">
          <view class="section-title">
            <text class="title-icon">🆔</text>
            <text class="title-text">身份证照片</text>
          </view>
          <text class="section-count">{{tempGrzpList.length}}/9</text>
        </view>

        <view class="photo-grid">
          <view class="photo-item" wx:for="{{tempGrzpList}}" wx:key="index">
            <image src="{{item}}" mode="aspectFill" class="photo-img"
                   bindtap="previewImage" data-type="grzp" data-index="{{index}}"></image>
            <view class="delete-btn" bindtap="deleteImage" data-type="grzp" data-index="{{index}}">×</view>
          </view>

          <view class="upload-item" bindtap="chooseImage" data-type="grzp" wx:if="{{tempGrzpList.length < 9}}">
            <text class="upload-plus">+</text>
            <text class="upload-label">添加</text>
          </view>
        </view>
      </view>

      <!-- 3. 户口簿照片 -->
      <view class="photo-section" id="household">
        <view class="section-header">
          <view class="section-title">
            <text class="title-icon">📋</text>
            <text class="title-text">户口簿照片</text>
          </view>
          <text class="section-count">{{tempQtzjzpList.length}}/9</text>
        </view>

        <view class="photo-grid">
          <view class="photo-item" wx:for="{{tempQtzjzpList}}" wx:key="index">
            <image src="{{item}}" mode="aspectFill" class="photo-img"
                   bindtap="previewImage" data-type="qtzjzp" data-index="{{index}}"></image>
            <view class="delete-btn" bindtap="deleteImage" data-type="qtzjzp" data-index="{{index}}">×</view>
          </view>

          <view class="upload-item" bindtap="chooseImage" data-type="qtzjzp" wx:if="{{tempQtzjzpList.length < 9}}">
            <text class="upload-plus">+</text>
            <text class="upload-label">添加</text>
          </view>
        </view>
      </view>

      <!-- 4. 房屋照片 -->
      <view class="photo-section" id="house">
        <view class="section-header">
          <view class="section-title">
            <text class="title-icon">🏠</text>
            <text class="title-text">房屋照片</text>
          </view>
          <text class="section-count">{{tempFwzpList.length}}/9</text>
        </view>

        <view class="photo-grid">
          <view class="photo-item" wx:for="{{tempFwzpList}}" wx:key="index">
            <image src="{{item}}" mode="aspectFill" class="photo-img"
                   bindtap="previewImage" data-type="fwzp" data-index="{{index}}"></image>
            <view class="delete-btn" bindtap="deleteImage" data-type="fwzp" data-index="{{index}}">×</view>
          </view>

          <view class="upload-item" bindtap="chooseImage" data-type="fwzp" wx:if="{{tempFwzpList.length < 9}}">
            <text class="upload-plus">+</text>
            <text class="upload-label">添加</text>
          </view>
        </view>
      </view>

      <!-- 5. 改厕照片 -->
      <view class="photo-section" id="toilet">
        <view class="section-header">
          <view class="section-title">
            <text class="title-icon">🚽</text>
            <text class="title-text">改厕照片</text>
          </view>
          <text class="section-count">{{tempGczpList.length}}/9</text>
        </view>

        <view class="photo-grid">
          <view class="photo-item" wx:for="{{tempGczpList}}" wx:key="index">
            <image src="{{item}}" mode="aspectFill" class="photo-img"
                   bindtap="previewImage" data-type="gczp" data-index="{{index}}"></image>
            <view class="delete-btn" bindtap="deleteImage" data-type="gczp" data-index="{{index}}">×</view>
          </view>

          <view class="upload-item" bindtap="chooseImage" data-type="gczp" wx:if="{{tempGczpList.length < 9}}">
            <text class="upload-plus">+</text>
            <text class="upload-label">添加</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 底部保存按钮 -->
    <view class="bottom-bar">
      <button class="bottom-btn primary" bindtap="saveImages">保存</button>
    </view>
  </view>
</view>
