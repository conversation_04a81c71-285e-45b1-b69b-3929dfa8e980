page{
    border-top: 1px solid #e0e0e0;
}
.pl-box{
    padding: 30rpx 0;
    margin: 0 30rpx;
    border-bottom: 1px solid #e0e0e0;
}

.flex-box{
    display: flex;
    justify-content:space-between;
    font-size: 26rpx;
    padding-top:30rpx;
}
.txt-over{
    max-width:510rpx;
    text-overflow:ellipsis;
    overflow: hidden;
}
.pay-wfk {
  font-size: 26rpx;
  font-weight: 300;
  padding: 5rpx 10rpx;
  color: #fff;
  border-radius: 5rpx;
  background-color: #ed6b75;
}
.pay-yfk {
  font-size: 26rpx;
  font-weight: 300;
  padding: 5rpx 10rpx;
  border-radius: 5rpx;
  color: #fff;
  background-color: #36c6d3;
}
.pl-bottom {
    margin-top:30rpx;
}

.container {
  min-height: 100vh;
  background-color: #f5f5f5;
  display: flex;
  flex-direction: column;
}

/* 标签栏样式 */
.tab-nav {
  display: flex;
  background: #fff;
  padding: 0 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
  position: sticky;
  top: 0;
  z-index: 100;
}

.tab-item {
  position: relative;
  padding: 20rpx 30rpx;
  flex: 1;
  text-align: center;
}

.tab-item text {
  font-size: 28rpx;
  color: #666;
  transition: all 0.3s ease;
}

.tab-item.active text {
  color: #1890ff;
  font-weight: 500;
}

.tab-line {
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 40rpx;
  height: 4rpx;
  background: #1890ff;
  border-radius: 2rpx;
  opacity: 0;
  transition: all 0.3s ease;
}

.tab-item.active .tab-line {
  opacity: 1;
}

/* 记录列表样式 */
.content-list {
  flex: 1;
  height: calc(100vh - 100rpx);
}

.record-card {
  background: #fff;
  margin: 20rpx;
  padding: 30rpx;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.record-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20rpx;
}

.record-title-wrap {
  flex: 1;
  margin-right: 20rpx;
}

.record-title {
  font-size: 32rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 8rpx;
  display: block;
}

.record-type {
  font-size: 24rpx;
  padding: 4rpx 12rpx;
  border-radius: 4rpx;
  display: inline-block;
}

.type-income {
  background: #f6ffed;
  color: #52c41a;
}

.type-expense {
  background: #fff2f0;
  color: #f56c6c;
}

.record-amount {
  font-size: 36rpx;
  font-weight: 600;
}

.amount-income {
  color: #52c41a;
}

.amount-expense {
  color: #f56c6c;
}

.record-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.info-item {
  display: flex;
  align-items: center;
}

.info-label {
  font-size: 26rpx;
  color: #999;
}

.info-value {
  font-size: 26rpx;
  color: #666;
}

.status-tag {
  font-size: 24rpx;
  padding: 4rpx 12rpx;
  border-radius: 4rpx;
}

.status-paid {
  background: #f6ffed;
  color: #52c41a;
}

.status-unpaid {
  background: #fff2f0;
  color: #f56c6c;
}

.record-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 20rpx;
  border-top: 1rpx solid #f0f0f0;
}

.record-time {
  font-size: 24rpx;
  color: #999;
}

.record-action {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.action-text {
  font-size: 26rpx;
  color: #1890ff;
}

.action-icon {
  width: 24rpx;
  height: 24rpx;
  opacity: 0.6;
}

/* 加载状态样式 */
.loading-state {
  padding: 30rpx 0;
  text-align: center;
}

.loading-content {
  display: inline-flex;
  align-items: center;
  gap: 12rpx;
}

.loading-spinner {
  width: 32rpx;
  height: 32rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #1890ff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 26rpx;
  color: #999;
}

/* 无更多数据样式 */
.no-more {
  padding: 30rpx 0;
}

.divider {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 20rpx;
}

.divider-line {
  width: 100rpx;
  height: 1rpx;
  background: #eee;
}

.no-more-text {
  font-size: 26rpx;
  color: #999;
}

/* 空状态样式 */
.empty-state {
  padding: 120rpx 0;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #fff;
  margin: 20rpx;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.empty-icon {
  width: 240rpx;
  height: 240rpx;
  margin-bottom: 30rpx;
  opacity: 0.8;
}

.empty-text {
  font-size: 30rpx;
  color: #999;
  font-weight: 400;
}