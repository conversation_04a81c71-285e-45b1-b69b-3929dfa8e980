/* pages/wenda/detail.wxss */

/* 导入wxParse的样式 */
@import "../../wxParse/wxParse.wxss";

/* 整体容器 */
.container {
  min-height: 100vh;
  background-color: #f5f7fa;
  padding-bottom: 120rpx; /* 为底部操作栏留出空间 */
  position: relative;
}

/* 详情卡片样式 */
.detail-card {
  background-color: #fff;
  border-radius: 16rpx;
  margin: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.03);
  overflow: hidden;
}

.detail-header {
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.detail-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  line-height: 1.5;
}

.detail-info {
  display: flex;
  flex-wrap: wrap;
  font-size: 24rpx;
  color: #888;
}

.detail-info-text {
  margin-right: 20rpx;
  margin-bottom: 8rpx;
}

/* 房产基本信息样式 */
.property-info {
  margin-top: 20rpx;
  padding-top: 20rpx;
  border-top: 1rpx dashed #e8e8e8;
  position: relative;
}

.info-title {
  font-size: 26rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 15rpx;
  position: relative;
  padding-left: 16rpx;
  display: flex;
  align-items: center;
}

.info-title-prefix {
  position: absolute;
  left: 0;
  top: 6rpx;
  bottom: 6rpx;
  width: 4rpx;
  background-color: #1677ff;
  border-radius: 2rpx;
}

.info-title::after {
  content: '';
  flex: 1;
  height: 1rpx;
  background: linear-gradient(to right, #e8e8e8, transparent);
  margin-left: 15rpx;
}

/* 房产信息网格布局 */
.property-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 10rpx;
  margin-top: 10rpx;
  background-color: #f9fafc;
  border-radius: 10rpx;
  padding: 10rpx;
  box-shadow: inset 0 1rpx 5rpx rgba(0, 0, 0, 0.02);
}

.property-grid-item {
  background-color: #fff;
  border-radius: 6rpx;
  padding: 10rpx 8rpx;
  box-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.03);
  position: relative;
  overflow: hidden;
  min-height: 72rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
  border-left: 3rpx solid #1677ff;
}

/* 长文本字段样式 */
.property-grid-item.long-item {
  grid-column: span 2;
}

.long-item .property-value {
  white-space: normal;
  word-break: break-all;
}

/* 不同类型的信息项边框颜色 */
.property-grid-item:nth-child(4n+1) {
  border-left-color: #1677ff;
}
.property-grid-item:nth-child(4n+2) {
  border-left-color: #52c41a;
}
.property-grid-item:nth-child(4n+3) {
  border-left-color: #f5a623;
}
.property-grid-item:nth-child(4n+4) {
  border-left-color: #7356f1;
}

/* 移除伪元素左边框 */
.property-grid-item::before {
  display: none;
}

.property-label {
  font-size: 20rpx;
  color: #888;
  margin-bottom: 4rpx;
  position: relative;
  padding-left: 8rpx;
}

.property-value {
  font-size: 24rpx;
  color: #333;
  font-weight: 500;
  overflow: hidden;
  text-overflow: ellipsis;
  padding-left: 8rpx;
  line-height: 1.3;
  word-break: break-word;
}

/* 对于长文本的适配 */
.property-grid-item:nth-child(odd):last-child {
  grid-column: span 2;
}

/* 移除所有点击效果 */
.property-grid-item:active {
  background-color: #fff;
}

/* 保留原来的样式供兼容使用 */
.info-item {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 10rpx;
  display: flex;
  flex-wrap: wrap;
}

/* 反馈信息区样式 */
.feedback-section {
  background-color: #fff;
  border-radius: 16rpx;
  margin: 20rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.03);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f1f3f5;
  overflow: visible; /* 防止出现滚动条 */
}

.section-title-container {
  display: flex;
  align-items: center;
  overflow: visible; /* 防止出现滚动条 */
}

.section-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  line-height: 1;
  white-space: nowrap; /* 防止文本换行 */
  overflow: visible; /* 防止出现滚动条 */
  display: flex;
  align-items: center;
}

.section-badge {
  font-size: 24rpx;
  background: #f7f8fc;
  color: #888;
  padding: 4rpx 16rpx;
  border-radius: 20rpx;
  line-height: 1.2;
  flex-shrink: 0; /* 防止徽章被挤压 */
  display: flex;
  align-items: center;
  height: 32rpx;
}

.section-content {
  padding: 0;
  overflow: visible; /* 防止出现滚动条 */
}

.feedback-list {
  padding: 0;
  overflow: visible; /* 防止出现滚动条 */
}

.feedback-item {
  display: flex;
  align-items: center;
  padding: 24rpx 30rpx;
  border-bottom: 1rpx solid #f1f3f5;
  position: relative;
}

.feedback-item:last-child {
  border-bottom: none;
}

.feedback-item:active {
  background-color: #f9f9f9;
}

.feedback-dot {
  width: 12rpx;
  height: 12rpx;
  border-radius: 50%;
  background-color: #5e72e4;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.feedback-title {
  font-size: 28rpx;
  color: #333;
  flex: 1;
}

.feedback-arrow {
  color: #bbb;
  font-size: 18px;
  margin-left: 10px;
}

/* 替代伪类 */
.feedback-arrow-text {
  font-size: 32rpx;
  font-weight: 300;
}

/* 点赞区域样式 */
.digg-container {
  padding: 20rpx 30rpx;
  border-top: 1rpx solid #f0f0f0;
}

.digg-buttons {
  display: flex;
  justify-content: flex-end;
}

.digg-item {
  display: flex;
  align-items: center;
  padding: 8rpx 20rpx;
  border-radius: 30rpx;
  margin-left: 20rpx;
  background-color: #f5f7fa;
  font-size: 24rpx;
  color: #666;
}

.digg-item.helpful {
  color: #2ecc71;
}

.digg-item.unhelpful {
  color: #e74c3c;
}

.digg-icon {
  width: 28rpx;
  height: 28rpx;
  margin-right: 8rpx;
}

/* 内容区域样式 */
.content-section {
  background-color: #fff;
  border-radius: 16rpx;
  margin: 20rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.03);
}

.content-header {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 20rpx;
  position: relative;
  padding-left: 20rpx;
}

/* 替代伪类 */
.content-header-prefix {
  position: absolute;
  left: 0;
  top: 6rpx;
  bottom: 6rpx;
  width: 6rpx;
  background-color: #1677ff;
  border-radius: 3rpx;
}

.content-body {
  font-size: 28rpx;
  line-height: 1.6;
  color: #333;
}

/* 文档区域样式 */
.document-section {
  background-color: #fff;
  border-radius: 16rpx;
  margin: 20rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.03);
}

.document-item {
  margin-top: 16rpx;
}

.document-image {
  width: 100%;
  border-radius: 8rpx;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);
}

.document-link {
  display: inline-block;
  padding: 12rpx 30rpx;
  background-color: #1677ff;
  color: #fff;
  font-size: 28rpx;
  border-radius: 8rpx;
  margin-top: 10rpx;
}

.image-title {
  font-size: 24rpx;
  color: #666;
  margin-top: 8rpx;
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  padding: 0 10rpx;
}

/* 图片区域样式 */
.image-section {
  background-color: #fff;
  border-radius: 16rpx;
  margin: 20rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.03);
}

.section-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 20rpx;
  position: relative;
  padding-left: 20rpx;
}

/* 替代伪类 */
.section-title-prefix {
  position: absolute;
  left: 0;
  top: 6rpx;
  bottom: 6rpx;
  width: 6rpx;
  background-color: #1677ff;
  border-radius: 3rpx;
}

.image-grid {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -5rpx;
}

.image-item {
  width: calc(33.333% - 10rpx);
  height: 180rpx;
  margin: 5rpx;
  border-radius: 8rpx;
  overflow: hidden;
  position: relative;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);
}

.preview-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.no-images {
  width: 100%;
  height: 120rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f9f9f9;
  border-radius: 8rpx;
  color: #999;
  font-size: 28rpx;
}

/* 底部信息区域样式 */
.footer-info {
  background-color: #fff;
  border-radius: 16rpx;
  margin: 20rpx;
  padding: 24rpx 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.03);
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  color: #888;
  font-size: 24rpx;
}

.footer-item {
  display: flex;
  align-items: center;
  margin: 6rpx 0;
}

.footer-label {
  margin-right: 10rpx;
}

.footer-value {
  color: #666;
}

/* 评论区域样式 */
.comments-section {
  background-color: #fff;
  border-radius: 16rpx;
  margin: 20rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.03);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f1f3f5;
}

.section-title-container {
  display: flex;
  align-items: center;
}

.section-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.comment-icon-content {
  content: '💬';
}

.section-badge {
  font-size: 24rpx;
  background: #f1f3f5;
  color: #7f8c8d;
  padding: 4rpx 16rpx;
  border-radius: 20rpx;
}

.section-content {
  padding: 20rpx 30rpx 30rpx;
}

.comments-list {
  padding: 0;
}

.comments-empty {
  padding: 30rpx 0;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #f9f9f9;
  border-radius: 8rpx;
  margin: 10rpx 0;
}

.empty-icon {
  width: 60rpx;
  height: 60rpx;
  margin-bottom: 10rpx;
  opacity: 0.6;
}

.empty-text {
  color: #95a5a6;
  font-size: 26rpx;
}

.comment-item {
  padding: 24rpx 0;
  border-bottom: 1rpx solid #f1f3f5;
}

.comment-item:last-child {
  border-bottom: none;
}

.comment-item-header {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.comment-item-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin-right: 20rpx;
  background-color: #f1f3f5;
}

.comment-item-meta {
  flex: 1;
}

.comment-item-author {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 6rpx;
  display: flex;
  align-items: center;
}

.comment-item-tag {
  font-size: 20rpx;
  background: #1677ff;
  color: #fff;
  padding: 2rpx 10rpx;
  border-radius: 6rpx;
  margin-left: 10rpx;
}

.comment-item-time {
  font-size: 24rpx;
  color: #999;
}

.comment-item-content {
  font-size: 28rpx;
  line-height: 1.6;
  color: #333;
}

.comment-images {
  display: flex;
  flex-wrap: wrap;
  margin: 20rpx -10rpx 0;
}

.comment-image-item {
  width: calc(33.333% - 20rpx);
  height: 180rpx;
  margin: 0 10rpx 20rpx;
  border-radius: 8rpx;
  overflow: hidden;
  position: relative;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);
}

.comment-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.loading-status {
  padding: 20rpx 0;
  text-align: center;
}

.loading-more {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #999;
  font-size: 24rpx;
}

.loading-spinner {
  width: 36rpx;
  height: 36rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #1677ff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 10rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-end {
  color: #999;
  font-size: 24rpx;
}

/* 底部间距 */
.bottom-spacer {
  height: 100rpx;
}

/* 底部操作栏 */
.action-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12rpx 20rpx;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.08);
  z-index: 99;
}

.action-button {
  height: 80rpx;
  width: 80rpx;
  padding: 0;
  margin: 0;
  border-radius: 50%;
  font-size: 26rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: #f5f7fa;
  border: none;
  position: relative;
}

.action-button.share-button {
  background: #1677ff;
  color: #fff;
}

.action-button-after {
  border: none;
}

.button-icon {
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
}

.button-text {
  font-size: 20rpx;
  margin-top: 4rpx;
}

.back-icon-content {
  content: '←';
  font-weight: bold;
}

.share-icon-content {
  content: '↗';
}

/* 评论输入区域 */
.comment-input-area {
  flex: 1;
  padding: 0 20rpx;
}

.comment-input-box {
  background: #f5f7fa;
  height: 80rpx;
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  padding: 0 30rpx;
  color: #999;
  font-size: 28rpx;
  border: 1rpx solid #e9ecef;
}

.input-placeholder {
  color: #999;
}

/* 评论弹出层 */
.comment-modal {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  z-index: 1000;
  transform: translateY(100%);
  transition: transform 0.3s ease;
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);
  border-radius: 30rpx 30rpx 0 0;
}

.comment-modal-show {
  transform: translateY(0);
}

.comment-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f1f3f5;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 500;
}

.comment-modal-close {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-icon {
  font-size: 40rpx;
  color: #999;
}

.comment-modal-body {
  padding: 30rpx;
}

.comment-modal-textarea {
  width: 100%;
  min-height: 200rpx;
  font-size: 30rpx;
  line-height: 1.5;
  padding: 20rpx 0;
}

.image-preview {
  display: flex;
  flex-wrap: wrap;
  margin-top: 20rpx;
}

.image-preview-item {
  width: 180rpx;
  height: 180rpx;
  margin: 0 20rpx 20rpx 0;
  border-radius: 8rpx;
  overflow: hidden;
  position: relative;
}

.image-preview-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.image-preview-delete {
  position: absolute;
  top: 0;
  right: 0;
  width: 40rpx;
  height: 40rpx;
  background: rgba(0, 0, 0, 0.5);
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 30rpx;
}

.comment-modal-footer {
  padding: 20rpx 30rpx;
  border-top: 1rpx solid #f1f3f5;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.upload-btn {
  background: none;
  padding: 0;
  display: flex;
  align-items: center;
  font-size: 28rpx;
  color: #1677ff;
}

.upload-btn-after {
  border: none;
}

.upload-icon {
  width: 32rpx;
  height: 32rpx;
  border-radius: 50%;
  background: #e7f5fe;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 10rpx;
  position: relative;
}

.upload-icon-content {
  content: '+';
  color: #1677ff;
  font-weight: bold;
}

.image-count {
  margin-left: 6rpx;
  color: #1677ff;
}

.comment-modal-actions {
  display: flex;
  align-items: center;
}

.comment-modal-btn {
  margin: 0 10rpx;
  height: 70rpx;
  border-radius: 8rpx;
  font-size: 28rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 30rpx;
}

.comment-modal-btn-after {
  border: none;
}

.comment-modal-btn-cancel {
  background: #f1f3f5;
  color: #999;
}

.comment-modal-btn-submit {
  background: #ccc;
  color: #fff;
}

.comment-modal-btn-submit.is-active {
  background: #1677ff;
}

/* wxParse 样式补充 */
.wxParse-p {
  margin-bottom: 20rpx;
  line-height: 1.6;
}

.wxParse-img {
  max-width: 100%;
  margin: 10rpx 0;
}

/* 针对评论中的图片处理 */
.wxParse-p-with-img {
  margin-top: 20rpx;
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 12rpx;
  background: #f8f9fa;
  padding: 12rpx;
  border-radius: 8rpx;
  position: relative;
}

.wxParse-img {
  width: 100% !important;
  height: 160rpx !important;
  object-fit: cover;
  border-radius: 6rpx;
}

/* 单张图片样式 */
.wxParse-p-with-img.single-img {
  grid-template-columns: 1fr;
  max-width: 400rpx;
}

.wxParse-p-with-img.single-img .wxParse-img {
  height: 280rpx !important;
}

/* 两张图片样式 */
.wxParse-p-with-img.double-img {
  grid-template-columns: repeat(2, 1fr);
  max-width: 600rpx;
}

.wxParse-p-with-img.double-img .wxParse-img {
  height: 220rpx !important;
}

.wxParse-img-single {
  max-width: 100%;
  max-height: 400rpx;
  margin: 10rpx 0;
  height: auto;
  object-fit: contain;
}

.wxParse-img-double {
  width: 100%;
  height: 220rpx !important;
  object-fit: cover;
}

/* 图片计数指示器 */
.image-count-indicator {
  position: absolute;
  right: 12rpx;
  bottom: 12rpx;
  background: rgba(0, 0, 0, 0.6);
  color: #fff;
  font-size: 22rpx;
  padding: 4rpx 16rpx;
  border-radius: 20rpx;
  z-index: 10;
}

/* 状态样式 */
.detail-info .status {
  display: inline-block;
  padding: 4rpx 12rpx;
  border-radius: 10rpx;
  color: #ff9900;
  background-color: rgba(255, 153, 0, 0.1);
  margin-left: 10rpx;
}

.detail-info .status.solved {
  color: #52c41a;
  background-color: rgba(82, 196, 26, 0.1);
}

.detail-info .status.inprocess {
  color: #1677ff;
  background-color: rgba(22, 119, 255, 0.1);
}

.detail-info .status.unsolved {
  color: #ff9900;
  background-color: rgba(255, 153, 0, 0.1);
}