@import "../member/paylog.wxss";

.container {
  min-height: 100vh;
  background-color: #f5f5f5;
  display: flex;
  flex-direction: column;
}

.content-list {
  flex: 1;
  height: calc(100vh - 40rpx);
}

.comment-card {
  background: #fff;
  margin: 20rpx;
  padding: 30rpx;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.comment-header {
  margin-bottom: 20rpx;
}

.comment-meta {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.meta-item {
  display: flex;
  align-items: center;
}

.meta-label {
  font-size: 26rpx;
  color: #999;
}

.meta-value {
  font-size: 26rpx;
  color: #ff9800;
  font-weight: 500;
}

.meta-time {
  font-size: 24rpx;
  color: #999;
}

.comment-content {
  font-size: 28rpx;
  color: #333;
  line-height: 1.6;
  margin-bottom: 20rpx;
}

.article-link {
  display: flex;
  align-items: center;
  margin: 20rpx 0;
  padding: 20rpx 0;
  border-top: 1rpx solid #f0f0f0;
}

.link-hover {
  opacity: 0.8;
}

.link-label {
  font-size: 28rpx;
  color: #666;
}

.link-text {
  font-size: 28rpx;
  color: #1890ff;
  flex: 1;
  margin: 0 12rpx;
}

.link-icon {
  width: 24rpx;
  height: 24rpx;
  opacity: 0.6;
}

/* 加载状态样式 */
.loading-state {
  padding: 30rpx 0;
  text-align: center;
}

.loading-content {
  display: inline-flex;
  align-items: center;
  gap: 12rpx;
}

.loading-spinner {
  width: 32rpx;
  height: 32rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #1890ff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 26rpx;
  color: #999;
}

/* 无更多数据样式 */
.no-more {
  padding: 30rpx 0;
}

.divider {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 20rpx;
}

.divider-line {
  width: 100rpx;
  height: 1rpx;
  background: #eee;
}

.no-more-text {
  font-size: 26rpx;
  color: #999;
}

/* 空状态样式 */
.empty-state {
  padding: 120rpx 0;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #fff;
  margin: 20rpx;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.empty-icon {
  width: 240rpx;
  height: 240rpx;
  margin-bottom: 30rpx;
  opacity: 0.8;
}

.empty-text {
  font-size: 30rpx;
  color: #999;
  font-weight: 400;
}