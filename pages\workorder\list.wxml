<view class="container">
  <view class="search-container">
    <view class="search-box">
      <image src="../../icons/search.png" class="search-icon" />
      <input 
        class="search-input" 
        placeholder="搜索工单" 
        placeholder-class="placeholder"
        bindinput="onSearchInput"
        confirm-type="search"
        bindconfirm="onSearchConfirm"
        value="{{searchKeyword}}"
      />
      <view class="clear-btn" wx:if="{{searchKeyword}}" catchtap="clearSearch">
        <image src="../../icons/clear.png" class="clear-icon" />
      </view>
    </view>
    <view class="search-btn" bindtap="onSearchConfirm">搜索</view>
  </view>
  <view class="tabs-container">
    <scroll-view scroll-x class="tabs-scroll" enhanced show-scrollbar="{{false}}">
      <view class="tabs">
        <view class="tab {{currentTab == 0 ? 'active' : ''}}" bindtap="switchTab" data-index="0">
          <text>所有工单</text>
        </view>
        <view class="tab {{currentTab == 1 ? 'active' : ''}}" bindtap="switchTab" data-index="1">
          <text>待受理</text>
        </view>
        <view class="tab {{currentTab == 5 ? 'active' : ''}}" bindtap="switchTab" data-index="5">
          <text>待回复</text>
        </view>
        <view class="tab {{currentTab == 4 ? 'active' : ''}}" bindtap="switchTab" data-index="4">
          <text>已回复</text>
        </view>
        <view class="tab {{currentTab == 2 ? 'active' : ''}}" bindtap="switchTab" data-index="2">
          <text>处理中</text>
        </view>
        <view class="tab {{currentTab == 3 ? 'active' : ''}}" bindtap="switchTab" data-index="3">
          <text>已完毕</text>
        </view>
      </view>
    </scroll-view>
  </view>

  <scroll-view 
    class="news-list" 
    scroll-y="true" 
    enable-back-to-top
    bindscrolltolower="loadMore"
    lower-threshold="50">
    <view class="no-data" wx:if="{{listData.length === 0}}">
      <image src="../../icons/no-data.png" class="no-data-icon" />
      <text class="no-data-text" wx:if="{{!isSearching}}">该类型中暂无数据</text>
      <text class="no-data-text" wx:else>未找到相关工单</text>
    </view>
    
    <view class="news-list-content">
      <block wx:for="{{listData}}" wx:for-item="news" wx:key="news">
        <navigator url="../workorder/show?id={{news.id}}" wx:if="{{news.gongkai != '保密' || (isLoggedIn && (isAdmin || news.userid == currentUserId))}}">
          <view class="news-card" bindlongpress="onLongPress" data-id="{{news.id}}">
            <view class="status-indicator {{news.fabuleixing === '发布信息' ? 'status-urgent' : ''}} {{news.jindu === '已完毕' ? 'status-completed' : ''}} {{news.jindu === '待受理' ? 'status-pending' : ''}} {{news.jindu === '处理中' ? 'status-processing' : ''}} {{news.jindu === '待回复' ? 'status-waiting' : ''}} {{news.jindu === '已回复' ? 'status-replied' : ''}}"></view>
            
            <view class="news-card-content">
              <view class="id-badge">
                <text>编号: {{news.id}}</text>
              </view>
              
              <view class="news-title {{news.fabuleixing === '发布信息' ? 'highlight-title' : ''}}">
                {{news.title}}
              </view>
              
              <view class="tags-container">
                <block wx:if="{{currentTab == 0}}">
                  <block wx:if="{{news.xinxileixing}}">
                    <view class="tag tag-type">{{news.xinxileixing}}</view>
                    <view class="tag tag-status {{news.jindu === '待受理' ? 'status-pending' : ''}} {{news.jindu === '处理中' ? 'status-processing' : ''}} {{news.jindu === '已完毕' ? 'status-completed' : ''}} {{news.jindu === '待回复' ? 'status-waiting' : ''}} {{news.jindu === '已回复' ? 'status-replied' : ''}}">{{news.jindu}}</view>
                  </block>
                  <block wx:else>
                    <view class="tag tag-department">{{news.sscz}}</view>
                    <view class="tag tag-status {{news.jindu === '待受理' ? 'status-pending' : ''}} {{news.jindu === '处理中' ? 'status-processing' : ''}} {{news.jindu === '已完毕' ? 'status-completed' : ''}} {{news.jindu === '待回复' ? 'status-waiting' : ''}} {{news.jindu === '已回复' ? 'status-replied' : ''}}">{{news.jindu}}</view>
                  </block>
                </block>
                <block wx:else>
                  <view class="tag tag-department" wx:if="{{news.sscz}}">{{news.sscz}}</view>
                </block>
              </view>
              
              <view class="news-footer">
                <view class="news-time">
                  <image src="../../icons/time.png" class="icon-small" />
                  <text>{{news.inputtime}}</text>
                </view>
                <view class="news-stats">
                  <view class="stat-item">
                    <image src="../../icons/see.png" class="icon-small" />
                    <text>{{news.hits}}</text>
                  </view>
                  <view class="stat-item">
                    <image src="../../icons/message.png" class="icon-small" />
                    <text>{{news.comments}}</text>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </navigator>
      </block>
    </view>

    <view class="loading-container" hidden="{{hidden}}">
      <view class="loading-tip" wx:if="{{hasMore}}">
        <image src="../../icons/waiting.gif" class="loading-icon" /> 
        <text>正在加载...</text>
      </view>
      <view class="no-more-tip" wx:else>
        <text>没有更多数据了</text>
      </view>
    </view>
  </scroll-view>

  <view class="action-menu" wx:if="{{showActionMenu}}">
    <view class="action-mask" bindtap="hideActionMenu"></view>
    <view class="action-content">
      <view class="action-item delete" bindtap="onDelete" data-id="{{selectedId}}">删除</view>
    </view>
  </view>
</view>