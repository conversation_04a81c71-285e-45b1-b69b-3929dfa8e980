<!-- 工单列表 -->
<view class="container">
  <!-- 顶部搜索栏 -->
  <view class="search-section">
    <view class="search-box">
      <view class="search-input-wrap">
        <image class="search-icon" src="../../icons/search.png" mode="aspectFit"></image>
        <input class="search-input" placeholder="搜索工单" bindinput="onSearchInput" 
          value="{{keyword}}" confirm-type="search" bindconfirm="doSearch" />
        <image class="clear-icon" src="../../icons/clear.png" mode="aspectFit" 
          wx:if="{{keyword}}" bindtap="clearSearch"></image>
      </view>
      <view class="search-btn" bindtap="doSearch">搜索</view>
    </view>
  </view>

  <!-- 工单列表 -->
  <scroll-view 
    class="content-list" 
    scroll-y="true" 
    bindscrolltolower="onReachBottom" 
    enhanced="{{true}}" 
    show-scrollbar="{{false}}"
    refresher-enabled="{{true}}"
    refresher-triggered="{{refreshing}}"
    bindrefresherrefresh="onPullDownRefresh">
    
    <block wx:if="{{listData.length > 0}}">
      <view class="workorder-card" wx:for="{{listData}}" wx:key="id" bindtap="navigateToDetail" data-id="{{item.id}}">
        <view class="card-header">
          <view class="order-info">
            <text class="order-number {{item.gongkai == '1' ? 'status-public' : item.gongkai == '2' ? 'status-private' : ''}}">
              <text class="status-label">状态：</text>{{item.gongkai == '1' ? '公开' : item.gongkai == '2' ? '保密' : '信息发布'}}
            </text>
            <text class="order-status {{item.jindu == '1' ? 'status-pending' : item.jindu == '2' ? 'status-processing' : item.jindu == '3' ? 'status-completed' : item.jindu == '4' ? 'status-replied' : 'status-waiting'}}">
              <text class="status-label">进度：</text>{{item.jindu == '1' ? '待受理' : item.jindu == '2' ? '处理中' : item.jindu == '3' ? '已完毕' : item.jindu == '4' ? '已回复' : '待回复'}}
            </text>
          </view>
          <text class="order-title highlight-title">{{item.title}}</text>
        </view>
        
        <view class="card-body">
          <view class="info-item time-item">
            <image class="time-icon" src="../../icons/time.png" mode="aspectFit"></image>
            <text class="time-text">{{item.inputtime}}</text>
            <view class="arrow-icon">
              <image src="../../icons/right-back.png" mode="aspectFit"></image>
            </view>
          </view>
        </view>
      </view>
    </block>

    <!-- 搜索结果提示 -->
    <view class="search-result-tip" wx:if="{{isSearching && listData.length > 0}}">
      <text>搜索到 {{listData.length}} 个相关工单</text>
      <view class="clear-search" bindtap="clearSearch">清除</view>
    </view>

    <!-- 空状态 - 搜索无结果 -->
    <view class="empty-state" wx:if="{{isSearching && listData.length === 0 && !loading}}">
      <image class="empty-icon" src="../../icons/search-empty.png" mode="aspectFit"></image>
      <text class="empty-text">未找到相关工单</text>
      <view class="empty-action" bindtap="clearSearch">
        <text>清除搜索</text>
      </view>
    </view>

    <!-- 空状态 - 无工单 -->
    <view class="empty-state" wx:if="{{!isSearching && listData.length === 0 && !loading}}">
      <image class="empty-icon" src="../../icons/empty.png" mode="aspectFit"></image>
      <text class="empty-text">暂无工单记录</text>
      <text class="empty-tip">点击下方按钮创建工单</text>
    </view>

    <!-- 加载状态 -->
    <view class="loading-state" wx:if="{{loading}}">
      <view class="loading-content">
        <image class="loading-icon" src="../../icons/loading.gif"/>
        <text class="loading-text">加载中...</text>
      </view>
    </view>
    
    <!-- 无更多数据 -->
    <view class="no-more" wx:if="{{!hasMore && listData.length > 0 && !loading}}">
      <text class="no-more-text">没有更多数据了</text>
    </view>
  </scroll-view>

  <!-- 创建按钮 -->
  <view class="create-button" bindtap="navigateToCreate">
    <view class="create-button-inner">
      <image class="create-icon" src="../../icons/add.png" mode="aspectFit"></image>
      <text class="create-text">新建工单</text>
    </view>
  </view>
</view> 