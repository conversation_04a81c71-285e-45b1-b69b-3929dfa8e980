<view class="container">
  <!-- 收藏列表 -->
  <scroll-view 
    class="content-list" 
    scroll-y="true" 
    enable-back-to-top="true"
    bindscrolltolower="loadMore"
    refresher-enabled="{{true}}"
    refresher-triggered="{{refreshing}}"
    bindrefresherrefresh="onPullDownRefresh"
  >
    <!-- 收藏内容 -->
    <view class="list-content" wx:if="{{listData.length > 0}}">
      <navigator 
        wx:for="{{listData}}" 
        wx:key="id"
        url="../workorder/show?id={{item.id}}" 
        hover-class="card-hover"
      >
        <view class="card {{index % 2 === 0 ? 'card-odd' : 'card-even'}}">
          <view class="card-content">
            <view class="card-title">{{item.title}}</view>
            <view class="card-meta">
              <view class="meta-item">
                <image class="meta-icon" src="../../icons/time.png"/>
                <text class="meta-text">{{item.inputtime}}</text>
              </view>
              <view class="meta-item">
                <image class="meta-icon" src="../../icons/see.png"/>
                <text class="meta-text">{{item.hits || 0}}</text>
              </view>
              <view class="meta-item">
                <image class="meta-icon" src="../../icons/message.png"/>
                <text class="meta-text">{{item.comments || 0}}</text>
              </view>
            </view>
          </view>
        </view>
      </navigator>
    </view>

    <!-- 空状态提示 -->
    <view class="empty-state" wx:if="{{listData.length === 0 && !loading}}">
      <image class="empty-icon" src="../../icons/empty.png" mode="aspectFit"/>
      <text class="empty-text">暂无收藏内容</text>
      <text class="empty-tip">浏览文章时点击收藏按钮添加</text>
    </view>

    <!-- 加载状态 -->
    <view class="loading-state" wx:if="{{loading}}">
      <view class="loading-content">
        <image class="loading-icon" src="../../icons/loading.gif"/>
        <text class="loading-text">加载中...</text>
      </view>
    </view>
    
    <!-- 无更多数据 -->
    <view class="no-more" wx:if="{{!hasMore && listData.length > 0 && !loading}}">
      <text class="no-more-text">没有更多内容了</text>
    </view>
  </scroll-view>
</view>