<view class="container">
  <!-- 顶部背景 -->
  <view class="top-bg"></view>
  
  <!-- 用户信息卡片 -->
  <view class="user-card" bindtap="userInfo">
    <view class="user-info-content">
      <image src="{{avatar}}" class="user-avatar" mode="aspectFill"></image>
      <view class="user-details">
        <view class="user-name" wx:if="{{member}}">
          <text class="username">{{member.name}}</text>
          
          <text class="user-role" wx:if="{{member.is_admin && member.is_admin > 0}}">超级管理员</text>
          <text class="user-role" wx:else>普通会员</text>
        </view>
        <view class="user-name" wx:else>
          <text class="username">点击登录</text>
          <text class="login-tip">登录后享受更多权益</text>
        </view>
        <!-- 会员状态标识 -->
        <view class="member-status" wx:if="{{member}}">
        <text class="user-role">{{member.username}}</text>
          <view class="status-badge verify {{member.is_verify == 1 ? 'active' : ''}}">
            <text>已认证</text>
          </view>
          <view class="status-badge mobile {{member.is_mobile == 1 ? 'active' : ''}}">
            <text>手机</text>
          </view>
          <view class="status-badge email {{member.is_email == 1 ? 'active' : ''}}">
            <text>邮箱</text>
          </view>
          <view class="status-badge avatar {{member.is_avatar == 1 ? 'active' : ''}}">
            <text>头像</text>
          </view>
          <view class="status-badge complete {{member.is_complete == 1 ? 'active' : ''}}">
            <text>资料</text>
          </view>
          <view class="status-badge lock {{member.is_lock == 1 ? 'active' : ''}}">
            <text>{{member.is_lock == 1 ? '已锁定' : '正常'}}</text>
          </view>
        </view>
      </view>
      <view class="action-arrow">
        <image src="../../icons/right-back.png" class="arrow-icon"></image>
      </view>
    </view>
    
    <!-- 账户数据 -->
    <view class="account-stats" wx:if="{{member}}">
      <view class="account-stats-row">
        <view class="stat-item">
          <text class="stat-value">{{ssczName}}</text>
          <text class="stat-label">所属村组</text>
        </view>
        <view class="stat-divider"></view>
        <view class="stat-item">
          <text class="stat-value">{{member.score || 0}}</text>
          <text class="stat-label">积分</text>
        </view>
      </view>
      <view class="account-stats-divider"></view>
      <view class="account-stats-row">
        <view class="stat-item">
          <text class="stat-value">¥{{member.money || '0.00'}}</text>
          <text class="stat-label">余额</text>
        </view>
        <view class="stat-divider"></view>
        <view class="stat-item">
          <text class="stat-value">{{member.exp || 0}}</text>
          <text class="stat-label">经验</text>
        </view>
      </view>
    </view>
  </view>
  
  <!-- 功能区块 -->
  <view class="section-title">我的服务</view>
  
  <!-- 常用功能快捷入口 -->
  <view class="quick-actions">
    <navigator class="quick-action-item" url="../member/my">
      <view class="quick-icon-wrapper">
        <image src="../../icons/user-on.png" class="quick-icon"></image>
      </view>
      <text class="quick-text">账号信息</text>
    </navigator>
    <navigator class="quick-action-item" url="../member/notice">
      <view class="quick-icon-wrapper">
        <image src="../../icons/message.png" class="quick-icon"></image>
      </view>
      <text class="quick-text">我的消息</text>
    </navigator>
    <navigator class="quick-action-item" url="../member/workorder">
      <view class="quick-icon-wrapper">
        <image src="../../icons/pencil.png" class="quick-icon"></image>
      </view>
      <text class="quick-text">我的工单</text>
    </navigator>
    <navigator class="quick-action-item" url="../member/favorite">
      <view class="quick-icon-wrapper">
        <image src="../../icons/star.png" class="quick-icon"></image>
      </view>
      <text class="quick-text">我的收藏</text>
    </navigator>
  </view>

  <!-- 菜单列表 -->
  <view class="menu-list">
    <view class="menu-section">
      <view class="menu-title">账号管理</view>
      <navigator url="../member/account" hover-class="menu-item-hover">
        <view class="menu-item">
          <view class="menu-item-left">
            <image src="../../icons/user.png" class="menu-icon"></image>
            <text class="menu-text">修改资料</text>
          </view>
          <image src="../../icons/right-back.png" class="arrow-icon"></image>
        </view>
      </navigator>
      <navigator url="../member/password" hover-class="menu-item-hover">
        <view class="menu-item">
          <view class="menu-item-left">
            <image src="../../icons/password.png" class="menu-icon"></image>
            <text class="menu-text">修改密码</text>
          </view>
          <image src="../../icons/right-back.png" class="arrow-icon"></image>
        </view>
      </navigator>
    </view>

    <view class="menu-section">
      <view class="menu-title">财务管理</view>
      <navigator url="../member/recharge" hover-class="menu-item-hover">
        <view class="menu-item">
          <view class="menu-item-left">
            <image src="../../icons/order.png" class="menu-icon"></image>
            <text class="menu-text">账户充值</text>
          </view>
          <image src="../../icons/right-back.png" class="arrow-icon"></image>
        </view>
      </navigator>
      <navigator url="../member/paylog" hover-class="menu-item-hover">
        <view class="menu-item">
          <view class="menu-item-left">
            <image src="../../icons/post.png" class="menu-icon"></image>
            <text class="menu-text">我的交易</text>
          </view>
          <image src="../../icons/right-back.png" class="arrow-icon"></image>
        </view>
      </navigator>
      <navigator url="../member/scorelog" hover-class="menu-item-hover">
        <view class="menu-item">
          <view class="menu-item-left">
            <image src="../../icons/order.png" class="menu-icon"></image>
            <text class="menu-text">积分记录</text>
          </view>
          <image src="../../icons/right-back.png" class="arrow-icon"></image>
        </view>
      </navigator>
    </view>

    <view class="menu-section">
      <view class="menu-title">互动管理</view>
      <navigator url="../member/comment" hover-class="menu-item-hover">
        <view class="menu-item">
          <view class="menu-item-left">
            <image src="../../icons/comment.png" class="menu-icon"></image>
            <text class="menu-text">我的评论</text>
          </view>
          <image src="../../icons/right-back.png" class="arrow-icon"></image>
        </view>
      </navigator>
      <navigator url="../wenda/index" hover-class="menu-item-hover">
        <view class="menu-item">
          <view class="menu-item-left">
            <image src="../../icons/message.png" class="menu-icon"></image>
            <text class="menu-text">问答论坛</text>
          </view>
          <image src="../../icons/right-back.png" class="arrow-icon"></image>
        </view>
      </navigator>
      <navigator url="../member/jifenduihuan" hover-class="menu-item-hover">
        <view class="menu-item">
          <view class="menu-item-left">
            <image src="../../icons/star.png" class="menu-icon"></image>
            <text class="menu-text">积分兑换</text>
          </view>
          <image src="../../icons/right-back.png" class="arrow-icon"></image>
        </view>
      </navigator>
      <navigator url="../cmda/index" hover-class="menu-item-hover" wx:if="{{member.is_admin && member.is_admin > 0}}">
        <view class="menu-item">
          <view class="menu-item-left">
            <image src="../../icons/lx.png" class="menu-icon"></image>
            <text class="menu-text">村民档案</text>
          </view>
          <image src="../../icons/right-back.png" class="arrow-icon"></image>
        </view>
      </navigator>
    </view>
  </view>

  <!-- 退出登录按钮 -->
  <view bindtap="downLogin" class="logout-btn" wx:if="{{member}}">
    退出登录
  </view>
  
  <!-- 版本号 -->
  <view class="version-info">
    <text>当前版本 v1.0.0</text>
  </view>
</view>
