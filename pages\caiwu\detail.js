var app = getApp();

Page({
  data: {
    isAdmin: false,
    isLoggedIn: false,
    id: null,
    recordData: null,
    loading: true
  },

  onLoad: function(options) {
    if (options.id) {
      this.setData({
        id: options.id
      });
      this.checkAdminPermission();
    } else {
      wx.showToast({
        title: '参数错误',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }
  },

  // 检查管理员权限
  checkAdminPermission: function() {
    try {
      // 获取当前用户信息
      const member = wx.getStorageSync('member');
      if (member && member.id) {
        // 判断是否是管理员，is_admin > 0 表示是管理员
        const isAdmin = member.is_admin && parseInt(member.is_admin) > 0;
        this.setData({
          isAdmin: isAdmin,
          isLoggedIn: true
        });

        // 如果不是管理员，提示无权访问并返回
        if (!isAdmin) {
          wx.showModal({
            title: '无权访问',
            content: '只有管理员才能查看财务详情',
            showCancel: false,
            success: (res) => {
              wx.navigateBack();
            }
          });
        } else {
          // 加载财务记录详情
          this.loadRecordDetail();
        }
      } else {
        this.setData({
          isAdmin: false,
          isLoggedIn: false
        });
        
        // 未登录，提示登录并返回
        wx.showModal({
          title: '请先登录',
          content: '您需要登录并拥有管理员权限才能查看财务详情',
          showCancel: false,
          success: (res) => {
            wx.navigateBack();
          }
        });
      }
    } catch (err) {
      wx.showToast({
        title: '系统错误，请重试',
        icon: 'none'
      });
    }
  },

  // 加载财务记录详情
  loadRecordDetail: function() {
    const id = this.data.id;
    if (!id) return;
    
    this.setData({ loading: true });
    
    // 获取用户认证信息
    const member_auth = wx.getStorageSync('member_auth');
    const member_uid = wx.getStorageSync('member_uid');
    
    wx.showLoading({
      title: '加载中...',
      mask: true
    });
    
    // 请求财务记录详情
    wx.request({
      url: 'https://p.hnzbz.net/index.php',
      method: 'GET',
      data: {
        s: 'caiwu',
        c: 'show',
        id: id,
        api_call_function: 'module_show',
        appid: app.globalData.appid,
        appsecret: app.globalData.appsecret,
        member_auth: member_auth,
        member_uid: member_uid,
        v: '1'
      },
      success: (res) => {
        wx.hideLoading();
        
        if (res.data && res.data.code == 1 && res.data.data) {
          // 处理返回的数据，格式化为界面需要的格式
          const data = res.data.data;
          
          // 处理凭证图片数据
          let pingzhengUrls = [];
          if (data.pingzheng && data.pingzheng.length > 0) {
            pingzhengUrls = data.pingzheng.map(item => item.file);
          }
          
          const recordData = {
            id: data.id,
            title: data.title,
            type: data.szxm === '收入' ? 'income' : 'expense',
            amount: data.money || '0.00',
            create_time: data.inputtime || '未知',
            creator_name: data.author || '未知',
            update_time: data.riqi || '',
            remark: data.szxm === '收入' ? data.shouruleibie : data.zhichuleibie,
            description: data.description,
            pingzheng: data.pingzheng || [],
            pingzhengUrls: pingzhengUrls,
            payment_method: data.fkfs || '',
            receipt_method: data.skfs || ''
          };
          
          this.setData({
            recordData: recordData,
            loading: false
          });
        } else {
          wx.showToast({
            title: res.data.msg || '加载失败',
            icon: 'none'
          });
          setTimeout(() => {
            wx.navigateBack();
          }, 1500);
        }
      },
      fail: (err) => {
        wx.hideLoading();
        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        });
      }
    });
  },
  
  // 预览凭证图片
  previewImage: function(e) {
    const url = e.currentTarget.dataset.url;
    const urls = e.currentTarget.dataset.urls;
    
    wx.previewImage({
      current: url, // 当前显示图片的链接
      urls: urls // 需要预览的图片链接列表
    });
  },
  
  // 导出凭证
  printRecord: function() {
    if (!this.data.recordData) return;
    
    // 直接复制凭证信息到剪贴板
    this.copyVoucherInfo();
  },
  
  // 复制凭证信息到剪贴板
  copyVoucherInfo: function() {
    if (!this.data.recordData) return;
    
    const data = this.data.recordData;
    const voucherText = 
      `凭证编号: ${data.id}\n` +
      `标题: ${data.title || '未命名'}\n` + 
      `类型: ${data.type === 'income' ? '收入' : '支出'}\n` +
      `分类: ${data.remark || '未分类'}\n` +
      `金额: ¥${data.amount}\n` +
      (data.type === 'income' ? 
        (data.receipt_method ? `收款方式: ${data.receipt_method}\n` : '') :
        (data.payment_method ? `付款方式: ${data.payment_method}\n` : '')) +
      `创建时间: ${data.create_time}\n` +
      (data.description ? `备注: ${data.description}\n` : '') +
      `凭证附件数量: ${data.pingzheng ? data.pingzheng.length : 0}个`;
    wx.setClipboardData({
      data: voucherText,
      success: () => {
        wx.showToast({
          title: '凭证信息已复制',
          icon: 'success',
          duration: 2000
        });
      },
      fail: () => {
        wx.showToast({
          title: '复制失败',
          icon: 'none',
          duration: 2000
        });
      }
    });
  },
  
  // 返回上一页
  goBack: function() {
    wx.navigateBack();
  }
}) 