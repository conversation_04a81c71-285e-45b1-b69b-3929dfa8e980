const app = getApp();

Page({
  data: {
    statsData: {},
    totalCount: 0,
    totalOptions: 0,
    statsList: [],
    loading: true
  },

  onLoad: function(options) {
    // 无论如何都直接从API加载最新数据
    this.loadStatsData();
    
    // 旧代码保留但不再使用，以防配合旧版本index.js
    if (options && options.data) {
      try {
        console.log('接收到数据参数，但将使用API加载最新数据');
      } catch (e) {
        console.error('解析统计数据出错:', e);
      }
    }
  },

  // 处理统计数据
  processStatsData: function(statsData) {
    // 检查是否为新的数据格式
    if (statsData.summary && Array.isArray(statsData.data)) {
      // 新的数据格式处理
      const totalCount = statsData.summary.total_records || 0;
      const totalOptions = statsData.summary.total_options || 0;
      
      // 创建列表数据
      const statsList = [];
      
      // 创建兼容旧格式的statsData对象
      const compatStatsData = {'总计': totalCount};
      
      statsData.data.forEach(item => {
        if (item.name && item.count !== undefined) {
          // 添加到兼容对象
          compatStatsData[item.name] = item.count;
          
          // 添加到列表
          statsList.push({
            name: item.name,
            count: item.count,
            color: this.getTypeColor(item.name),
            percentage: item.percent ? parseFloat(item.percent) : 0,
            percentText: item.percent || '0%'
          });
        }
      });
      
      // 按照数量从大到小排序
      statsList.sort((a, b) => b.count - a.count);
      
      this.setData({
        loading: false,
        statsData: compatStatsData, // 使用兼容对象
        totalCount: totalCount,
        totalOptions: totalOptions,
        statsList: statsList
      });
    } else {
      // 旧的数据格式处理
      const totalCount = statsData['总计'] || 0;
      // 计算分类总数
      let totalOptions = 0;
      for (const key in statsData) {
        if (key !== '总计' && statsData.hasOwnProperty(key)) {
          totalOptions++;
        }
      }
      
      // 创建列表数据
      const statsList = [];
      for (const key in statsData) {
        if (key !== '总计' && statsData.hasOwnProperty(key)) {
          const percent = totalCount > 0 ? (statsData[key] / totalCount * 100).toFixed(1) : 0;
          statsList.push({
            name: key,
            count: statsData[key],
            color: this.getTypeColor(key),
            percentage: percent,
            percentText: percent + '%'
          });
        }
      }
      
      // 按照数量从大到小排序
      statsList.sort((a, b) => b.count - a.count);
      
      this.setData({
        loading: false,
        statsData: statsData,
        totalCount: totalCount,
        totalOptions: totalOptions,
        statsList: statsList
      });
    }
  },
  
  // 从API加载统计数据
  loadStatsData: function() {
    wx.showLoading({ title: '加载中...' });
    
    // 获取验证信息
    var member_uid = wx.getStorageSync('member_uid');
    var member_auth = wx.getStorageSync('member_auth');
    
    if (!member_uid || !member_auth) {
      wx.hideLoading();
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }
    
    // 构建API请求URL
    const requestUrl = app.globalData.http_api + 
      's=httpapi&id=10' +
      '&appid=' + app.globalData.appid +
      '&appsecret=' + app.globalData.appsecret;
    
    wx.request({
      url: requestUrl,
      method: 'GET',
      header: {
        'content-type': 'application/json'
      },
      success: res => {
        wx.hideLoading();
        
        if (res.data.code == 1 && res.data.data) {
          this.processStatsData(res.data.data);
        } else {
          wx.showToast({
            title: res.data.msg || '获取统计数据失败',
            icon: 'none'
          });
          this.setData({ loading: false });
        }
      },
      fail: err => {
        console.error('请求统计数据失败:', err);
        wx.hideLoading();
        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        });
        this.setData({ loading: false });
      }
    });
  },
  
  // 为不同类型分配颜色
  getTypeColor: function(key) {
    // 使用固定的颜色列表
    const colors = [
      '#E74C3C', // 红色
      '#9B59B6', // 紫色
      '#3498DB', // 蓝色
      '#1ABC9C', // 青绿色
      '#2ECC71', // 绿色
      '#F1C40F', // 黄色
      '#E67E22', // 橙色
      '#34495E', // 深蓝色
      '#16A085', // 深青色
      '#7F8C8D', // 灰色
      '#D35400', // 深橙色
      '#27AE60', // 浅绿色
      '#8E44AD', // 深紫色
      '#2980B9', // 深蓝色
      '#F39C12'  // 橙黄色
    ];
    
    // 如果还没有生成颜色映射，则创建一个
    if (!this.colorMapping) {
      this.colorMapping = {};
      this.colorIndex = 0;
    }
    
    // 如果该类型还没有分配颜色，则分配一个
    if (!this.colorMapping[key]) {
      this.colorMapping[key] = colors[this.colorIndex % colors.length];
      this.colorIndex++;
    }
    
    return this.colorMapping[key];
  },
  
  // 查看某类型的详情列表
  viewTypeDetail: function(e) {
    const type = e.currentTarget.dataset.type;
    if (!type) return;
    
    console.log('statsDetail页面点击类型:', type);
    
    // 使用hujishuxing作为直接参数名称
    const url = './list?type=' + encodeURIComponent(type) + '&field=hujishuxing&hujishuxing=' + encodeURIComponent(type);
    console.log('跳转URL:', url);
    
    wx.redirectTo({
      url: url,
      success: function() {
        console.log('跳转到列表页成功，筛选类型:', type);
      },
      fail: function(err) {
        console.error('跳转失败:', err);
        // 如果redirectTo也失败，尝试使用switchTab或reLaunch
        wx.showToast({
          title: '页面跳转失败，请返回首页重试',
          icon: 'none',
          duration: 2000
        });
      }
    });
  },
  
  // 下拉刷新
  onPullDownRefresh: function() {
    this.loadStatsData();
    wx.stopPullDownRefresh();
  }
}); 