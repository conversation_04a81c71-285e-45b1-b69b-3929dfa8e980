var WxParse = require('../../wxParse/wxParse.js');

var app = getApp();
var http_url = app.globalData.http_api + "s=workorder&c=show";
http_url+= '&api_call_function=module_show';

// 获取最新的认证URL
function getAuthUrl(baseUrl) {
  const member_auth = wx.getStorageSync('member_auth');
  const member_uid = wx.getStorageSync('member_uid');
  return baseUrl + "&api_auth_code=" + member_auth + "&api_auth_uid=" + member_uid;
}

// 评论相关URL基础部分
var comment_url = app.globalData.http_api + "s=workorder&c=comment";
comment_url += '&api_call_function=module_comment_list';
var comment_save_base_url = app.globalData.http_api + "s=workorder&m=post&c=comment";
var member_base_url = app.globalData.http_api + "s=api&app=workorder&c=module";

// 评论框图片上传URL
var upload_url = app.globalData.http_api + "s=api&c=file&m=ueditor&action=uploadimage&encode=utf-8";

// 配置常量
const CONFIG = {
  http_url: http_url,
  comment_url: comment_url,
  comment_save_base_url: comment_save_base_url,
  member_base_url: member_base_url,
  upload_url: upload_url
};

Page({
  data:{
    id:'',
    content:'',
    supports: 0,
    upsImg:"../../icons/ups.png",
    collectImg:"../../icons/collect.png",
    commentList: [], // 评论列表
    commentText: '', // 评论内容
    isLoading: false, // 是否正在加载
    hasMore: true, // 是否还有更多评论
    page: 1, // 当前页码
    tempImagePaths: [], // 临时图片路径数组
    uploadedImages: [], // 已上传的图片URL数组
    member_uid: wx.getStorageSync('member_uid'), // 当前登录用户ID
    member: wx.getStorageSync('member'), // 将 member 对象添加到 data 中
    isRefreshing: false, // 是否正在刷新
    lastCommentTime: 0, // 上次评论时间
    commentInterval: 60, // 评论间隔时间（秒）
    isCommentBarShow: false, // 控制评论框显示状态
    isActionBarHide: false, // 控制底部操作栏显示状态
    maxImageCount: 6 //这里可与后台图片字段限制的数量保持一致
  },

  onLoad:function(options){
    if (!options.id) {
      wx.showToast({
        title: '参数错误',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
      return;
    }

    this.setData({ 
      id: options.id,
      member: wx.getStorageSync('member'),
      member_uid: wx.getStorageSync('member_uid')
    });
    
    // 先获取工单内容，判断是否有权限查看
    this.loadContentWithPermissionCheck();
  },

  onShow: function() {
    // 每次页面显示时更新用户信息
    this.setData({
      member: wx.getStorageSync('member'),
      member_uid: wx.getStorageSync('member_uid')
    });
  },

  // 加载初始数据
  loadInitialData: function() {
    // 这个函数现在已经不再使用，保留函数体便于维护
    console.log('loadInitialData弃用，已更换为loadContentWithPermissionCheck');
  },

  // 加载内容并检查权限
  loadContentWithPermissionCheck: function() {
    wx.showLoading({
      title: '加载中...',
      mask: true
    });
    
    // 先获取内容，检查是否为保密工单
    wx.request({
      url: CONFIG.http_url,
      data: { id: this.data.id },
      header: { 'content-type': 'application/json' },
      dataType: 'json',
      method: 'GET',
      success: (res) => {
        wx.hideLoading();
        
        if (res.data.code == 1) {
          const content = res.data.data;
          
          // 检查工单数据格式和头像
          console.log('工单数据:', content);
          
          // 修复缺失的头像字段
          if (!content.avatar) {
            console.log('工单作者缺少头像字段');
            
            // 查询member表获取头像 - 优先作者ID
            const authorId = content.uid || content.userid;
            if (authorId) {
              console.log('根据作者ID尝试获取头像:', authorId);
              this.tryLoadAuthorAvatar(authorId, content);
            }
          }
          
          // 检查是否为保密工单，以及当前用户是否有权限查看
          if (content.gongkai === '保密') {
            const member = this.data.member;
            const isLoggedIn = member && member.id;
            const isAdmin = isLoggedIn && member.is_admin && parseInt(member.is_admin) > 0;
            const isAuthor = isLoggedIn && (member.id == content.userid || member.id == content.uid);
            
            // 如果是保密工单，但用户没有权限查看（非管理员且非作者）
            if (!isLoggedIn || (!isAdmin && !isAuthor)) {
              wx.showModal({
                title: '无权访问',
                content: '该工单为保密工单，您没有权限查看',
                showCancel: false,
                success: () => {
                  wx.navigateBack();
                }
              });
              return;
            }
          }
          
          // 有权限，继续加载数据
          WxParse.wxParse('data', 'html', content.content, this);
          this.setData({ content: content });
          
          // 继续加载其他数据
          Promise.all([
            this.loadSupportStatus(),
            this.checkFavoriteStatus()
          ]).then(() => {
            this.loadComments();
          }).catch(error => {
            this.handleError(error);
          });
        } else {
          this.handleError(new Error(res.data.msg || '加载失败'));
        }
      },
      fail: (err) => {
        wx.hideLoading();
        this.handleError(err);
      }
    });
  },

  // 尝试加载作者头像
  tryLoadAuthorAvatar: function(authorId, content) {
    // 这里可以添加API调用来获取用户头像
    // 简化处理，仅记录日志
    console.log('尝试获取用户ID为 ' + authorId + ' 的头像');
  },

  // 加载点赞状态
  loadSupportStatus: function() {
    return new Promise((resolve, reject) => {
      // 先检查本地存储的点赞状态
      const likeKey = `like_${this.data.id}`;
      const isLiked = wx.getStorageSync(likeKey);
      
      if (isLiked) {
        this.setData({ 
          upsImg: "../../icons/ups-active.png"
        });
      }

      wx.request({
        url: app.globalData.http_api + "s=zan&mid=workorder&id=" + this.data.id,
        header: { 'content-type': 'application/json' },
        dataType: 'json',
        method: 'GET',
        success: (res) => {
          if (res.data.code == 1) {
            this.setData({ supports: res.data.data.b });
          }
          resolve();
        },
        fail: reject
      });
    });
  },

  // 加载内容
  loadContent: function() {
    // 这个函数现在已经不再使用，保留函数体便于维护
    console.log('loadContent弃用，已整合到loadContentWithPermissionCheck');
    return Promise.resolve();
  },

  // 检查收藏状态
  checkFavoriteStatus: function() {
    return new Promise((resolve, reject) => {
      wx.request({
        url: getAuthUrl(CONFIG.member_base_url) + '&m=is_favorite',
        data: { id: this.data.id },
        header: { 'content-type': 'application/json' },
        dataType: 'json',
        method: 'GET',
        success: (res) => {
          if (res.data.code == 1) {
            this.setData({ collectImg: "../../icons/collect-active.png" });
          }
          resolve();
        },
        fail: reject
      });
    });
  },

  // 加载评论
  loadComments: function() {
    if (this.data.isLoading || (!this.data.hasMore && !this.data.isRefreshing)) return;
    
    this.setData({ isLoading: true });
    
    wx.request({
      url: CONFIG.comment_url,
      data: {
        id: this.data.id,
        page: this.data.page
      },
      header: { 'content-type': 'application/json' },
      dataType: 'json',
      method: 'GET',
      success: (res) => {
        if (res.data.code == 1) {
          let comments = res.data.data;
          if (!Array.isArray(comments)) {
            if (comments && comments.list) {
              comments = comments.list;
            } else {
              comments = [];
            }
          }

          const newComments = comments.map(comment => {
            const uniqueKey = `${comment.id || ''}_${comment.inputtime || ''}_${Date.now()}`;
            const nodeName = `comment_${uniqueKey}`;
            
            // 处理评论内容，确保图片有统一样式
            let content = comment.content || '';
            
            // 简化处理：将所有图片提取出来，然后用新的容器和样式替换
            if (content.includes('<img')) {
              // 提取出所有图片标签
              const imgRegex = /<img[^>]*>/g;
              const imgTags = content.match(imgRegex) || [];
              const imgCount = imgTags.length;
              
              if (imgCount > 0) {
                // 移除原始图片标签
                content = content.replace(imgRegex, '');
                
                // 处理可能的空段落
                content = content.replace(/<p>\s*<\/p>/g, '');
                
                // 构建新的图片HTML
                let imagesHtml = '';
                if (imgCount === 1) {
                  // 单张图片使用单独处理
                  imagesHtml = imgTags[0].replace('<img', '<img class="wxParse-img wxParse-img-single"');
                } else {
                  // 多张图片
                  imagesHtml = imgTags.map(img => {
                    if (imgCount === 2) {
                      return img.replace('<img', '<img class="wxParse-img wxParse-img-double"');
                    } else {
                      return img.replace('<img', '<img class="wxParse-img"');
                    }
                  }).join('');
                  
                  // 添加计数
                  imagesHtml += `<div class="image-count-indicator">共${imgCount}张</div>`;
                }
                
                // 将处理后的图片追加到内容后面
                content += `<div class="comment-images-container wxParse-p-with-img">${imagesHtml}</div>`;
              }
            }
            
            // 解析处理后的内容
            WxParse.wxParse(nodeName, 'html', content, this, 5);
            
            return {
              ...comment,
              uniqueKey,
              parsedContent: this.data[nodeName]
            };
          });

          this.setData({
            commentList: this.data.page === 1 ? newComments : [...this.data.commentList, ...newComments],
            page: this.data.page + 1,
            hasMore: newComments.length > 0,
            isLoading: false
          });
        } else {
          this.setData({ isLoading: false });
          this.handleError(new Error(res.data.msg || '加载评论失败'));
        }
      },
      fail: (error) => {
        this.setData({ isLoading: false });
        this.handleError(error);
      }
    });
  },

  // 点赞
  up: function() {
    if (this.data.isLoading) return;
    
    wx.showLoading({
      title: '点赞中...',
      mask: true
    });
    
    wx.request({
      url: app.globalData.http_api + "s=api&app=workorder&c=module&m=digg&id=" + this.data.id + "&value=1",
      header: { 'content-type': 'application/json' },
      dataType: 'json',
      method: 'GET',
      success: (res) => {
        wx.hideLoading();
        
        if (res.data.code == 1) {
          // 保存点赞状态到本地存储
          const likeKey = `like_${this.data.id}`;
          wx.setStorageSync(likeKey, true);
          
          this.setData({
            supports: res.data.data,
            upsImg: "../../icons/ups-active.png"
          });
          
          wx.showToast({
            icon: 'success',
            title: res.data.msg || '点赞成功',
            duration: 2000
          });
          
          this.loadSupportStatus();
        } else {
          this.handleError(new Error(res.data.msg || '点赞失败'));
        }
      },
      fail: (error) => {
        wx.hideLoading();
        this.handleError(error);
      }
    });
  },

  // 收藏
  collect: function() {
    if (this.data.isLoading) return;
    
    // 检查用户是否登录
    const member_auth = wx.getStorageSync('member_auth');
    const member_uid = wx.getStorageSync('member_uid');
    const member = wx.getStorageSync('member');
    
    if (!member_auth || !member_uid || !member) {
      wx.showModal({
        title: '提示',
        content: '收藏需要登录，是否前往登录？',
        success: (res) => {
          if (res.confirm) {
            wx.navigateTo({
              url: '/pages/login/login'
            });
          }
        }
      });
      return;
    }
    
    wx.request({
      url: getAuthUrl(CONFIG.member_base_url) + '&m=favorite',
      data: { id: this.data.id },
      header: { 'content-type': 'application/json' },
      dataType: 'json',
      method: 'GET',
      success: (res) => {
        if (res.data.code == 1) {
          wx.showToast({
            icon: 'success',
            title: res.data.msg,
            duration: 2000
          });
          
          this.setData({
            collectImg: res.data.msg == '收藏成功' ? 
              "../../icons/collect-active.png" : 
              "../../icons/collect.png"
          });
        } else {
          wx.showToast({
            title: res.data.msg || '操作失败',
            icon: 'none'
          });
        }
      },
      fail: (error) => {
        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        });
      }
    });
  },

  // 获取评论输入内容
  getText: function(e) {
    this.setData({
      commentText: e.detail.value
    });
  },

  // 选择图片上传
  chooseImage: function() {
    const self = this;
    const count = this.data.maxImageCount - this.data.tempImagePaths.length;
    
    if (count <= 0) {
      wx.showToast({
        title: '最多上传' + this.data.maxImageCount + '张图片',
        icon: 'none'
      });
      return;
    }
    
    wx.chooseImage({
      count: count,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: function(res) {
        // 追加新图片
        let newImages = self.data.tempImagePaths.concat(res.tempFilePaths);
        // 确保不超过最大图片数量
        if (newImages.length > self.data.maxImageCount) {
          newImages = newImages.slice(0, self.data.maxImageCount);
        }
        self.setData({
          tempImagePaths: newImages
        });
      }
    });
  },

  // A辅助方法：检查图片尺寸
  checkImageSize: function(filePath) {
    return new Promise((resolve) => {
      wx.getImageInfo({
        src: filePath,
        success: (res) => {
          // 图片过大时进行压缩
          if (res.width > 1200 || res.height > 1200) {
            wx.showToast({
              title: '压缩图片中...',
              icon: 'loading',
              duration: 1000
            });
            // 返回需要压缩的信息
            resolve({
              needCompress: true,
              width: res.width,
              height: res.height
            });
          } else {
            resolve({ needCompress: false });
          }
        },
        fail: () => {
          resolve({ needCompress: false });
        }
      });
    });
  },

  // 上传图片
  uploadImage: function(filePath) {
    return new Promise(async (resolve, reject) => {
      // 检查图片尺寸
      const sizeInfo = await this.checkImageSize(filePath);
      
      // 如果需要压缩
      let uploadPath = filePath;
      if (sizeInfo.needCompress) {
        try {
          // 计算压缩后的尺寸，保持宽高比
          const ratio = sizeInfo.width / sizeInfo.height;
          let newWidth, newHeight;
          
          if (sizeInfo.width > sizeInfo.height) {
            newWidth = 1200;
            newHeight = newWidth / ratio;
          } else {
            newHeight = 1200;
            newWidth = newHeight * ratio;
          }
          
          // 压缩图片
          const compressRes = await wx.compressImage({
            src: filePath,
            quality: 80,
            compressedWidth: newWidth,
            compressedHeight: newHeight
          });
          
          uploadPath = compressRes.tempFilePath;
        } catch (error) {
          console.error('压缩失败，使用原图：', error);
          // 压缩失败使用原图
        }
      }
      
      // 上传图片
      wx.uploadFile({
        url: CONFIG.upload_url,
        filePath: uploadPath,
        name: 'upfile',
        success: (res) => {
          try {
            const data = JSON.parse(res.data);
            if (data.state === 'SUCCESS') {
              resolve(data.url);
            } else {
              reject(new Error(data.state || '上传失败'));
            }
          } catch (e) {
            reject(new Error('解析返回数据失败'));
          }
        },
        fail: (error) => {
          reject(error);
        }
      });
    });
  },

  // 保存评论
  saveComment: function() {
    if (this.data.isLoading) return;
    
    // 检查评论时间间隔
    const now = Math.floor(Date.now() / 1000);
    const timeSinceLastComment = now - this.data.lastCommentTime;
    if (timeSinceLastComment < this.data.commentInterval) {
      const remainingTime = this.data.commentInterval - timeSinceLastComment;
      wx.showToast({
        title: `请等待${remainingTime}秒后再评论`,
        icon: 'none',
        duration: 2000
      });
      return;
    }

    const commentText = this.data.commentText.trim();
    if (!commentText && this.data.tempImagePaths.length === 0) {
      wx.showToast({
        title: '请输入评论内容或上传图片',
        icon: 'none'
      });
      return;
    }

    if (!this.data.id) {
      wx.showToast({
        title: '参数错误',
        icon: 'none'
      });
      return;
    }

    // 检查用户是否登录
    const member_auth = wx.getStorageSync('member_auth');
    const member_uid = wx.getStorageSync('member_uid');
    const member = wx.getStorageSync('member');
    
    if (!member_auth || !member_uid || !member) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }

    // 检查用户权限
    const isAdmin = (member.is_admin && parseInt(member.is_admin) > 0) || 
                   (member.authid && Array.isArray(member.authid) && member.authid.includes(1)) ||
                   (member.adminid && parseInt(member.adminid) > 0) ||
                   (member.group && member.group[1] && member.group[1].group_name === '管理员') ||
                   member.uid === this.data.content.uid ||
                   member.id === this.data.content.uid;

    if (!isAdmin) {
      wx.showToast({
        title: '您没有发表评论的权限',
        icon: 'none'
      });
      return;
    }

    this.setData({ isLoading: true });
    
    // 上传图片
    Promise.all(this.data.tempImagePaths.map(path => this.uploadImage(path)))
      .then(uploadedUrls => {
        // 构建评论内容
        let content = commentText;
        if (uploadedUrls.length > 0) {
          // 将文本和图片分开处理
          if (content) {
            // 如果有文本，保留原文本
            content = `<div class="comment-text-content">${content}</div>`;
          }
          
          // 构建图片HTML
          let imagesHtml = '';
          if (uploadedUrls.length === 1) {
            // 单张图片使用单独样式
            imagesHtml = `<img src="${uploadedUrls[0]}" class="wxParse-img wxParse-img-single">`;
          } else {
            // 多张图片处理
            imagesHtml = uploadedUrls.map(url => {
              if (uploadedUrls.length === 2) {
                return `<img src="${url}" class="wxParse-img wxParse-img-double">`;
              } else {
                return `<img src="${url}" class="wxParse-img">`;
              }
            }).join('');
            
            // 添加图片计数
            imagesHtml += `<div class="image-count-indicator">共${uploadedUrls.length}张</div>`;
          }
          
          // 添加图片容器并追加到内容
          content += `<div class="comment-images-container wxParse-p-with-img">${imagesHtml}</div>`;
        }

        // 发送评论
        return new Promise((resolve, reject) => {
          // 确保用户信息正确获取
          const member_uid = wx.getStorageSync('member_uid');
          const member = wx.getStorageSync('member');
          
          // 构建请求数据，确保用户ID和用户名正确设置
          const requestData = {
            id: this.data.id,
            content: content,
            module: 'workorder',
            mid: 'workorder',
            cid: this.data.id,
            uid: member_uid,
            cuid: this.data.content.uid, // 关联uid，即工单创建者的uid
            author: member.username || '匿名用户', // 评论者账号，使用username填充
            pinglunzhe: member_uid, // 评论者字段
            status: 1
          };
          
          wx.request({
            url: getAuthUrl(CONFIG.comment_save_base_url) + '&id=' + this.data.id,
            data: requestData,
            header: { 
              'content-type': 'application/json'
            },
            dataType: 'json',
            method: 'POST',
            success: (res) => {
              if (res.data.code == 1) {
                // 更新最后评论时间
                this.setData({
                  lastCommentTime: Math.floor(Date.now() / 1000)
                });
                resolve();
              } else {
                reject(new Error(res.data.msg || '评论失败'));
              }
            },
            fail: reject
          });
        });
      })
      .then(() => {
        // 清空输入和图片
        this.setData({
          commentText: '',
          tempImagePaths: [],
          uploadedImages: [],
          isLoading: false,
          isCommentBarShow: false
        });

        wx.showToast({
          icon: 'success',
          title: '评论成功',
          duration: 2000
        });

        // 刷新评论列表
        this.setData({
          commentList: [],
          page: 1,
          hasMore: true
        });
        this.loadComments();
      })
      .catch(error => {
        this.setData({ isLoading: false });
        this.handleError(error);
      });
  },

  // 上拉加载更多
  onReachBottom: function() {
    if (!this.data.isLoading && this.data.hasMore) {
      this.loadComments();
    }
  },

  // 预览图片佐证区域的图片
  previewEvidenceImage: function(e) {
    const { urls, current } = e.currentTarget.dataset;
    wx.previewImage({
      urls: urls.map(item => item.file),
      current: current.file
    });
  },
  // 预览评论区域的图片
  previewCommentImage: function(e) {
    const src = e.currentTarget.dataset.src;
    if (!src) return;
    
    // 收集所有评论中的图片URL
    const urls = this.data.commentList
      .flatMap(comment => {
        if (comment.parsedContent && comment.parsedContent.nodes) {
          return comment.parsedContent.nodes
            .filter(node => node.tag === 'img' && node.attr && node.attr.src)
            .map(node => node.attr.src);
        }
        return [];
      })
      .filter(url => !!url);
    
    // 如果找不到URL，至少预览当前图片
    const imageUrls = urls.length > 0 ? urls : [src];
    
    wx.previewImage({
      urls: imageUrls,
      current: src,
      fail: err => {
        wx.showToast({
          title: '预览失败',
          icon: 'none'
        });
      }
    });
  },

  // 处理评论区域图片长按事件
  handleCommentImageLongPress: function(e) {
    const src = e.currentTarget.dataset.src;
    if (!src) return;
    
    wx.showActionSheet({
      itemList: ['保存图片'],
      success: (res) => {
        if (res.tapIndex === 0) {
          wx.showLoading({
            title: '保存中...',
            mask: true
          });
          
          wx.downloadFile({
            url: src,
            success: (res) => {
              if (res.statusCode === 200) {
                wx.saveImageToPhotosAlbum({
                  filePath: res.tempFilePath,
                  success: () => {
                    wx.hideLoading();
                    wx.showToast({
                      title: '保存成功',
                      icon: 'success'
                    });
                  },
                  fail: (err) => {
                    wx.hideLoading();
                    wx.showToast({
                      title: '保存失败',
                      icon: 'none'
                    });
                  }
                });
              } else {
                wx.hideLoading();
                wx.showToast({
                  title: '下载失败',
                  icon: 'none'
                });
              }
            },
            fail: (err) => {
              wx.hideLoading();
              wx.showToast({
                title: '下载失败',
                icon: 'none'
              });
            }
          });
        }
      },
      fail: (err) => {
      }
    });
  },

  // 完成工单
  completeOrder: function() {
    if (this.data.isLoading) return;
    
    wx.showModal({
      title: '提示',
      content: '确定后不能回复了，要确定吗？',
      success: (res) => {
        if (res.confirm) {
          wx.showLoading({
            title: '处理中...',
            mask: true
          });
          
          // 修改为使用与show2.js相同的API路径
          wx.request({
            url: app.globalData.http_api + "s=workorder&c=api&m=myda&id=" + this.data.id + 
                 "&api_auth_code=" + wx.getStorageSync('member_auth') + 
                 "&api_auth_uid=" + wx.getStorageSync('member_uid'),
            header: { 'content-type': 'application/json' },
            dataType: 'json',
            method: 'GET',
            success: (res) => {
              wx.hideLoading();
              
              if (res.data.code == 1) {
                wx.showToast({
                  icon: 'success',
                  title: '工单已完成',
                  duration: 2000
                });
                
                // 刷新页面数据
                this.setData({ isRefreshing: true });
                this.loadContentWithPermissionCheck();
              } else {
                this.handleError(new Error(res.data.msg || '操作失败'));
              }
            },
            fail: (error) => {
              wx.hideLoading();
              this.handleError(error);
            }
          });
        }
      }
    });
  },

  // 保存图片到相册
  saveImage: function(e) {
    const url = e.currentTarget.dataset.url;
    wx.showLoading({
      title: '保存中...',
      mask: true
    });
    
    wx.downloadFile({
      url: url,
      success: function(res) {
        if (res.statusCode === 200) {
          wx.saveImageToPhotosAlbum({
            filePath: res.tempFilePath,
            success: function() {
              wx.hideLoading();
              wx.showToast({
                title: '保存成功',
                icon: 'success'
              });
            },
            fail: function() {
              wx.hideLoading();
              wx.showToast({
                title: '保存失败',
                icon: 'none'
              });
            }
          });
        } else {
          wx.hideLoading();
          wx.showToast({
            title: '下载失败',
            icon: 'none'
          });
        }
      },
      fail: function() {
        wx.hideLoading();
        wx.showToast({
          title: '下载失败',
          icon: 'none'
        });
      }
    });
  },

  // 添加阻止触摸移动事件
  preventTouchMove: function() {
    return false;
  },

  // 下拉刷新
  onPullDownRefresh: function() {
    this.setData({
      isRefreshing: true,
      commentList: [],
      page: 1,
      hasMore: true,
      isLoading: false
    });
    
    Promise.all([
      this.loadContentWithPermissionCheck(),
      this.loadComments()
    ]).then(() => {
      wx.stopPullDownRefresh();
      this.setData({ isRefreshing: false });
    }).catch(error => {
      wx.stopPullDownRefresh();
      this.setData({ isRefreshing: false });
      this.handleError(error);
    });
  },

  // 处理错误
  handleError: function(error) {
    console.error('加载出错：', error);
    wx.showModal({
      title: '提示',
      content: typeof error === 'string' ? error : (error.message || '加载失败，请重试'),
      showCancel: false,
      success: function (res) {
        if (res.confirm) {
          wx.navigateBack();
        }
      }
    });
  },

  // 在onUnload函数中释放资源
  onUnload: function() {
    // 页面卸载时的清理工作
  },

  // 设置一个获取焦点时显示评论框的函数
  onCommentFocus: function() {
    this.setData({
      isCommentBarShow: true,
      isActionBarHide: true
    });
  },

  // 设置失去焦点时恢复显示状态的函数
  onCommentBlur: function() {
    setTimeout(() => {
      this.setData({
        isCommentBarShow: false,
        isActionBarHide: false
      });
    }, 200); // 延迟执行，避免闪烁
  },
  
  // 显示全屏评论框
  showFullCommentBar: function() {
    this.setData({
      isCommentBarShow: true
    });
  },
  
  // 隐藏全屏评论框
  hideFullCommentBar: function() {
    this.setData({
      isCommentBarShow: false
    });
  },
  
  // 删除已选择的图片
  deleteImage: function(e) {
    const index = e.currentTarget.dataset.index;
    const tempImagePaths = [...this.data.tempImagePaths];
    tempImagePaths.splice(index, 1);
    this.setData({
      tempImagePaths
    });
  },
  
  // 监听键盘高度变化
  onKeyboardHeightChange: function(res) {
    // 不再需要此功能，可以移除
  },

  // 重新加载页面数据
  refreshData: function() {
    this.setData({
      page: 1,
      commentList: [],
      isRefreshing: true
    });
    this.loadContentWithPermissionCheck();
  },
  
  // 删除重复使用loadContentWithPermissionCheck的代码
  onShareAppMessage: function() {
    return {
      title: this.data.content ? this.data.content.title : '工单详情',
      path: '/pages/workorder/show?id=' + this.data.id
    };
  }
})