<!-- 导入WxParse模板 -->
<import src="../../wxParse/wxParse.wxml"/>

<view class="container">
  <!-- 顶部状态栏 -->
  <view class="status-bar {{loadError ? 'error' : (loading ? 'loading' : 'success')}}">
    <view class="status-progress" wx:if="{{loading}}"></view>
    <view class="status-indicator">
      <text class="status-text">{{loadError ? '加载失败' : (loading ? '加载中' : '档案详情')}}</text>
    </view>
  </view>

  <!-- 加载中提示 -->
  <view class="loading-container" wx:if="{{loading}}">
    <view class="loading-animation">
      <view class="loading-spinner"></view>
    </view>
    <text class="loading-text">正在加载档案信息...</text>
  </view>

  <!-- 加载失败提示 -->
  <view class="error-container" wx:elif="{{loadError}}">
    <image src="../../icons/empty.png" class="error-image"></image>
    <text class="error-title">加载失败</text>
    <text class="error-message">{{errorMsg || '无法获取村民档案信息'}}</text>
    <button class="retry-button" bindtap="loadDetail">重新加载</button>
  </view>

  <!-- 档案内容区 -->
  <scroll-view scroll-y class="archive-scroll" wx:elif="{{villager}}" enable-flex="true">
    <view class="archive-container">
      <!-- 档案头部 -->
      <view class="archive-header">
        <view class="archive-blur-bg" wx:if="{{villager.thumb}}" style="background-image: url('{{villager.thumb}}'); background-size: cover; background-position: center;"></view>
        <view class="archive-info">
          <view class="archive-title">
            <text class="archive-name">{{villager.title}}</text>
            <view class="archive-status {{villager.isXiaohu ? 'status-inactive' : 'status-active'}}">
              {{villager.isXiaohu ? '已销户' : '有效'}}
            </view>
          </view>
          <text class="archive-id">档案编号: {{villager.id}}</text>
          <view class="archive-meta">
            <view class="meta-item">
              <text class="meta-icon views-icon"></text>
              <text class="meta-value">{{villager.hits || 0}}</text>
            </view>
            <view class="meta-item">
              <text class="meta-icon comments-icon"></text>
              <text class="meta-value">{{villager.comments || 0}}条</text>
            </view>
          </view>
        </view>
        <!-- 个人照片缩略图 -->
        <view class="archive-thumb" wx:if="{{villager.thumb}}">
          <image src="{{villager.thumb}}" mode="aspectFill" class="thumb-image" bindtap="previewThumb"></image>
          <view class="thumb-shadow"></view>
        </view>
      </view>

      <!-- 快速操作区 -->
      <view class="quick-actions">
        <view class="quick-action-btn" bindtap="updateImages">
          <text class="quick-action-icon update-image-icon">📷</text>
          <text class="quick-action-text">更新图片</text>
        </view>
        <view class="quick-action-btn" bindtap="updateInfo">
          <text class="quick-action-icon update-info-icon">✏️</text>
          <text class="quick-action-text">更新信息</text>
        </view>
        <view class="quick-action-btn" bindtap="addNewArchive">
          <text class="quick-action-icon add-archive-icon">📋</text>
          <text class="quick-action-text">新增档案</text>
        </view>
        <view class="quick-action-btn" bindtap="addNewNote">
          <text class="quick-action-icon add-note-icon">📝</text>
          <text class="quick-action-text">新增备注</text>
        </view>
      </view>

      <!-- 基本信息区 -->
      <view class="section basic-info">
        <view class="section-header">
          <view class="section-title-container">
            <view class="section-icon basic-icon"></view>
            <text class="section-title">基本信息</text>
          </view>
        </view>
        <view class="section-content">
          <view class="info-grid">
            <view class="info-cell">
              <text class="info-label">姓名</text>
              <text class="info-value">{{villager.title}}</text>
            </view>
            <view class="info-cell">
              <text class="info-label">性别</text>
              <text class="info-value">{{villager.xingbie || '无'}}</text>
            </view>
            <view class="info-cell">
              <text class="info-label">身份证号</text>
              <text class="info-value id-number">{{villager.sfzhm || '无'}}</text>
            </view>
            <view class="info-cell">
              <text class="info-label">联系电话</text>
              <text class="info-value">{{villager.shoujihaoma || '无'}}</text>
            </view>
            <view class="info-cell">
              <text class="info-label">户籍地址</text>
              <text class="info-value">{{villager.hujidizhi || '无'}}</text>
            </view>
            <view class="info-cell">
              <text class="info-label">户籍属性</text>
              <text class="info-value">{{villager.hujishuxing || '无'}}</text>
            </view>
            <view class="info-cell" wx:if="{{villager.jtgx}}">
              <text class="info-label">家庭关系</text>
              <text class="info-value">{{villager.jtgx}}</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 文档状态区 -->
      <view class="section document-status">
        <view class="section-header">
          <view class="section-title-container">
            <view class="section-icon status-icon"></view>
            <text class="section-title">档案状态</text>
          </view>
        </view>
        <view class="section-content">
          <view class="status-timeline">
            <view class="timeline-item">
              <view class="timeline-point"></view>
              <view class="timeline-content">
                <text class="timeline-title">创建时间</text>
                <text class="timeline-value">{{villager.inputtime || '未知'}}</text>
              </view>
            </view>
            <view class="timeline-item">
              <view class="timeline-point"></view>
              <view class="timeline-content">
                <text class="timeline-title">最近更新</text>
                <text class="timeline-value">{{villager.updatetime || '未知'}}</text>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 其他信息区 -->
      <view class="section other-info" wx:if="{{villager.otherInfo && villager.otherInfo.length > 0}}">
        <view class="section-header">
          <view class="section-title-container">
            <view class="section-icon info-icon"></view>
            <text class="section-title">附加信息</text>
          </view>
          <view class="section-badge">{{villager.otherInfo.length}}项</view>
        </view>
        <view class="section-content">
          <view class="info-table">
            <view class="info-row" wx:for="{{villager.otherInfo}}" wx:key="label" wx:if="{{item.label !== 'photoCount'}}">
              <text class="info-row-label">{{item.label}}</text>
              <text class="info-row-value">{{item.value || '无'}}</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 照片区域 Tab控制 -->
      <view class="media-section" wx:if="{{villager.photoCount > 0}}">
        <view class="section-header">
          <view class="section-title-container">
            <view class="section-icon photo-icon"></view>
            <text class="section-title">相关照片</text>
          </view>
          <view class="section-badge total-photos">{{villager.photoCount}}张</view>
        </view>
        
        <!-- 照片标签 - 横向滚动 -->
        <view class="photo-tabs-container">
          <scroll-view scroll-x="true" class="photo-tabs" show-scrollbar="false" enhanced="true" enable-flex="true">
            <view class="photo-tab {{activePhotoTab == 'grzp' ? 'active' : ''}}" 
                  bindtap="switchPhotoTab" data-tab="grzp" 
                  wx:if="{{villager.grzpList && villager.grzpList.length > 0}}">
              <text>身份证件</text>
              <text class="photo-tab-count">{{villager.grzpList.length}}</text>
            </view>
            <view class="photo-tab {{activePhotoTab == 'fwzp' ? 'active' : ''}}" 
                  bindtap="switchPhotoTab" data-tab="fwzp"
                  wx:if="{{villager.fwzpList && villager.fwzpList.length > 0}}">
              <text>房屋照片</text>
              <text class="photo-tab-count">{{villager.fwzpList.length}}</text>
            </view>
            <view class="photo-tab {{activePhotoTab == 'qtzjzp' ? 'active' : ''}}" 
                  bindtap="switchPhotoTab" data-tab="qtzjzp"
                  wx:if="{{villager.qtzjzpList && villager.qtzjzpList.length > 0}}">
              <text>证件照片</text>
              <text class="photo-tab-count">{{villager.qtzjzpList.length}}</text>
            </view>
            <view class="photo-tab {{activePhotoTab == 'gczp' ? 'active' : ''}}" 
                  bindtap="switchPhotoTab" data-tab="gczp"
                  wx:if="{{villager.gczpList && villager.gczpList.length > 0}}">
              <text>改厕照片</text>
              <text class="photo-tab-count">{{villager.gczpList.length}}</text>
            </view>
          </scroll-view>
        </view>
        
        <!-- 照片内容 -->
        <view class="photo-content">
          <!-- 个人照片区 -->
          <view class="photo-container {{activePhotoTab == 'grzp' ? 'active' : ''}}" wx:if="{{villager.grzpList && villager.grzpList.length > 0}}">
            <scroll-view scroll-x class="media-scroll" enable-flex="true">
              <view class="media-grid">
                <view class="media-item" wx:for="{{villager.grzpList}}" wx:key="index" 
                      bindtap="previewImage" data-index="{{index}}" data-type="grzp">
                  <image src="{{item}}" mode="aspectFill" class="media-image" lazy-load></image>
                  <view class="media-overlay">
                    <text class="media-number">{{index + 1}}</text>
                  </view>
                </view>
              </view>
            </scroll-view>
          </view>
          
          <!-- 房屋照片区 -->
          <view class="photo-container {{activePhotoTab == 'fwzp' ? 'active' : ''}}" wx:if="{{villager.fwzpList && villager.fwzpList.length > 0}}">
            <scroll-view scroll-x class="media-scroll" enable-flex="true">
              <view class="media-grid">
                <view class="media-item" wx:for="{{villager.fwzpList}}" wx:key="index" 
                      bindtap="previewImage" data-index="{{index}}" data-type="fwzp">
                  <image src="{{item}}" mode="aspectFill" class="media-image" lazy-load></image>
                  <view class="media-overlay">
                    <text class="media-number">{{index + 1}}</text>
                  </view>
                </view>
              </view>
            </scroll-view>
          </view>
          
          <!-- 证件照片区 -->
          <view class="photo-container {{activePhotoTab == 'qtzjzp' ? 'active' : ''}}" wx:if="{{villager.qtzjzpList && villager.qtzjzpList.length > 0}}">
            <scroll-view scroll-x class="media-scroll" enable-flex="true">
              <view class="media-grid">
                <view class="media-item" wx:for="{{villager.qtzjzpList}}" wx:key="index" 
                      bindtap="previewImage" data-index="{{index}}" data-type="qtzjzp">
                  <image src="{{item}}" mode="aspectFill" class="media-image" lazy-load></image>
                  <view class="media-overlay">
                    <text class="media-number">{{index + 1}}</text>
                  </view>
                </view>
              </view>
            </scroll-view>
          </view>
          
          <!-- 改厕照片区 -->
          <view class="photo-container {{activePhotoTab == 'gczp' ? 'active' : ''}}" wx:if="{{villager.gczpList && villager.gczpList.length > 0}}">
            <scroll-view scroll-x class="media-scroll" enable-flex="true">
              <view class="media-grid">
                <view class="media-item" wx:for="{{villager.gczpList}}" wx:key="index" 
                      bindtap="previewImage" data-index="{{index}}" data-type="gczp">
                  <image src="{{item}}" mode="aspectFill" class="media-image" lazy-load></image>
                  <view class="media-overlay">
                    <text class="media-number">{{index + 1}}</text>
                  </view>
                </view>
              </view>
            </scroll-view>
          </view>
          
          <!-- 空状态 - 当没有选择任何标签或所选标签没有照片时显示 -->
          <view class="media-empty" wx:if="{{!activePhotoTab || (activePhotoTab == 'grzp' && (!villager.grzpList || villager.grzpList.length === 0)) || (activePhotoTab == 'fwzp' && (!villager.fwzpList || villager.fwzpList.length === 0)) || (activePhotoTab == 'qtzjzp' && (!villager.qtzjzpList || villager.qtzjzpList.length === 0)) || (activePhotoTab == 'gczp' && (!villager.gczpList || villager.gczpList.length === 0))}}">
            <text class="media-empty-icon">📷</text>
            <text class="media-empty-text">暂无照片</text>
          </view>
        </view>
      </view>

      <!-- 反馈信息区 -->
      <view class="section feedback-section" wx:if="{{villager.fankui && villager.fankui.length > 0}}">
        <view class="section-header">
          <view class="section-title-container">
            <view class="section-icon feedback-icon"></view>
            <text class="section-title">反馈信息</text>
          </view>
          <view class="section-badge">{{villager.fankui.length}}条</view>
        </view>
        <view class="section-content">
          <view class="feedback-list">
            <view class="feedback-item" wx:for="{{villager.fankui}}" wx:key="id" bindtap="openFeedback" data-url="{{item.url}}">
              <view class="feedback-indicator"></view>
              <view class="feedback-content">
                <view class="feedback-title">{{item.title}}</view>
              </view>
              <view class="feedback-arrow"></view>
            </view>
          </view>
        </view>
      </view>

      <!-- 评论区域 -->
      <view class="section comments-section">
        <view class="section-header">
          <view class="section-title-container">
            <view class="section-icon comment-icon"></view>
            <text class="section-title">档案记录</text>
          </view>
          <view class="section-badge">{{commentList.length || 0}}条</view>
        </view>
        
        <view class="section-content">
          <view class="comments-list">
            <block wx:if="{{commentList.length === 0}}">
              <view class="comments-empty">
                <image src="../../icons/empty-comments.png" class="empty-icon"></image>
                <text class="empty-text">点击底部"记录"添加档案记录</text>
              </view>
            </block>
            <block wx:else>
              <view 
                class="comment-item"
                wx:for="{{commentList}}" 
                wx:key="uniqueKey"
              >
                <view class="comment-item-header">
                  <image 
                    class="comment-item-avatar" 
                    src="{{item.avatar || '../../images/avatar.png'}}" 
                    mode="aspectFill" 
                    lazy-load
                  />
                  <view class="comment-item-meta">
                    <view class="comment-item-author">
                      {{item.author || '匿名用户'}}
                      <text class="comment-item-tag" wx:if="{{item.uid == member.uid}}">我</text>
                    </view>
                    <text class="comment-item-time">{{item.inputtime}}</text>
                  </view>
                </view>
                <view class="comment-item-content">
                  <!-- 使用wxParse模板渲染评论内容 -->
                  <template is="wxParse" data="{{wxParseData:item.parsedContent.nodes}}"/>
                  
                  <!-- 评论图片 - 如果存在未经处理的单独图片数组 -->
                  <view class="comment-images" wx:if="{{item.images && item.images.length > 0}}">
                    <view 
                      class="comment-image-item" 
                      wx:for="{{item.images}}" 
                      wx:for-item="imgUrl" 
                      wx:for-index="imgIndex" 
                      wx:key="imgIndex"
                      bindtap="previewImage"
                      data-current="{{imgUrl}}"
                      data-urls="{{item.images}}"
                    >
                      <image src="{{imgUrl}}" mode="aspectFill" class="comment-image" lazy-load />
                    </view>
                  </view>
                </view>
              </view>
            </block>
          </view>

          <!-- 加载状态 -->
          <view class="loading-status" wx:if="{{isLoading || (!hasMore && commentList.length > 0)}}">
            <view class="loading-more" wx:if="{{isLoading}}">
              <view class="loading-spinner small"></view>
              <text>加载中...</text>
            </view>
            <view class="loading-end" wx:if="{{!hasMore && !isLoading && commentList.length > 0}}">
              <text>没有更多记录了</text>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 底部间距 -->
      <view class="bottom-spacer"></view>
    </view>
  </scroll-view>

  <!-- 底部操作栏 -->
  <view class="action-bar" wx:if="{{villager}}">
    <button class="action-button back-button" bindtap="goBack">
      <text class="button-icon back-icon"></text>
      <text class="button-text">返回</text>
    </button>
    <view class="comment-input-area" bindtap="showFullCommentBar">
      <view class="comment-input-box">
        <text class="input-placeholder">点击添加档案记录...</text>
      </view>
    </view>
    <button class="action-button share-button" open-type="share">
      <text class="button-icon share-icon"></text>
    </button>
  </view>

  <!-- 评论弹出层 -->
  <view class="comment-modal {{isCommentBarShow ? 'comment-modal-show' : ''}}">
    <view class="comment-modal-header">
      <text class="modal-title">添加档案记录</text>
      <view class="comment-modal-close" bindtap="hideFullCommentBar">
        <text class="close-icon">×</text>
      </view>
    </view>
    
    <view class="comment-modal-body">
      <textarea 
        class="comment-modal-textarea"
        placeholder="请输入档案记录内容..." 
        bindinput="getText"
        value="{{commentText}}"
        fixed
        maxlength="2000"
        show-confirm-bar="{{false}}"
      />
      
      <!-- 图片预览 -->
      <block wx:if="{{tempImagePaths.length > 0}}">
        <view class="image-preview">
          <view 
            class="image-preview-item" 
            wx:for="{{tempImagePaths}}" 
            wx:key="index"
          >
            <image src="{{item}}" class="image-preview-img" mode="aspectFill"></image>
            <view 
              class="image-preview-delete" 
              catchtap="deleteImage" 
              data-index="{{index}}"
            >×</view>
          </view>
        </view>
      </block>
    </view>

    <view class="comment-modal-footer">
      <view class="comment-modal-tools">
        <button class="upload-btn" bindtap="chooseImage">
          <text class="upload-icon"></text>
          <text>添加图片</text>
          <block wx:if="{{tempImagePaths.length > 0}}">
            <text class="image-count">({{tempImagePaths.length}})</text>
          </block>
        </button>
      </view>
      <view class="comment-modal-actions">
        <button 
          class="comment-modal-btn comment-modal-btn-cancel" 
          bindtap="hideFullCommentBar"
        >取消</button>
        <button 
          class="comment-modal-btn comment-modal-btn-submit {{commentText || tempImagePaths.length > 0 ? 'is-active' : ''}}" 
          bindtap="saveComment"
        >发表</button>
      </view>
    </view>
  </view>
</view> 