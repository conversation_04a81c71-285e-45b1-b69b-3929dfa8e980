/* 页面容器 */
.page-container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 120rpx;
  position: relative;
}

/* 头部区域 */
.header {
  background: #fff;
  padding: 24rpx;
  margin-bottom: 16rpx;
}

.header__title-section {
  margin-bottom: 20rpx;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid #eee;
}

/* 添加作者信息区域样式 */
.header__author-info {
  display: flex;
  align-items: flex-start;
  margin-bottom: 16rpx;
}

.header__author-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin-right: 20rpx;
  border: 2rpx solid #eee;
  flex-shrink: 0;
}

.header__text-content {
  flex: 1;
  overflow: hidden;
}

.header__main-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #2c3e50;
  line-height: 1.5;
  margin-bottom: 16rpx;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

.header__meta {
  display: flex;
  align-items: center;
  font-size: 24rpx;
  color: #999;
  line-height: 1;
}

.header__author {
  color: #666;
  margin-right: 16rpx;
  position: relative;
  padding-left: 16rpx;
}

.header__author::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 4rpx;
  height: 24rpx;
  background: #1989fa;
  border-radius: 2rpx;
}

.header__author::after {
  content: '';
  position: absolute;
  right: -8rpx;
  top: 50%;
  transform: translateY(-50%);
  width: 2rpx;
  height: 16rpx;
  background: #e0e0e0;
}

.header__time {
  color: #999;
  display: flex;
  align-items: center;
}

/* 状态面板 */
.status-panel {
  margin-top: 30rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  padding: 24rpx;
}

.status-panel__grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200rpx, 1fr));
  gap: 20rpx;
}

.status-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

/* 所属区域样式调整 */
.status-item--area {
  flex-direction: row;
  background-color: #f0f8ff;
  padding: 12rpx 16rpx;
  border-radius: 8rpx;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);
  margin-bottom: 16rpx;
  grid-column: 1 / -1;
  position: relative;
  overflow: hidden;
}

.status-item--area::after {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 6rpx;
  background: #1989fa;
}

.status-item--area .status-item__icon {
  margin-bottom: 0;
  margin-right: 16rpx;
  width: 56rpx;
  height: 56rpx;
}

.status-item--area .status-item__content {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.status-item__value--highlight {
  color: #1989fa;
  font-size: 32rpx;
  font-weight: 600;
}

.status-item__icon {
  width: 48rpx;
  height: 48rpx;
  margin-bottom: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.status-item__icon-img {
  width: 100%;
  height: 100%;
}

.status-item__label {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 8rpx;
}

.status-item__value {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

/* 主要内容区域 */
.main-content {
  padding: 0 30rpx;
}

/* 内容卡片通用样式 */
.content-card,
.image-gallery-card {
  background: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

/* 章节标题通用样式 */
.section-header {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
}

.section-header__line {
  width: 6rpx;
  height: 32rpx;
  background: #1989fa;
  margin-right: 16rpx;
  border-radius: 3rpx;
}

.section-header__title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.section-header__count {
  font-size: 26rpx;
  color: #999;
  margin-left: 12rpx;
}

/* 内容区域 */
.content-card__body {
  font-size: 30rpx;
  color: #333;
  line-height: 1.6;
}

/* 图片画廊 */
.image-gallery__grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20rpx;
  margin-top: 20rpx;
  position: relative;
}

.image-gallery__item {
  position: relative;
  padding-bottom: 100%;
  overflow: hidden;
  border-radius: 8rpx;
  background: #f8f9fa;
}

.image-gallery__img {
  position: absolute;
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* 图片计数样式 */
.image-count {
  position: absolute;
  right: 20rpx;
  bottom: 20rpx;
  background: rgba(0, 0, 0, 0.6);
  color: #fff;
  font-size: 24rpx;
  padding: 6rpx 16rpx;
  border-radius: 20rpx;
  display: flex;
  align-items: center;
  backdrop-filter: blur(4px);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
  z-index: 10;
}

.image-count__icon {
  width: 28rpx;
  height: 28rpx;
  margin-right: 8rpx;
}

/* 评论区域 */
.comments {
  background: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.comments__empty {
  text-align: center;
  padding: 60rpx 0;
  color: #999;
  font-size: 28rpx;
}

.comment-item {
  padding: 30rpx 0;
  border-bottom: 1rpx solid #eee;
}

.comment-item:last-child {
  border-bottom: none;
}

.comment-item__header {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.comment-item__avatar {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  margin-right: 16rpx;
}

.comment-item__meta {
  flex: 1;
}

.comment-item__author {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  display: flex;
  align-items: center;
}

.comment-item__tag {
  font-size: 22rpx;
  color: #1989fa;
  background: rgba(25, 137, 250, 0.1);
  padding: 4rpx 12rpx;
  border-radius: 8rpx;
  margin-left: 12rpx;
}

.comment-item__time {
  font-size: 24rpx;
  color: #999;
  margin-top: 6rpx;
}

.comment-item__content {
  font-size: 28rpx;
  color: #333;
  line-height: 1.6;
  padding: 0 0 16rpx;
}

/* 评论图片样式 */
.comment-item .wxParse-p {
  margin: 0;
  padding: 0;
}

.comment-item .wxParse-img {
  margin: 0 !important;
  padding: 0 !important;
  width: auto !important;
  height: auto !important;
}

.comment-item .wxParse-p-with-img {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 12rpx;
  margin-top: 16rpx;
  position: relative;
  background: #f8f9fa;
  padding: 12rpx;
  border-radius: 8rpx;
}

.comment-item .wxParse-p-with-img .wxParse-img {
  width: 100% !important;
  height: 160rpx !important;
  object-fit: cover;
  border-radius: 6rpx;
}

/* 单张图片样式 */
.comment-item .wxParse-p-with-img.single-img {
  grid-template-columns: 1fr;
  max-width: 400rpx;
}

.comment-item .wxParse-p-with-img.single-img .wxParse-img {
  height: 280rpx !important;
}

/* 两张图片样式 */
.comment-item .wxParse-p-with-img.double-img {
  grid-template-columns: repeat(2, 1fr);
  max-width: 600rpx;
}

.comment-item .wxParse-p-with-img.double-img .wxParse-img {
  height: 220rpx !important;
}

/* 评论区图片计数样式 */
.comment-item .image-count {
  position: absolute;
  right: 12rpx;
  bottom: 12rpx;
  background: rgba(0, 0, 0, 0.6);
  color: #fff;
  font-size: 22rpx;
  padding: 4rpx 16rpx;
  border-radius: 20rpx;
  display: flex;
  align-items: center;
  backdrop-filter: blur(4px);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.15);
  z-index: 10;
}

.comment-item .image-count__icon {
  width: 24rpx;
  height: 24rpx;
  margin-right: 6rpx;
  opacity: 0.9;
}

/* 加载状态 */
.loading-status {
  text-align: center;
  padding: 20rpx 0;
}

.loading-status__more,
.loading-status__end {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #999;
  font-size: 26rpx;
}

.loading-status__icon {
  width: 36rpx;
  height: 36rpx;
  margin-right: 12rpx;
}

/* 底部区域 */
.footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  padding: 12rpx 24rpx;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  display: flex;
  align-items: center;
  z-index: 100;
}

.footer__input-wrapper {
  flex: 1;
  margin-right: 16rpx;
}

.footer__input-box {
  background: #f5f5f5;
  border-radius: 4rpx;
  padding: 14rpx 20rpx;
  color: #999;
  font-size: 26rpx;
  line-height: 1.4;
  height: 36rpx;
}

.footer__completed-notice {
  background: #f5f5f5;
  border-radius: 4rpx;
  padding: 14rpx 20rpx;
  color: #999;
  font-size: 26rpx;
  text-align: center;
  line-height: 1.4;
  height: 36rpx;
}

.footer__actions {
  display: flex;
  align-items: center;
  height: 64rpx;
  gap: 24rpx;
}

/* 操作按钮 */
.action-btn {
  margin-left: 16rpx;
  height: 64rpx;
  display: flex;
  align-items: center;
}

.action-btn__normal {
  background: none !important;
  border: none !important;
  padding: 0 !important;
  margin: 0;
  line-height: 1;
  color: #666;
  display: flex;
  align-items: center;
  height: 100%;
  min-width: unset !important;
}

.action-btn__normal text {
  background: #f0f2f5;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  font-size: 24rpx;
  color: #666;
  margin-left: 4rpx;
  min-width: 32rpx;
  text-align: center;
}

.action-btn__normal::after {
  display: none;
}

.action-btn__complete {
  background: #1989fa;
  color: #fff;
  font-size: 26rpx;
  padding: 0 24rpx;
  border-radius: 4rpx;
  border: none;
  height: 64rpx;
  line-height: 64rpx;
  display: flex;
  align-items: center;
}

.action-btn__icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 0;
}

/* 评论弹出层 */
.comment-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: #fff;
  z-index: 1000;
  transform: translateY(100%);
  transition: transform 0.3s ease-out;
}

.comment-modal--show {
  transform: translateY(0);
}

.comment-modal__header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  border-bottom: 1rpx solid #eee;
}

.comment-modal__close {
  padding: 10rpx;
}

.comment-modal__close-icon {
  width: 32rpx;
  height: 32rpx;
}

.comment-modal__body {
  padding: 24rpx;
  flex: 1;
  overflow-y: auto;
  background: #fff;
}

.comment-modal__textarea {
  width: 100%;
  height: 200rpx;
  font-size: 28rpx;
  line-height: 1.6;
  padding: 16rpx;
  box-sizing: border-box;
  border: 1rpx solid #eee;
  border-radius: 6rpx;
  background: #f8f9fa;
}

.comment-modal__textarea:focus {
  border-color: #1989fa;
  background: #fff;
}

/* 图片预览 */
.image-preview {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 12rpx;
  margin-top: 20rpx;
  padding: 16rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
}

.image-preview__item {
  position: relative;
  padding-bottom: 100%;
  background: #fff;
  border-radius: 8rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.image-preview__img {
  position: absolute;
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 6rpx;
  transition: transform 0.2s ease;
}

.image-preview__img:active {
  transform: scale(0.95);
}

.image-preview__delete {
  position: absolute;
  top: -6rpx;
  right: -6rpx;
  width: 32rpx;
  height: 32rpx;
  background: rgba(0, 0, 0, 0.6);
  color: #fff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  z-index: 10;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.2);
}

.image-preview__delete:active {
  background: rgba(0, 0, 0, 0.8);
}

/* 评论弹出层底部操作区 */
.comment-modal__footer {
  padding: 20rpx 30rpx;
  border-top: 1rpx solid #eee;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: #fff;
}

.comment-modal__tools {
  display: flex;
  align-items: center;
}

/* 上传按钮样式优化 */
.upload-btn {
  background: #f8f9fa;
  border: none;
  padding: 10rpx 16rpx;
  display: flex;
  align-items: center;
  border-radius: 4rpx;
  transition: background-color 0.2s ease;
}

.upload-btn:active {
  background: #f0f0f0;
}

.upload-btn__icon {
  width: 36rpx;
  height: 36rpx;
}

/* 更新操作按钮样式 */
.comment-modal__actions {
  display: flex;
  align-items: center;
}

.comment-modal__btn {
  min-width: 140rpx;
  font-size: 26rpx;
  padding: 12rpx 24rpx;
  border-radius: 4rpx;
  text-align: center;
  transition: all 0.2s ease;
  margin-left: 16rpx;
}

.comment-modal__btn--cancel {
  background: #f5f5f5;
  color: #666;
}

.comment-modal__btn--cancel:active {
  background: #eee;
}

.comment-modal__btn--submit {
  background: #ccc;
  color: #fff;
}

.comment-modal__btn--submit.is-active {
  background: #1989fa;
}

.comment-modal__btn--submit.is-active:active {
  background: #1677dd;
}

/* 底部占位 */
.bottom-spacer {
  height: 120rpx;
}

