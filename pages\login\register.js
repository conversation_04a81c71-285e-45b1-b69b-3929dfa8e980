const app = getApp();

Page({

  /**
   * 页面的初始数据
   */
  data: {
    regData: {
      username: '',
      name: '',
      email: '',
      phone: '',
      password: '',
      confirmPassword: '',
      agreement: true,
      sscz_level1: '', // 所属村组一级
      sscz_level2: ''  // 所属村组二级
    },
    errors: {
      username: '',
      name: '',
      email: '',
      phone: '',
      password: '',
      confirmPassword: '',
      sscz: ''
    },
    showPassword: false,
    loading: false,
    // 所属村组数据
    ssczLevel1Options: [],
    ssczLevel2Options: []
  },

  onLoad: function(options) {
    // 如果有传入的手机号，自动填充
    if (options.phone) {
      this.setData({
        'regData.phone': options.phone,
        'errors.phone': ''
      });
    }
    
    // 检查本地存储中是否有手机号
    const savedPhone = wx.getStorageSync('phone');
    if (savedPhone && !this.data.regData.phone) {
      this.setData({
        'regData.phone': savedPhone,
        'errors.phone': ''
      });
    }

    // 加载所属村组一级数据
    this.loadSsczLevel1Options();
  },

  // 加载所属村组一级选项
  loadSsczLevel1Options: function() {
    wx.request({
      url: app.globalData.http_api + 's=api&c=api&m=linkage_ld&mid=member&file=&code=sscz&parent_id=0',
      method: 'GET',
      success: (res) => {
        if (res.data && res.data.data && Array.isArray(res.data.data)) {
          this.setData({
            ssczLevel1Options: res.data.data
          });
        } else {
          console.error('加载所属村组一级选项失败:', res.data);
          wx.showToast({
            title: '加载村组数据失败',
            icon: 'none',
            duration: 2000
          });
        }
      },
      fail: (err) => {
        console.error('加载所属村组一级选项请求失败:', err);
      }
    });
  },

  // 加载所属村组二级选项
  loadSsczLevel2Options: function(parentId) {
    if (!parentId) {
      this.setData({
        ssczLevel2Options: [],
        'regData.sscz_level2': ''
      });
      return;
    }

    wx.request({
      url: app.globalData.http_api + 's=api&c=api&m=linkage_ld&mid=member&file=&code=sscz&parent_id=' + parentId,
      method: 'GET',
      success: (res) => {
        if (res.data && res.data.data && Array.isArray(res.data.data)) {
          this.setData({
            ssczLevel2Options: res.data.data
          });
        } else {
          console.error('加载所属村组二级选项失败:', res.data);
          wx.showToast({
            title: '加载村组数据失败',
            icon: 'none',
            duration: 2000
          });
        }
      },
      fail: (err) => {
        console.error('加载所属村组二级选项请求失败:', err);
      }
    });
  },

  // 所属村组一级选择事件
  changeSsczLevel1: function(e) {
    const level1Index = e.detail.value;
    const selectedItem = this.data.ssczLevel1Options[level1Index];
    const level1Id = selectedItem ? selectedItem.region_id : '';
    
    this.setData({
      'regData.sscz_level1': level1Index,
      'errors.sscz': ''
    });
    
    // 加载对应的二级选项
    this.loadSsczLevel2Options(level1Id);
  },

  // 所属村组二级选择事件
  changeSsczLevel2: function(e) {
    const level2Index = e.detail.value;
    
    this.setData({
      'regData.sscz_level2': level2Index,
      'errors.sscz': ''
    });
  },

  // 输入框事件处理函数
  inputUsername: function(e) {
    this.setData({
      'regData.username': e.detail.value,
      'errors.username': ''
    });
  },

  inputName: function(e) {
    this.setData({
      'regData.name': e.detail.value,
      'errors.name': ''
    });
  },

  inputEmail: function(e) {
    this.setData({
      'regData.email': e.detail.value,
      'errors.email': ''
    });
  },

  inputPhone: function(e) {
    this.setData({
      'regData.phone': e.detail.value,
      'errors.phone': ''
    });
  },

  inputPassword: function(e) {
    this.setData({
      'regData.password': e.detail.value,
      'errors.password': ''
    });
  },

  inputConfirmPassword: function(e) {
    this.setData({
      'regData.confirmPassword': e.detail.value,
      'errors.confirmPassword': ''
    });
  },

  // 密码可见性切换
  togglePasswordVisibility: function() {
    this.setData({
      showPassword: !this.data.showPassword
    });
  },

  // 同意协议切换
  toggleAgreement: function() {
    this.setData({
      'regData.agreement': !this.data.regData.agreement
    });
  },

  // 显示用户协议
  showTerms: function() {
    wx.navigateTo({
      url: '/pages/common/terms'
    });
  },

  // 显示隐私政策
  showPrivacy: function() {
    wx.navigateTo({
      url: '/pages/common/privacy'
    });
  },

  // 跳转到登录页
  goToLogin: function() {
    wx.navigateTo({
      url: '/pages/login/login'
    });
  },

  // 表单验证
  validateForm: function() {
    let isValid = true;
    const errors = {};
    const { username, name, email, phone, password, confirmPassword, agreement, sscz_level1, sscz_level2 } = this.data.regData;

    // 验证用户名
    if (!username.trim()) {
      errors.username = '请输入用户名';
      isValid = false;
    } else if (username.trim().length < 3) {
      errors.username = '用户名至少需要3个字符';
      isValid = false;
    }

    // 验证真实姓名
    if (!name.trim()) {
      errors.name = '请输入真实姓名';
      isValid = false;
    }

    // 验证邮箱
    const emailRegex = /^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,6}$/;
    if (!email.trim()) {
      errors.email = '请输入电子邮箱';
      isValid = false;
    } else if (!emailRegex.test(email.trim())) {
      errors.email = '请输入有效的电子邮箱';
      isValid = false;
    }

    // 验证手机号
    const phoneRegex = /^1[3-9]\d{9}$/;
    if (!phone.trim()) {
      errors.phone = '请输入手机号码';
      isValid = false;
    } else if (!phoneRegex.test(phone.trim())) {
      errors.phone = '请输入有效的手机号码';
      isValid = false;
    }

    // 验证所属村组
    if (!sscz_level1) {
      errors.sscz = '请选择所属村组';
      isValid = false;
    } else if (this.data.ssczLevel2Options.length > 0 && !sscz_level2) {
      errors.sscz = '请选择具体所属村组';
      isValid = false;
    }

    // 验证密码
    if (!password) {
      errors.password = '请输入密码';
      isValid = false;
    } else if (password.length < 6) {
      errors.password = '密码至少需要6个字符';
      isValid = false;
    }

    // 验证确认密码
    if (!confirmPassword) {
      errors.confirmPassword = '请确认密码';
      isValid = false;
    } else if (password !== confirmPassword) {
      errors.confirmPassword = '两次输入的密码不一致';
      isValid = false;
    }

    // 验证协议同意
    if (!agreement) {
      wx.showToast({
        title: '请阅读并同意用户条款和隐私政策',
        icon: 'none',
        duration: 2000
      });
      isValid = false;
    }

    this.setData({ errors });
    return isValid;
  },

  // 表单提交处理
  formSubmit: function() {
    if (this.data.loading) return;
    
    if (!this.validateForm()) return;

    this.setData({ loading: true });

    const { username, name, email, phone, password, confirmPassword, sscz_level1, sscz_level2 } = this.data.regData;
    
    // 获取所属村组实际ID值
    let ssczValue = '';
    if (sscz_level2 && this.data.ssczLevel2Options.length > 0) {
      ssczValue = this.data.ssczLevel2Options[sscz_level2].region_id;
    } else if (sscz_level1) {
      ssczValue = this.data.ssczLevel1Options[sscz_level1].region_id;
    }
    
    // 构建请求URL
    const requestUrl = `${app.globalData.http_api}&s=member&c=register`;
    
    // 发送注册请求
    wx.request({
      url: requestUrl,
      method: 'POST',
      data: {
        'is_ajax': 1,
        'is_protocol': 1,
        'data[username]': username,
        'data[name]': name,
        'data[password]': password,
        'data[password2]': confirmPassword,
        'data[email]': email || '',
        'data[phone]': phone || '',
        'data[sscz]': ssczValue
      },
      header: {
        'Content-Type': 'application/x-www-form-urlencoded'
      },
      success: (res) => {
        console.log('注册响应:', res.data);
        
        if (res.data.code === 1) {
          // 注册成功
          wx.showToast({
            title: '注册成功',
            icon: 'success',
            duration: 2000
          });
          
          // 延迟跳转到登录页
          setTimeout(() => {
            wx.navigateTo({
              url: '/pages/login/login'
            });
          }, 2000);
        } else {
          // 注册失败
          wx.showToast({
            title: res.data.msg || '注册失败，请稍后重试',
            icon: 'none',
            duration: 2000
          });
        }
      },
      fail: (err) => {
        console.error('注册请求失败:', err);
        wx.showToast({
          title: '网络错误，请检查网络连接',
          icon: 'none',
          duration: 2000
        });
      },
      complete: () => {
        this.setData({ loading: false });
      }
    });
  }
})