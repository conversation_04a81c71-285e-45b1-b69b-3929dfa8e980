<view class="container">
  <!-- 顶部背景 -->
  <view class="top-bg"></view>
  
  <!-- 页面标题 -->
  <view class="page-header">
    <text class="page-title">村民档案管理</text>
  </view>
  
  <!-- 搜索框 -->
  <view class="search-box">
    <view class="search-input-wrap">
      <image src="../../icons/search.png" class="search-icon"></image>
      <input 
        class="search-input" 
        placeholder="输入姓名或身份证号搜索" 
        confirm-type="search"
        value="{{searchKeyword}}"
        bindinput="onSearchInput"
        bindconfirm="onSearchConfirm"
      />
      <view class="clear-icon" bindtap="clearSearch" wx:if="{{searchKeyword}}">
        <image src="../../icons/clear.png"></image>
      </view>
    </view>
    <view class="search-btn" bindtap="onSearchConfirm">搜索</view>
  </view>
  
  <!-- 内容区域 -->
  <view class="content-area">
    <!-- 搜索结果提示 -->
    <view class="search-result-tip" wx:if="{{isSearching}}">
      <text>关键词: "{{searchKeyword}}" 搜索结果 ({{cmList.length}})</text>
      <view class="back-to-all" bindtap="clearSearch">返回全部</view>
    </view>
    
    <!-- 筛选标签 -->
    <view class="filter-tag-wrap" wx:if="{{showFilterTag}}">
      <view class="filter-tag">
        <text>类型: {{filterType}} ({{totalFilterCount || cmList.length}})</text>
        <view class="clear-filter" bindtap="clearFilter">清除</view>
      </view>
      <view class="back-to-stats" bindtap="backToStats">
        <text>« 返回统计</text>
      </view>
    </view>

    <view class="empty-state" wx:if="{{!cmList || cmList.length === 0}}">
      <image src="../../icons/empty.png" class="empty-icon"></image>
      <text class="empty-text" wx:if="{{isSearching}}">未找到匹配的村民档案</text>
      <text class="empty-text" wx:elif="{{showFilterTag}}">未找到"{{filterType}}"类型的村民档案</text>
      <text class="empty-text" wx:else>暂无村民档案信息</text>
    </view>
    
    <view class="cmda-list" wx:else>
      <view class="cmda-item" wx:for="{{cmList}}" wx:key="uniqueKey" bindtap="viewDetail" data-id="{{item.id}}">
        <view class="cmda-info">
          <text class="cmda-name">{{item.title}}</text>
          <text class="cmda-id">身份证: {{item.sfzhm}}</text>
        </view>
        <view class="cmda-meta">
          <text class="cmda-address">{{item.hujidizhi || ''}}</text>
          <image src="../../icons/right-back.png" class="arrow-icon"></image>
        </view>
      </view>
    </view>
    
    <!-- 加载更多提示 -->
    <view class="loading-more" wx:if="{{loading && cmList.length > 0}}">
      <text>加载中...</text>
    </view>
    <view class="loading-more" wx:if="{{!hasMore && cmList.length > 0 && !loading}}">
      <text>已加载全部</text>
    </view>
  </view>
  
  <!-- 底部操作栏 -->
  <view class="footer-actions">
  </view>
</view> 