/* 页面容器 */
.container {
  padding: 0;
  background-color: #f6f8fa;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* 页头 */
.page-header {
  padding: 30rpx 40rpx;
  margin-bottom: 0;
  background-color: #fff;
  border-bottom: 1rpx solid #eaeef2;
}

.header-title {
  font-size: 42rpx;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 10rpx;
}

.header-subtitle {
  font-size: 26rpx;
  color: #7f8c8d;
}

.required-mark {
  color: #e74c3c;
  font-weight: bold;
}

/* 表单容器 */
.form-container {
  flex: 1;
  padding: 30rpx 30rpx 120rpx;
  background-color: #f6f8fa;
}

/* 表单分区 */
.form-section {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  margin-bottom: 30rpx;
  border: 1rpx solid #eaeef2;
  overflow: hidden;
}

.section-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 30rpx;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid #f0f2f5;
}

/* 收支类型选择器 */
.income-expense-selector {
  padding: 30rpx;
}

.type-tabs {
  display: flex;
  margin-top: 20rpx;
  border-radius: 12rpx;
  overflow: hidden;
  background-color: #f0f2f5;
  padding: 6rpx;
}

.type-tab {
  flex: 1;
  text-align: center;
  padding: 24rpx 0;
  font-size: 30rpx;
  color: #7f8c8d;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8rpx;
  transition: all 0.3s;
}

.type-tab.active {
  color: #fff;
  background-color: #3498db;
  font-weight: 500;
  box-shadow: 0 2rpx 8rpx rgba(52, 152, 219, 0.3);
}

.type-tab:first-child.active {
  background-color: #2ecc71;
  box-shadow: 0 2rpx 8rpx rgba(46, 204, 113, 0.3);
}

.tab-icon {
  width: 36rpx;
  height: 36rpx;
  margin-right: 16rpx;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.income-icon {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><line x1="12" y1="19" x2="12" y2="5"></line><polyline points="5 12 12 5 19 12"></polyline></svg>');
}

.expense-icon {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><line x1="12" y1="5" x2="12" y2="19"></line><polyline points="19 12 12 19 5 12"></polyline></svg>');
}

.type-tab.active .income-icon, .type-tab.active .expense-icon {
  filter: brightness(10);
}

/* 表单行 */
.form-row {
  display: flex;
  margin: 0 -10rpx;
}

.half-width {
  width: 50%;
  padding: 0 10rpx;
  box-sizing: border-box;
}

/* 表单项 */
.form-item {
  margin-bottom: 30rpx;
}

.form-label {
  font-size: 28rpx;
  color: #34495e;
  margin-bottom: 16rpx;
  font-weight: 500;
}

.form-label.required:after {
  content: ' *';
  color: #e74c3c;
}

.form-input {
  width: 100%;
  height: 90rpx;
  border: 1rpx solid #dfe6e9;
  border-radius: 8rpx;
  padding: 0 24rpx;
  font-size: 28rpx;
  box-sizing: border-box;
  background-color: #fff;
  color: #2c3e50;
  transition: all 0.3s;
}

.form-input:focus {
  border-color: #3498db;
  box-shadow: 0 0 0 2rpx rgba(52, 152, 219, 0.2);
}

/* 金额输入框 */
.amount-input-container {
  position: relative;
  display: flex;
  align-items: center;
}

.currency-symbol {
  position: absolute;
  left: 24rpx;
  font-size: 32rpx;
  color: #2c3e50;
  font-weight: 500;
}

.amount-input {
  padding-left: 60rpx;
  font-size: 32rpx;
  font-weight: 500;
  color: #2c3e50;
}

/* 选择器样式 */
.picker-view {
  width: 100%;
  height: 90rpx;
  border: 1rpx solid #dfe6e9;
  border-radius: 8rpx;
  padding: 0 24rpx;
  font-size: 28rpx;
  background-color: #fff;
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: #2c3e50;
  box-sizing: border-box;
  position: relative;
}

.picker-text {
  flex: 1;
}

.picker-icon {
  width: 24rpx;
  height: 24rpx;
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="6 9 12 15 18 9"></polyline></svg>');
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  opacity: 0.5;
}

/* 文本域 */
.form-textarea {
  width: 100%;
  height: 200rpx;
  border: 1rpx solid #dfe6e9;
  border-radius: 8rpx;
  padding: 24rpx;
  font-size: 28rpx;
  box-sizing: border-box;
  background-color: #fff;
  color: #2c3e50;
  transition: all 0.3s;
}

.form-textarea:focus {
  border-color: #3498db;
  box-shadow: 0 0 0 2rpx rgba(52, 152, 219, 0.2);
}

.textarea-counter {
  font-size: 24rpx;
  color: #95a5a6;
  text-align: right;
  margin-top: 8rpx;
}

/* 凭证上传区域 */
.upload-area {
  border: 1rpx dashed #dfe6e9;
  border-radius: 8rpx;
  background-color: #f8fafc;
  min-height: 180rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20rpx;
}

.upload-hint {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 30rpx 0;
  color: #7f8c8d;
  font-size: 28rpx;
}

.upload-icon {
  width: 80rpx;
  height: 80rpx;
  margin-bottom: 20rpx;
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="%2395a5a6" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path><polyline points="17 8 12 3 7 8"></polyline><line x1="12" y1="3" x2="12" y2="15"></line></svg>');
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.upload-desc {
  font-size: 24rpx;
  color: #95a5a6;
  margin-top: 10rpx;
}

.image-list {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
}

.image-item {
  width: 200rpx;
  height: 200rpx;
  margin: 10rpx;
  position: relative;
  border-radius: 8rpx;
  overflow: hidden;
}

.image-item image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.image-delete {
  position: absolute;
  top: 10rpx;
  right: 10rpx;
  width: 40rpx;
  height: 40rpx;
  background-color: rgba(0, 0, 0, 0.5);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  z-index: 2;
}

.add-more {
  border: 1rpx dashed #dfe6e9;
  background-color: #f8fafc;
  display: flex;
  align-items: center;
  justify-content: center;
}

.add-icon {
  font-size: 60rpx;
  font-weight: 300;
  color: #95a5a6;
}

/* 选项网格 */
.option-grid {
  display: flex;
  flex-wrap: wrap;
  width: 100%;
  padding: 0;
  box-sizing: border-box;
}

.option-row {
  display: flex;
  flex-wrap: wrap;
  width: 100%;
  padding: 0;
  box-sizing: border-box;
}

.option-item {
  margin: 10rpx 10rpx;
  padding: 16rpx 24rpx;
  border: 1rpx solid #dfe6e9;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: #34495e;
  background-color: #f8fafc;
  transition: all 0.3s;
  box-sizing: border-box;
  flex: 0 0 auto;
  min-width: 140rpx;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
}

.option-item.active {
  color: #fff;
  background-color: #3498db;
  border-color: #3498db;
  box-shadow: 0 2rpx 8rpx rgba(52, 152, 219, 0.3);
}

/* 底部操作栏 */
.action-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  padding: 20rpx 30rpx;
  background-color: #fff;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  z-index: 100;
}

/* 按钮 */
.btn {
  flex: 1;
  height: 88rpx;
  line-height: 88rpx;
  text-align: center;
  border-radius: 44rpx;
  font-size: 30rpx;
  margin: 0 15rpx;
  transition: all 0.3s;
}

.btn-cancel {
  background-color: #f1f3f5;
  color: #34495e;
  border: 1rpx solid #dfe6e9;
}

.btn-submit {
  background-color: #3498db;
  color: #fff;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-submit.disabled {
  background-color: #95afc0;
  color: #fff;
  opacity: 0.7;
}

.btn-arrow {
  width: 24rpx;
  height: 24rpx;
  margin-left: 10rpx;
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><line x1="5" y1="12" x2="19" y2="12"></line><polyline points="12 5 19 12 12 19"></polyline></svg>');
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

/* 无权限页面 */
.no-permission {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  background-color: #f6f8fa;
  padding: 40rpx;
}

.lock-icon {
  width: 180rpx;
  height: 180rpx;
  margin-bottom: 50rpx;
  opacity: 0.7;
}

.no-permission-text {
  font-size: 34rpx;
  color: #34495e;
  margin-bottom: 60rpx;
  text-align: center;
  font-weight: 500;
}

.btn-back {
  width: 360rpx;
  height: 88rpx;
  line-height: 88rpx;
  background-color: #3498db;
  color: #fff;
  border-radius: 44rpx;
  font-size: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(52, 152, 219, 0.3);
  transition: all 0.3s;
} 