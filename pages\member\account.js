var app=getApp();
Page({

  data: {
      member: wx.getStorageSync('member'),
      // 所属村组数据
      ssczLevel1Options: [],
      ssczLevel2Options: [],
      sscz_level1: '',
      sscz_level2: ''
  },
  onLoad: function (options) {
    var member = wx.getStorageSync('member');
    if (member == "") {
      wx.reLaunch({ url: "../login/login" });
    }
    
    // 加载所属村组一级数据
    this.loadSsczLevel1Options();
  },
  onShow: function(){
    let member = wx.getStorageSync('member');
    this.setData({
      member: member
    }); 
  },
  
  // 加载所属村组一级选项
  loadSsczLevel1Options: function() {
    wx.request({
      url: app.globalData.http_api + 's=api&c=api&m=linkage_ld&mid=member&file=&code=sscz&parent_id=0',
      method: 'GET',
      success: (res) => {
        if (res.data && res.data.data && Array.isArray(res.data.data)) {
          this.setData({
            ssczLevel1Options: res.data.data
          });
          
          // 如果会员已有所属村组信息，进行初始化设置
          if (this.data.member && this.data.member.sscz) {
            this.initSsczSelection();
          }
        } else {
          console.error('加载所属村组一级选项失败:', res.data);
          wx.showToast({
            title: '加载村组数据失败',
            icon: 'none',
            duration: 2000
          });
        }
      },
      fail: (err) => {
        console.error('加载所属村组一级选项请求失败:', err);
      }
    });
  },
  
  // 初始化所属村组选择
  initSsczSelection: function() {
    const ssczId = this.data.member.sscz;
    
    // 查找一级选项中是否包含当前所属村组ID
    const level1Index = this.data.ssczLevel1Options.findIndex(item => item.region_id === ssczId);
    
    if (level1Index !== -1) {
      // 找到匹配的一级选项
      this.setData({
        sscz_level1: level1Index.toString()
      });
      return;
    }
    
    // 如果一级中没找到，则需要查找二级
    for (let i = 0; i < this.data.ssczLevel1Options.length; i++) {
      const parentId = this.data.ssczLevel1Options[i].region_id;
      
      // 加载二级数据并检查
      wx.request({
        url: app.globalData.http_api + 's=api&c=api&m=linkage_ld&mid=member&file=&code=sscz&parent_id=' + parentId,
        method: 'GET',
        success: (res) => {
          if (res.data && res.data.data && Array.isArray(res.data.data)) {
            const level2Index = res.data.data.findIndex(item => item.region_id === ssczId);
            
            if (level2Index !== -1) {
              // 找到匹配的二级选项
              this.setData({
                sscz_level1: i.toString(),
                ssczLevel2Options: res.data.data,
                sscz_level2: level2Index.toString()
              });
            }
          }
        }
      });
    }
  },
  
  // 加载所属村组二级选项
  loadSsczLevel2Options: function(parentId) {
    if (!parentId) {
      this.setData({
        ssczLevel2Options: [],
        sscz_level2: ''
      });
      return;
    }

    wx.request({
      url: app.globalData.http_api + 's=api&c=api&m=linkage_ld&mid=member&file=&code=sscz&parent_id=' + parentId,
      method: 'GET',
      success: (res) => {
        if (res.data && res.data.data && Array.isArray(res.data.data)) {
          this.setData({
            ssczLevel2Options: res.data.data
          });
        } else {
          console.error('加载所属村组二级选项失败:', res.data);
          wx.showToast({
            title: '加载村组数据失败',
            icon: 'none',
            duration: 2000
          });
        }
      },
      fail: (err) => {
        console.error('加载所属村组二级选项请求失败:', err);
      }
    });
  },

  // 所属村组一级选择事件
  changeSsczLevel1: function(e) {
    const level1Index = e.detail.value;
    const selectedItem = this.data.ssczLevel1Options[level1Index];
    const level1Id = selectedItem ? selectedItem.region_id : '';
    
    this.setData({
      sscz_level1: level1Index
    });
    
    // 加载对应的二级选项
    this.loadSsczLevel2Options(level1Id);
  },

  // 所属村组二级选择事件
  changeSsczLevel2: function(e) {
    const level2Index = e.detail.value;
    
    this.setData({
      sscz_level2: level2Index
    });
  },
  
  formBindsubmit: function (e) {
        var self = this;
        
        // 获取所属村组实际ID值
        let ssczValue = '';
        if (this.data.sscz_level2 && this.data.ssczLevel2Options.length > 0) {
          ssczValue = this.data.ssczLevel2Options[this.data.sscz_level2].region_id;
        } else if (this.data.sscz_level1) {
          ssczValue = this.data.ssczLevel1Options[this.data.sscz_level1].region_id;
        }
        
        var postParams = "is_ajax=1&"
            + "&data[name]=" + e.detail.value.name
            + "&data[email]=" + e.detail.value.email
            + "&data[description]=" + e.detail.value.description
            + "&data[sex]=" + e.detail.value.sex
            + "&data[sscz]=" + ssczValue;
            
        wx.request({//提交
            url: app.globalData.http_api + "s=member&c=account&m=index&api_auth_code=" + wx.getStorageSync('member_auth') + "&api_auth_uid=" +wx.getStorageSync('member_uid'),
            data: postParams,
            method: 'post',
            header: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            success: function (res) {
                if (res.data.code == 1) {
                    // 重新获取用户信息
                    // 储存会员信息
                    wx.removeStorageSync('member');
                    wx.setStorageSync('member', res.data.data);
                    
                    // 更新当前页面的数据
                    self.setData({
                        member: res.data.data
                    });
                    
                    // 使用Modal弹窗提示修改成功
                    wx.showModal({
                        title: '提示',
                        content: '修改信息成功',
                        showCancel: false,
                        success: function(res) {
                            if (res.confirm) {
                                // 用户点击确定后返回个人中心页面
                                wx.navigateBack({
                                    delta: 1
                                });
                            }
                        }
                    });
                }
                else {
                    wx.showModal({
                        showCancel: false,
                        content: res.data.msg
                    })
                }
            },
            fail: function(err) {
                console.log('请求失败:', err);
                wx.showToast({
                    title: '网络错误，请重试',
                    icon: 'none',
                    duration: 2000
                });
            }
        })
    },
})