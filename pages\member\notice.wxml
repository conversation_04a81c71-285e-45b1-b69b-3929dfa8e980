<view class="container">
  <!-- 顶部标签栏 -->
  <view class="tabs-container">
    <scroll-view scroll-x enable-flex class="tabs-scroll">
      <view class="tabs">
        <view class="tab {{currentTab == 0 ? 'active' : ''}}" bindtap="switchTab" data-tid="0">
          <text>全部</text>
          <view class="tab-line"></view>
        </view>
        <view class="tab {{currentTab == 1 ? 'active' : ''}}" bindtap="switchTab" data-tid="1">
          <text>系统</text>
          <view class="tab-line"></view>
        </view>
        <view class="tab {{currentTab == 2 ? 'active' : ''}}" bindtap="switchTab" data-tid="2">
          <text>用户</text>
          <view class="tab-line"></view>
        </view>
        <view class="tab {{currentTab == 3 ? 'active' : ''}}" bindtap="switchTab" data-tid="3">
          <text>内容</text>
          <view class="tab-line"></view>
        </view>
        <view class="tab {{currentTab == 4 ? 'active' : ''}}" bindtap="switchTab" data-tid="4">
          <text>应用</text>
          <view class="tab-line"></view>
        </view>
        <view class="tab {{currentTab == 5 ? 'active' : ''}}" bindtap="switchTab" data-tid="5">
          <text>交易</text>
          <view class="tab-line"></view>
        </view>
        <view class="tab {{currentTab == 6 ? 'active' : ''}}" bindtap="switchTab" data-tid="6">
          <text>订单</text>
          <view class="tab-line"></view>
        </view>
      </view>
    </scroll-view>
  </view>

  <!-- 通知列表 -->
  <scroll-view 
    scroll-y 
    class="notice-scroll" 
    bindscrolltolower="loadMore"
    refresher-enabled="{{true}}"
    refresher-triggered="{{refreshing}}"
    bindrefresherrefresh="onPullDownRefresh">
    
    <view class="notice-list">
      <block wx:for="{{listData}}" wx:key="id">
        <view class="notice-item {{item.is_read == 0 ? 'unread' : ''}}" bindtap="markAsRead" data-id="{{item.id}}">
          <view class="notice-header">
            <view class="notice-type-tag">{{item.type_name || '系统'}}</view>
            <text class="notice-time">{{item.inputtime}}</text>
          </view>
          <view class="notice-title">{{item.title}}</view>
          <view class="notice-content">{{item.content}}</view>
          <view class="notice-footer">
            <view class="notice-dot" wx:if="{{item.is_read == 0}}"></view>
            <view class="notice-action">
              <text class="action-text">查看详情</text>
              <image class="action-icon" src="../../icons/arrow-right.png" mode="aspectFit"></image>
            </view>
          </view>
        </view>
      </block>
    </view>

    <!-- 加载状态 -->
    <view class="loading-container" wx:if="{{loading && !hidden && listData.length > 0}}">
      <view class="loading">
        <view class="loading-spinner"></view>
        <text>加载中...</text>
      </view>
    </view>

    <!-- 无更多数据 -->
    <view class="no-more" wx:if="{{!hasMore && listData.length > 0}}">
      <view class="divider">
        <view class="divider-line"></view>
        <text>没有更多消息了</text>
        <view class="divider-line"></view>
      </view>
    </view>

    <!-- 空状态 -->
    <view class="empty-state" wx:if="{{listData.length == 0 && !loading}}">
      <image class="empty-icon" src="../../icons/empty-notice.png" mode="aspectFit"></image>
      <text class="empty-text">暂无消息</text>
      <view class="empty-action" bindtap="onPullDownRefresh">
        <text>下拉刷新</text>
      </view>
    </view>
  </scroll-view>
</view> 