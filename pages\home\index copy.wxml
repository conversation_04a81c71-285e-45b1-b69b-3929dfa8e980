<view class="index-body">
  <!-- 搜索框区域 -->
  <view class="search-section">
    <view class="search-container">
      <view class="search-box">
        <image class="search-icon" src="/images/search.png" mode="aspectFit"></image>
        <input class="search-input"
               placeholder="搜索村民姓名或身份证号..."
               value="{{searchKeyword}}"
               bindinput="onSearchInput"
               bindconfirm="onSearchConfirm"
               bindfocus="onSearchFocus"
               bindblur="onSearchBlur"
               confirm-type="search" />
        <view class="search-btn" bindtap="onSearchConfirm" wx:if="{{searchKeyword}}">
          <text>搜索</text>
        </view>
      </view>
      <!-- 搜索历史 -->
      <view class="search-history" wx:if="{{showSearchHistory && searchHistory.length > 0}}">
        <view class="history-header">
          <text class="history-title">搜索历史</text>
          <text class="clear-history" bindtap="clearSearchHistory">清空</text>
        </view>
        <view class="history-tags">
          <text class="history-tag"
                wx:for="{{searchHistory}}"
                wx:key="index"
                bindtap="selectHistoryKeyword"
                data-keyword="{{item}}">{{item}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 工单统计概览 -->
  <view class="statistics-section">
    <view class="stat-item">
      <text class="stat-num">{{totalTickets}}</text>
      <text class="stat-label">总工单数</text>
    </view>
    <view class="stat-item">
      <text class="stat-num">{{pendingTickets}}</text>
      <text class="stat-label">待处理</text>
    </view>
    <view class="stat-item">
      <text class="stat-num">{{processingTickets}}</text>
      <text class="stat-label">处理中</text>
    </view>
    <view class="stat-item">
      <text class="stat-num">{{completedTickets}}</text>
      <text class="stat-label">已完成</text>
    </view>
  </view>

  <!-- 快捷功能区 -->
  <view class="index-icons">
    <view class="flex-item" wx:for="{{icons}}" wx:key="item">
      <navigator url="{{item.url}}">
        <image class='image-icon' src="{{item.icon}}" />
        <view class='text-icon'>{{item.name}}</view>
      </navigator>
    </view>
  </view>

  <!-- 最新更新列表 -->
  <view class="index-row">
    <view class="section-header">
      <text class="section-title">最新更新</text>
      <view class="update-filter">
        <text class="filter-item {{updateFilter === 'all' ? 'active' : ''}}"
              bindtap="changeUpdateFilter"
              data-filter="all">全部</text>
        <text class="filter-item {{updateFilter === 'tickets' ? 'active' : ''}}"
              bindtap="changeUpdateFilter"
              data-filter="tickets">工单</text>
        <text class="filter-item {{updateFilter === 'notices' ? 'active' : ''}}"
              bindtap="changeUpdateFilter"
              data-filter="notices">公告</text>
      </view>
    </view>

    <!-- 最新更新内容 -->
    <view class="update-list">
      <block wx:for="{{filteredUpdates}}" wx:key="id">
        <view class="update-item" bindtap="goToUpdateDetail" data-item="{{item}}">
          <view class="update-type-badge {{item.type === 'ticket' ? 'ticket-badge' : 'notice-badge'}}">
            {{item.type === 'ticket' ? '工单' : '公告'}}
          </view>
          <view class="update-content">
            <view class="update-title">{{item.title}}</view>
            <view class="update-desc">{{item.description}}</view>
            <view class="update-meta">
              <text class="update-author">{{item.author}}</text>
              <text class="update-time">{{item.updateTime}}</text>
              <text class="update-status" wx:if="{{item.type === 'ticket'}}">{{item.status}}</text>
            </view>
          </view>
          <view class="update-arrow">
            <text class="arrow-text">></text>
          </view>
        </view>
      </block>

      <!-- 空状态 -->
      <view class="empty-state" wx:if="{{filteredUpdates.length === 0}}">
        <text class="empty-icon">📝</text>
        <text class="empty-text">暂无更新内容</text>
      </view>
    </view>

    <!-- 加载更多 -->
    <view class="load-more" wx:if="{{filteredUpdates.length > 0}}">
      <view class="loading" wx:if="{{updateIsLoading}}">加载中...</view>
      <view class="no-more" wx:if="{{!updateHasMore && !updateIsLoading}}">没有更多内容了</view>
      <view class="load-more-btn" wx:if="{{updateHasMore && !updateIsLoading}}" bindtap="loadMoreUpdates">加载更多</view>
    </view>
  </view>

  <!-- 测试用退出登录按钮 -->
  <view class="logout-section" wx:if="{{isLoggedIn}}">
    <button class="logout-btn" bindtap="logout">退出登录（测试）</button>
  </view>
</view>
