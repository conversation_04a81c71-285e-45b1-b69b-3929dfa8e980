<view class="tab-bar">
  <block wx:for="{{tabList}}" wx:key="index">
    <!-- 管理员Tab只在用户是管理员时显示 -->
    <view wx:if="{{!item.adminOnly || (item.adminOnly && isAdmin)}}" class="tab-bar-item {{selected === index ? 'active' : ''}}" data-index="{{index}}" bindtap="switchTab">
      <image src="{{selected === index ? item.selectedIconPath : item.iconPath}}" class="tab-bar-icon"></image>
      <view class="tab-bar-text">{{item.text}}</view>
    </view>
  </block>
</view> 