<!-- 用户信息页面 -->
<view class="container">
  <!-- 顶部背景 -->
  <view class="top-bg"></view>

  <!-- 用户基本信息卡片 -->
  <view class="user-card">
    <view class="user-info-content">
      <button class="avatar-wrapper" open-type="chooseAvatar" bind:chooseavatar="onChooseAvatar">
        <image src="{{avatarUrl || defaultAvatarUrl}}" class="user-avatar" mode="aspectFill"></image>
        <view class="edit-avatar-icon">
          <image src="../../icons/camera.png" class="camera-icon"></image>
        </view>
      </button>
      <view class="user-details">
        <input type="nickname" class="nickname-input" bindinput="nickInput" placeholder="请输入昵称" value="{{member.name}}" />
        <text class="account-id">账号: {{member.username || '未设置'}}</text>
      </view>
    </view>

    <!-- 账户数据 -->
    <view class="profile-stats">
      <view class="profile-stats-row">
        <view class="profile-stat-item">
          <text class="profile-stat-value">{{ssczName}}</text>
          <text class="profile-stat-label">所属村组</text>
        </view>
        <view class="profile-stat-divider"></view>
        <view class="profile-stat-item">
          <text class="profile-stat-value">{{member.score || 0}}</text>
          <text class="profile-stat-label">金币</text>
        </view>
      </view>
      <view class="profile-row-divider"></view>
      <view class="profile-stats-row">
        <view class="profile-stat-item">
          <text class="profile-stat-value">¥{{member.money || '0.00'}}</text>
          <text class="profile-stat-label">余额</text>
        </view>
        <view class="profile-stat-divider"></view>
        <view class="profile-stat-item">
          <text class="profile-stat-value">{{member.experience || 0}}</text>
          <text class="profile-stat-label">经验</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 会员信息列表 -->
  <view class="section-title">详细信息</view>
  <view class="info-list">
    <!-- 会员组别 -->
    <view class="info-item">
      <view class="info-label">
        <image class="info-icon" src="../../icons/vip.png" mode="aspectFit"></image>
        <text>会员组别</text>
      </view>
      <view class="info-value">
        <block wx:for="{{member.group}}" wx:key="index" wx:for-item="group">
          <text class="group-tag">{{group.group_name}}</text>
        </block>
      </view>
    </view>

    <!-- 认证状态 -->
    <view class="info-item">
      <view class="info-label">
        <image class="info-icon" src="../../icons/verify.png" mode="aspectFit"></image>
        <text>认证状态</text>
      </view>
      <view class="info-value">
        <view class="status-badge verify {{member.is_verify == 1 ? 'active' : ''}}">
          <text>{{member.is_verify == 1 ? '已认证' : '未认证'}}</text>
        </view>
      </view>
    </view>

    <!-- 手机认证 -->
    <view class="info-item">
      <view class="info-label">
        <image class="info-icon" src="../../icons/phone.png" mode="aspectFit"></image>
        <text>手机认证</text>
      </view>
      <view class="info-value">
        <view class="status-badge mobile {{member.is_mobile == 1 ? 'active' : ''}}">
          <text>{{member.is_mobile == 1 ? '已认证' : '未认证'}}</text>
        </view>
      </view>
    </view>

    <!-- 邮箱认证 -->
    <view class="info-item">
      <view class="info-label">
        <image class="info-icon" src="../../icons/email.png" mode="aspectFit"></image>
        <text>邮箱认证</text>
      </view>
      <view class="info-value">
        <view class="status-badge email {{member.is_email == 1 ? 'active' : ''}}">
          <text>{{member.is_email == 1 ? '已认证' : '未认证'}}</text>
        </view>
      </view>
    </view>

    <!-- 头像认证 -->
    <view class="info-item">
      <view class="info-label">
        <image class="info-icon" src="../../icons/avatar.png" mode="aspectFit"></image>
        <text>头像认证</text>
      </view>
      <view class="info-value">
        <view class="status-badge avatar {{member.is_avatar == 1 ? 'active' : ''}}">
          <text>{{member.is_avatar == 1 ? '已认证' : '未认证'}}</text>
        </view>
      </view>
    </view>

    <!-- 资料完整度 -->
    <view class="info-item">
      <view class="info-label">
        <image class="info-icon" src="../../icons/profile.png" mode="aspectFit"></image>
        <text>资料完整</text>
      </view>
      <view class="info-value">
        <view class="status-badge complete {{member.is_complete == 1 ? 'active' : ''}}">
          <text>{{member.is_complete == 1 ? '已完善' : '未完善'}}</text>
        </view>
      </view>
    </view>

    <!-- 账号状态 -->
    <view class="info-item">
      <view class="info-label">
        <image class="info-icon" src="../../icons/lock.png" mode="aspectFit"></image>
        <text>账号状态</text>
      </view>
      <view class="info-value">
        <view class="status-badge lock {{member.is_lock == 1 ? 'active' : ''}}">
          <text>{{member.is_lock == 1 ? '已锁定' : '正常'}}</text>
        </view>
      </view>
    </view>

    <!-- 安全邮箱 -->
    <view class="info-item">
      <view class="info-label">
        <image class="info-icon" src="../../icons/email.png" mode="aspectFit"></image>
        <text>安全邮箱</text>
      </view>
      <view class="info-value">
        <text>{{member.email || '未设置'}}</text>
      </view>
    </view>

    <!-- 手机号码 -->
    <view class="info-item">
      <view class="info-label">
        <image class="info-icon" src="../../icons/phone.png" mode="aspectFit"></image>
        <text>手机号码</text>
      </view>
      <view class="info-value">
        <text>{{member.phone || '未绑定'}}</text>
      </view>
    </view>

    <!-- 注册时间 -->
    <view class="info-item">
      <view class="info-label">
        <image class="info-icon" src="../../icons/time.png" mode="aspectFit"></image>
        <text>注册时间</text>
      </view>
      <view class="info-value">
        <text>{{member.regtime || '-'}}</text>
      </view>
    </view>

    <!-- 上次登录 -->
    <view class="info-item">
      <view class="info-label">
        <image class="info-icon" src="../../icons/login.png" mode="aspectFit"></image>
        <text>上次登录</text>
      </view>
      <view class="info-value">
        <text>{{member.lastlogintime || '-'}}</text>
      </view>
    </view>
  </view>

  <!-- 账号安全 -->
  <view class="section-title">账号安全</view>
  <view class="info-list">
    <!-- 修改资料 -->
    <navigator url="../member/account" hover-class="navigator-hover">
      <view class="info-item">
        <view class="info-label">
          <image class="info-icon" src="../../icons/account.png" mode="aspectFit"></image>
          <text>修改资料</text>
        </view>
        <view class="info-value">
          <image src="../../icons/right-back.png" class="arrow-icon"></image>
        </view>
      </view>
    </navigator>

    <!-- 修改密码 -->
    <navigator url="../member/password" hover-class="navigator-hover">
      <view class="info-item">
        <view class="info-label">
          <image class="info-icon" src="../../icons/password.png" mode="aspectFit"></image>
          <text>修改密码</text>
        </view>
        <view class="info-value">
          <image src="../../icons/right-back.png" class="arrow-icon"></image>
        </view>
      </view>
    </navigator>
  </view>
</view>







