/* 整体容器 */
.container {
  padding: 0 0 30rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 头部区域 */
.header {
  background-color: #fff;
  padding: 20rpx 30rpx;
  position: sticky;
  top: 0;
  z-index: 10;
}

.page-title {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
  color: #333;
}

.search-bar {
  display: flex;
  align-items: center;
  background-color: #f5f5f5;
  border-radius: 40rpx;
  padding: 0 20rpx;
  height: 70rpx;
  position: relative;
}

.search-bar input {
  flex: 1;
  height: 100%;
  font-size: 28rpx;
  padding-left: 10rpx;
}

.search-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60rpx;
  height: 60rpx;
}

.search-icon {
  width: 40rpx;
  height: 40rpx;
}

/* 分类标签 */
.category-tabs {
  display: flex;
  white-space: nowrap;
  background-color: #fff;
  padding: 20rpx 0;
  border-bottom: 1px solid #eee;
}

.category-item {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 10rpx 30rpx;
  margin: 0 10rpx;
  font-size: 28rpx;
  color: #666;
  border-radius: 30rpx;
  flex-shrink: 0;
}

.category-item.active {
  background-color: #1677ff;
  color: #fff;
}

/* 问题列表 */
.question-list {
  padding: 20rpx;
}

.question-item {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.question-header {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.user-avatar {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  margin-right: 15rpx;
}

.question-info {
  flex: 1;
}

.username {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

.post-time {
  font-size: 24rpx;
  color: #999;
  margin-left: 10rpx;
}

.question-status {
  font-size: 24rpx;
  color: #ff9900;
  padding: 8rpx 15rpx;
  border-radius: 20rpx;
  background-color: rgba(255, 153, 0, 0.1);
}

.question-status.solved {
  color: #52c41a;
  background-color: rgba(82, 196, 26, 0.1);
}

.question-status.inprocess {
  color: #1677ff;
  background-color: rgba(22, 119, 255, 0.1);
}

.question-title {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 15rpx;
  color: #333;
}

.question-content {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 20rpx;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
  overflow: hidden;
}

.question-main {
  display: block;
  width: 100%;
}

/* 问题底部统计信息 */
.question-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 10px;
  font-size: 24rpx;
  color: #666;
  padding-top: 10rpx;
  flex-wrap: wrap;
}

.stat-item {
  display: flex;
  align-items: center;
  margin-right: 15rpx;
}

.stat-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 6rpx;
}

/* 点赞按钮样式 */
.digg-buttons {
  display: flex;
  flex: 1;
  justify-content: flex-end;
  gap: 20rpx;
}

.digg-item {
  display: flex;
  align-items: center;
  padding: 4rpx 12rpx;
  border-radius: 30rpx;
  background-color: #f5f5f5;
}

.digg-icon {
  width: 28rpx;
  height: 28rpx;
  margin-right: 6rpx;
}

.helpful {
  color: #07c160;
}

.unhelpful {
  color: #999;
}

/* 加载更多 */
.loading-status {
  text-align: center;
  padding: 30rpx 0;
  font-size: 28rpx;
  color: #999;
}

.loading-more, .no-more, .empty-list {
  padding: 15rpx 0;
}

.loading-more {
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 浮动按钮 */
.float-btn {
  position: fixed;
  right: 30rpx;
  bottom: 100rpx;
  width: 180rpx;
  height: 80rpx;
  background-color: #1677ff;
  border-radius: 40rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 100;
  padding: 0 20rpx;
}

.add-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 10rpx;
}

.float-btn-text {
  color: #fff;
  font-size: 28rpx;
  font-weight: bold;
} 