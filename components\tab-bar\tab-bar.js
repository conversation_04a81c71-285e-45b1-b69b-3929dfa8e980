Component({
  /**
   * 组件的属性列表
   */
  properties: {
    selected: {
      type: Number,
      value: 0
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    isAdmin: false,
    tabList: [
      {
        pagePath: "/pages/home/<USER>",
        text: "首页",
        iconPath: "/icons/home.png",
        selectedIconPath: "/icons/home-on.png"
      },
      {
        pagePath: "/pages/workorder/list",
        text: "工单",
        iconPath: "/icons/news.png",
        selectedIconPath: "/icons/news-on.png"
      },
      {
        pagePath: "/pages/workorder/post",
        text: "发布",
        iconPath: "/icons/post.png",
        selectedIconPath: "/icons/post-on.png"
      },
      {
        pagePath: "/pages/caiwu/index",
        text: "财务",
        iconPath: "/icons/finance.png",
        selectedIconPath: "/icons/finance-on.png",
        adminOnly: true
      },
      {
        pagePath: "/pages/member/index",
        text: "会员",
        iconPath: "/icons/user.png",
        selectedIconPath: "/icons/user-on.png"
      }
    ]
  },

  /**
   * 组件的方法列表
   */
  methods: {
    switchTab(e) {
      const dataset = e.currentTarget.dataset;
      const index = dataset.index;
      const item = this.data.tabList[index];
      
      // 如果是财务Tab，需要判断权限
      if (item.adminOnly && !this.data.isAdmin) {
        wx.showToast({
          title: '只有管理员才能访问财务功能',
          icon: 'none'
        });
        return;
      }
      
      wx.switchTab({
        url: item.pagePath
      });
    }
  },

  lifetimes: {
    attached: function() {
      // 获取用户信息，检查是否是管理员
      try {
        const member = wx.getStorageSync('member');
        if (member && member.id) {
          const isAdmin = member.is_admin && parseInt(member.is_admin) > 0;
          this.setData({
            isAdmin: isAdmin
          });
        } else {
          this.setData({
            isAdmin: false
          });
        }
      } catch (err) {
        console.error('获取用户信息失败:', err);
        this.setData({
          isAdmin: false
        });
      }
    }
  },

  pageLifetimes: {
    show: function() {
      // 页面显示时再次检查管理员权限
      try {
        const member = wx.getStorageSync('member');
        if (member && member.id) {
          const isAdmin = member.is_admin && parseInt(member.is_admin) > 0;
          this.setData({
            isAdmin: isAdmin
          });
        } else {
          this.setData({
            isAdmin: false
          });
        }
      } catch (err) {
        console.error('获取用户信息失败:', err);
        this.setData({
          isAdmin: false
        });
      }
    }
  }
}) 