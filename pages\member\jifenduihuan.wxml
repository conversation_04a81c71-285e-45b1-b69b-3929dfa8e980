<view class="container">
  <!-- 顶部会员信息卡片 -->
  <view class="member-card" wx:if="{{member}}">
    <view class="member-info">
      <view class="member-avatar">
        <image class="avatar-img" src="{{member.avatar || '../../icons/user.png'}}" mode="aspectFill"></image>
      </view>
      <view class="member-details">
        <view class="member-name">{{member.username || '会员用户'}}</view>
        <view class="member-id">ID: {{member.uid || ''}}</view>
      </view>
    </view>
    <view class="member-score-box">
      <text class="score-label">当前积分</text>
      <text class="score-value">{{member.score || 0}}</text>
    </view>
  </view>

  <!-- 未登录提示 -->
  <view class="login-tip" wx:if="{{!member}}">
    <text class="tip-text">请先登录查看您的积分兑换信息</text>
    <navigator url="/pages/login/login" class="login-btn">立即登录</navigator>
  </view>

  <!-- 标签页导航 -->
  <view class="tab-nav">
    <view class="tab-item {{activeTab === 'exchange' ? 'active' : ''}}" bindtap="switchTab" data-tab="exchange">
      <text class="tab-text">可兑换商品</text>
    </view>
    <view class="tab-item {{activeTab === 'my-exchange' ? 'active' : ''}}" bindtap="switchTab" data-tab="my-exchange">
      <text class="tab-text">我的兑换</text>
    </view>
    <view class="tab-item {{activeTab === 'history' ? 'active' : ''}}" bindtap="switchTab" data-tab="history">
      <text class="tab-text">兑换记录</text>
    </view>
  </view>

  <!-- 商品列表 -->
  <view class="tab-content" hidden="{{activeTab !== 'exchange'}}">
    <view class="exchange-list">
      <view class="empty-tip" wx:if="{{exchangeList.length === 0}}">
        暂无可兑换商品
      </view>
      
      <view class="exchange-item" wx:for="{{exchangeList}}" wx:key="id">
        <!-- 商品基本信息 -->
        <view class="item-header">
          <image class="item-thumb" src="{{item.thumb}}" mode="aspectFill"></image>
          <view class="item-info">
            <view class="item-title">{{item.title}}</view>
            <view class="item-description">{{item.description}}</view>
          </view>
        </view>
        
        <!-- 已售罄标签 -->
        <view class="sold-out-badge" wx:if="{{!item.hasStock}}">已售罄</view>
        <!-- 库存充足标签 -->
        <view class="stock-abundant-badge" wx:if="{{item.hasStock && item.stock > 10}}">充足</view>
        <!-- 库存紧张标签 -->
        <view class="stock-low-badge" wx:if="{{item.hasStock && item.stock <= 10}}">紧张</view>

        <!-- 价格和规格信息 -->
        <view class="price-section">
          <view class="price-row">
            <view class="base-price">
              <text class="price-label">基础价格</text>
              <text class="price-value">{{item.price}}积分</text>
            </view>
            <view class="stock-info">
              <text class="stock-label">库存</text>
              <text class="stock-value {{item.hasStock ? '' : 'no-stock'}}">{{item.hasStock ? item.stock : '已售罄'}}</text>
            </view>
          </view>

          <!-- 规格选择 -->
          <view class="sku-section" wx:if="{{item.priceSku && Object.keys(item.priceSku).length > 0}}">
            <view class="sku-title">规格选择</view>
            <view class="sku-list">
              <view class="sku-item {{item.selectedSku === key ? 'selected' : ''}}" 
                    wx:for="{{item.priceSku}}" 
                    wx:for-item="price" 
                    wx:for-index="key" 
                    wx:key="key"
                    data-index="{{index}}"
                    data-sku="{{key}}"
                    bindtap="selectSku">
                <text class="sku-name">规格{{key}}</text>
                <text class="sku-price">{{price}}积分</text>
              </view>
            </view>
          </view>
        </view>

        <!-- 兑换按钮 -->
        <view class="exchange-btn {{!item.hasStock || loading ? 'disabled' : ''}}" 
              data-item="{{item}}" 
              bindtap="handleExchange">
          <text class="btn-text">{{item.hasStock ? '立即兑换' : '已兑完'}}</text>
          <text class="btn-price" wx:if="{{item.hasStock}}">{{item.selectedPrice || item.price}}积分</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 我的兑换 -->
  <view class="tab-content" hidden="{{activeTab !== 'my-exchange'}}">
    <view class="my-exchange-list">
      <view class="empty-tip" wx:if="{{myExchangeList.length === 0}}">
        暂无兑换中的商品
      </view>
      
      <view class="my-exchange-item" wx:for="{{myExchangeList}}" wx:key="id">
        <view class="my-exchange-main">
          <view class="my-exchange-title">{{item.title || '商品' + item.cid}}</view>
          <view class="my-exchange-info">
            <text class="my-exchange-label">兑换时间：</text>
            <text class="my-exchange-value">{{item.inputtime || '未知'}}</text>
          </view>
          <view class="my-exchange-info">
            <text class="my-exchange-label">兑换状态：</text>
            <text class="my-exchange-value status-{{item.status}}">{{item.statusText || '处理中'}}</text>
          </view>
        </view>
        <view class="my-exchange-price">{{item.price}}积分</view>
      </view>
    </view>
  </view>

  <!-- 兑换历史 -->
  <view class="tab-content" hidden="{{activeTab !== 'history'}}">
    <view class="history-list">
      <view class="empty-tip" wx:if="{{historyList.length === 0}}">
        暂无兑换记录
      </view>
      
      <view class="history-item" wx:for="{{historyList}}" wx:key="id">
        <view class="history-main">
          <view class="history-title">{{item.title || '商品' + item.cid}}</view>
          <view class="history-info">
            <text class="history-label">兑换时间：</text>
            <text class="history-value">{{item.inputtime || '未知'}}</text>
          </view>
          <view class="history-info">
            <text class="history-label">兑换人：</text>
            <text class="history-value">{{item.name || '未知'}}</text>
          </view>
        </view>
        <view class="history-price">{{item.price}}积分</view>
      </view>
    </view>
  </view>

  <!-- 加载中提示 -->
  <view class="loading-container" wx:if="{{loading}}">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>
</view> 