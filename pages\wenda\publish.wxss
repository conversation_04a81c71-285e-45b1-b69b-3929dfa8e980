/* 整体容器 */
.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f7f8fa;
  padding-bottom: 30rpx;
}

/* 头部区域 */
.header {
  padding: 40rpx 30rpx 20rpx;
  background-color: #ffffff;
  border-bottom: 1rpx solid #eaeaea;
  margin-bottom: 20rpx;
}

.page-title {
  display: flex;
  flex-direction: column;
}

.title-text {
  font-size: 36rpx;
  font-weight: 600;
  color: #333333;
}

.title-description {
  font-size: 24rpx;
  color: #888888;
  margin-top: 10rpx;
}

/* 表单容器 */
.form-container {
  padding: 0 30rpx;
}

/* 卡片式表单项 */
.form-card {
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.form-section-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333333;
  margin-bottom: 20rpx;
  padding-bottom: 16rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

/* 表单项 */
.form-item {
  margin-bottom: 30rpx;
}

.form-item:last-child {
  margin-bottom: 0;
}

.form-label {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.label-text {
  font-size: 28rpx;
  font-weight: 500;
  color: #333333;
}

.label-required {
  font-size: 28rpx;
  color: #ff4d4f;
  margin-left: 8rpx;
}

.label-tip {
  font-size: 24rpx;
  color: #999999;
  margin-left: 16rpx;
}

/* 输入框样式 */
.form-input {
  width: 100%;
  height: 88rpx;
  background-color: #f7f8fa;
  border-radius: 8rpx;
  padding: 0 24rpx;
  font-size: 28rpx;
  color: #333333;
  box-sizing: border-box;
  border: 1rpx solid #eaeaea;
}

/* 文本域样式 */
.form-textarea {
  width: 100%;
  height: 240rpx;
  background-color: #f7f8fa;
  border-radius: 8rpx;
  padding: 24rpx;
  font-size: 28rpx;
  color: #333333;
  box-sizing: border-box;
  border: 1rpx solid #eaeaea;
}

/* 字数统计 */
.char-count {
  text-align: right;
  font-size: 24rpx;
  color: #999999;
  margin-top: 12rpx;
}

.char-count.valid {
  color: #52c41a;
}

.char-count.invalid {
  color: #ff4d4f;
}

/* 选择器样式 */
.picker-view {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  height: 88rpx;
  background-color: #f7f8fa;
  border-radius: 8rpx;
  padding: 0 24rpx;
  font-size: 28rpx;
  color: #999999;
  box-sizing: border-box;
  border: 1rpx solid #eaeaea;
}

.picker-view.selected {
  color: #333333;
}

.down-arrow {
  color: #666666;
  font-size: 24rpx;
}

/* 新增的分类选择器样式 */
.category-container, .subcategory-container {
  width: 100%;
  margin-bottom: 20rpx;
}

.category-scroll {
  width: 100%;
  white-space: nowrap;
}

.category-list {
  display: inline-flex;
  padding: 10rpx 0;
}

.category-item {
  display: inline-block;
  padding: 12rpx 24rpx;
  margin-right: 16rpx;
  font-size: 28rpx;
  color: #666666;
  background-color: #f7f8fa;
  border-radius: 32rpx;
  transition: all 0.3s ease;
  white-space: nowrap;
  border: 2rpx solid transparent;
}

.category-item.active {
  color: #1890ff;
  background-color: rgba(24, 144, 255, 0.1);
  border-color: rgba(24, 144, 255, 0.3);
  font-weight: 500;
}

.subcategory-container .category-item {
  background-color: #ffffff;
  border: 1rpx solid #eaeaea;
  padding: 10rpx 20rpx;
  font-size: 26rpx;
}

.subcategory-container .category-item.active {
  border-color: #1890ff;
}

.selected-category {
  display: flex;
  align-items: flex-start;
  padding: 16rpx 24rpx;
  background-color: #f0f7ff;
  border-radius: 8rpx;
  margin-top: 10rpx;
  border-left: 6rpx solid #1890ff;
}

.selected-category-label {
  font-size: 26rpx;
  color: #333333;
  font-weight: 500;
  width: 140rpx;
  flex-shrink: 0;
}

.selected-category-value {
  font-size: 26rpx;
  color: #1890ff;
  font-weight: 500;
  flex: 1;
}

.loading-view, .error-view {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 88rpx;
  background-color: #f7f8fa;
  border-radius: 8rpx;
}

.loading-text {
  font-size: 28rpx;
  color: #999999;
}

.error-text {
  font-size: 28rpx;
  color: #ff4d4f;
}

/* 图片上传区域 */
.image-uploader {
  width: 100%;
}

.image-list {
  display: flex;
  flex-wrap: wrap;
}

.image-item {
  position: relative;
  width: 180rpx;
  height: 180rpx;
  margin-right: 20rpx;
  margin-bottom: 20rpx;
  border-radius: 8rpx;
  overflow: hidden;
}

.image-item image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.delete-btn {
  position: absolute;
  top: 0;
  right: 0;
  width: 40rpx;
  height: 40rpx;
  background-color: rgba(0, 0, 0, 0.6);
  color: #ffffff;
  font-size: 28rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  border-bottom-left-radius: 8rpx;
}

.upload-btn {
  width: 180rpx;
  height: 180rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-color: #f7f8fa;
  border: 1rpx dashed #d9d9d9;
  border-radius: 8rpx;
  margin-bottom: 20rpx;
}

.upload-btn image {
  width: 60rpx;
  height: 60rpx;
  margin-bottom: 10rpx;
}

.upload-text {
  font-size: 24rpx;
  color: #999999;
}

/* 提示信息区域 */
.tips-section {
  margin: 30rpx 0;
  padding: 0 10rpx;
}

.tip-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 16rpx;
}

.tip-icon {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 32rpx;
  height: 32rpx;
  background-color: #1890ff;
  color: #ffffff;
  font-size: 24rpx;
  font-style: italic;
  border-radius: 50%;
  margin-right: 12rpx;
}

.tip-text {
  font-size: 24rpx;
  color: #666666;
  flex: 1;
}

/* 提交按钮区域 */
.form-action {
  margin-top: 40rpx;
}

.submit-btn {
  width: 100%;
  height: 88rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 32rpx;
  color: #ffffff;
  border-radius: 44rpx;
  background: linear-gradient(90deg, #1890ff, #3f6df5);
  box-shadow: 0 6rpx 16rpx rgba(24, 144, 255, 0.3);
  transition: all 0.3s ease;
}

.submit-btn.active:active {
  transform: scale(0.98);
  box-shadow: 0 3rpx 8rpx rgba(24, 144, 255, 0.3);
}

.submit-btn.disabled {
  background: linear-gradient(90deg, #cccccc, #f0f0f0);
  color: #999999;
  box-shadow: none;
}

.btn-text {
  font-weight: 500;
} 