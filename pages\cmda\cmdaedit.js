// 获取应用实例
const app = getApp();

Page({
  /**
   * 页面的初始数据
   */
  data: {
    loading: true,
    villager: {},
    villagerId: null,
    
    // 表单相关
    genderOptions: ['男', '女'],
    genderIndex: 0,
    statusOptions: ['有效', '已销户'],
    statusIndex: 0,
    
    // 户籍属性选项
    hujishuxingOptions: [
      '党员', '孤儿', '重点优抚', '计生优抚', '脱贫户', 
      '低保户', '残疾户', '五保户', '兵役', '销户', '特困', '监测户'
    ],
    selectedHujishuxing: [], // 选中的户籍属性
    
    // 其他信息字段
    otherInfoFields: [],
    

    
    // 保存原始响应数据
    originalData: null
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    if (options.id) {
      this.setData({ villagerId: options.id });
      this.loadVillagerData(options.id);
    } else {
      this.setData({ 
        loading: false,
        villager: {
          title: '',
          xingbie: '',
          sfzhm: '',
          shoujihaoma: '',
          hujidizhi: '',
          hujishuxing: '',
          jtgx: '',
          isXiaohu: false
        }
      });
    }
  },

  /**
   * 加载村民数据
   */
  loadVillagerData: function(id) {
    // 获取验证信息
    const member_uid = wx.getStorageSync('member_uid');
    const member_auth = wx.getStorageSync('member_auth');
    
    if (!member_uid || !member_auth) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      this.setData({ loading: false });
      return;
    }
    
    // 构建API请求URL
    const requestUrl = `${app.globalData.http_api}s=cmda&c=show&id=${id}&api_call_function=module_show&api_auth_uid=${member_uid}&api_auth_code=${member_auth}`;
    
    wx.showLoading({ title: '加载中...' });
    
    wx.request({
      url: requestUrl,
      method: 'GET',
      header: { 'content-type': 'application/json' },
      success: res => {
        console.log('获取村民详情结果:', res.data);
        
        if (res.data.code == 1 && res.data.data) {
          const villagerData = res.data.data;
          
          // 保存原始完整数据对象，以便后续提交时使用
          this.setData({ originalData: villagerData });
          
          // 处理性别索引
          const genderIndex = villagerData.xingbie === '女' ? 1 : 0;
          
          // 处理状态索引
          const statusIndex = villagerData.isXiaohu ? 1 : 0;
          
          // 处理照片数据和其他信息字段
          this._processVillagerData(villagerData);
        } else {
          wx.showToast({
            title: res.data.msg || '加载失败',
            icon: 'none'
          });
          this.setData({ loading: false });
        }
        wx.hideLoading();
      },
      fail: err => {
        console.error('加载失败:', err);
        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        });
        this.setData({ loading: false });
        wx.hideLoading();
      }
    });
  },

  /**
   * 处理村民数据
   * @private
   */
  _processVillagerData: function(villagerData) {
    // 处理其他信息字段
    let otherInfoFields = [];
    if (villagerData.otherInfo && Array.isArray(villagerData.otherInfo)) {
      otherInfoFields = villagerData.otherInfo.map(item => ({
        label: item.label,
        value: item.value,
        field: item.field
      }));
    }
    
    // 处理户籍属性复选框数据
    let selectedHujishuxing = [];
    if (villagerData.hujishuxing) {
      const hujishuxingStr = villagerData.hujishuxing;
      // 处理不同分隔符
      if (hujishuxingStr.includes('、')) {
        selectedHujishuxing = hujishuxingStr.split('、');
      } else if (hujishuxingStr.includes(',')) {
        selectedHujishuxing = hujishuxingStr.split(',');
      } else if (hujishuxingStr.includes('，')) {
        selectedHujishuxing = hujishuxingStr.split('，');
      } else if (hujishuxingStr.trim()) {
        // 单个值
        selectedHujishuxing = [hujishuxingStr];
      }
      // 清理数组元素
      selectedHujishuxing = selectedHujishuxing.map(item => item.trim()).filter(item => item);
    }
    
    // 处理照片数据
    const photoData = this._processPhotoData(villagerData);
    
    // 更新状态
    this.setData({
      loading: false,
      villager: villagerData,
      genderIndex: villagerData.xingbie === '女' ? 1 : 0,
      statusIndex: villagerData.isXiaohu ? 1 : 0,
      otherInfoFields,
      selectedHujishuxing,
      thumbUrl: photoData.thumbUrl,
      ...photoData
    });
  },
  
  /**
   * 处理照片数据
   * @private
   */
  _processPhotoData: function(villagerData) {
    // 处理照片数据
    let tempGrzpList = this._processPhotoList(villagerData.grzp, '身份证件照片');
    let tempFwzpList = this._processPhotoList(villagerData.fwzp, '房屋照片');
    let tempQtzjzpList = this._processPhotoList(villagerData.qtzjzp, '证件照片');
    let tempGczpList = this._processPhotoList(villagerData.gczp, '改厕照片');
    
    // 处理缩略图数据
    let thumbUrl = '';
    if (villagerData.thumb) {
      console.log('原始缩略图数据类型:', typeof villagerData.thumb, 
                 Array.isArray(villagerData.thumb) ? '是数组' : '不是数组');
      
      if (typeof villagerData.thumb === 'object' && villagerData.thumb.file) {
        // 如果是对象格式，提取file字段
        thumbUrl = villagerData.thumb.file;
      } else if (Array.isArray(villagerData.thumb) && villagerData.thumb.length > 0) {
        // 如果是数组格式
        if (typeof villagerData.thumb[0] === 'object') {
          thumbUrl = villagerData.thumb[0].file || villagerData.thumb[0].url || villagerData.thumb[0];
        } else {
          thumbUrl = villagerData.thumb[0];
        }
      } else {
        // 如果是字符串格式，直接使用
        thumbUrl = villagerData.thumb;
      }
      
      console.log('处理后的缩略图URL:', thumbUrl);
    }
    
    return { tempGrzpList, tempFwzpList, tempQtzjzpList, tempGczpList, thumbUrl };
  },
  
  /**
   * 处理单类照片列表
   * @private
   */
  _processPhotoList: function(photoData, photoType) {
    let photoList = [];
    
    if (!photoData) return photoList;
    
    console.log(`原始${photoType}数据类型:`, typeof photoData, 
                Array.isArray(photoData) ? '是数组' : '不是数组');
    
    if (typeof photoData === 'string') {
      // 处理字符串格式
      photoList = photoData.split(',').filter(url => url && url.trim() !== '');
    } else if (Array.isArray(photoData)) {
      if (photoData.length > 0) {
        // 处理数组格式
        if (typeof photoData[0] === 'object') {
          // 如果是对象数组格式，提取file字段
          photoList = photoData.map(item => item.file || item.url || item);
        } else {
          // 如果是字符串数组，直接使用
          photoList = photoData;
        }
      }
    }
    
    console.log(`处理后的${photoType}:`, photoList);
    return photoList;
  },

  /**
   * 输入框事件处理
   */
  inputHandler: function(e) {
    const field = e.currentTarget.dataset.field;
    const value = e.detail.value;
    
    const data = {};
    data[`villager.${field}`] = value;
    this.setData(data);
  },

  /**
   * 选择器事件处理
   */
  pickerChange: function(e) {
    const field = e.currentTarget.dataset.field;
    const value = e.detail.value;
    
    if (field === 'xingbie') {
      this.setData({
        genderIndex: value,
        'villager.xingbie': this.data.genderOptions[value]
      });
    } else if (field === 'isXiaohu') {
      this.setData({
        statusIndex: value,
        'villager.isXiaohu': value === '1'
      });
    }
  },

  /**
   * 户籍属性复选框选择事件
   */
  checkboxChange: function(e) {
    const values = e.detail.value;
    
    this.setData({
      selectedHujishuxing: values,
      'villager.hujishuxing': values.join('、')
    });
  },

  /**
   * 防止checkbox点击事件冒泡
   */
  preventTap: function(e) {
    return false;
  },

  /**
   * 切换单个复选框
   */
  toggleCheckbox: function(e) {
    const value = e.currentTarget.dataset.value;
    let selectedHujishuxing = [...this.data.selectedHujishuxing]; // 创建数组副本
    
    // 检查该值是否已在数组中
    const index = selectedHujishuxing.indexOf(value);
    
    if (index === -1) {
      // 值不在数组中，添加它
      selectedHujishuxing.push(value);
    } else {
      // 值已在数组中，移除它
      selectedHujishuxing.splice(index, 1);
    }
    
    // 更新数据
    this.setData({
      selectedHujishuxing: selectedHujishuxing,
      'villager.hujishuxing': selectedHujishuxing.join('、')
    });
  },

  /**
   * 其他信息处理
   */
  otherInfoHandler: function(e) {
    const index = e.currentTarget.dataset.index;
    const value = e.detail.value;
    
    let otherInfoFields = [...this.data.otherInfoFields];
    otherInfoFields[index].value = value;
    
    this.setData({ otherInfoFields });
  },

  /**
   * 添加新字段
   */
  addOtherInfoField: function() {
    wx.showModal({
      title: '添加新字段',
      editable: true,
      placeholderText: '请输入字段名称',
      success: res => {
        if (res.confirm && res.content) {
          let otherInfoFields = [...this.data.otherInfoFields];
          
          // 生成字段名
          const fieldName = 'field_' + Date.now();
          
          otherInfoFields.push({
            label: res.content,
            value: '',
            field: fieldName
          });
          
          this.setData({ otherInfoFields });
        }
      }
    });
  },



  /**
   * 取消编辑
   */
  cancelEdit: function() {
    wx.showModal({
      title: '提示',
      content: '确定放弃编辑？未保存的内容将丢失',
      success: function(res) {
        if (res.confirm) {
          wx.navigateBack();
        }
      }
    });
  },






  /**
   * 保存村民信息
   */
  saveVillager: function() {
    // 基本验证
    if (!this.data.villager.title) {
      wx.showToast({
        title: '请填写姓名',
        icon: 'none'
      });
      return;
    }
    
    // 显示加载提示
    wx.showLoading({
      title: '准备上传...',
      mask: true
    });
    
    // 获取验证信息
    const member_uid = wx.getStorageSync('member_uid');
    const member_auth = wx.getStorageSync('member_auth');
    
    if (!member_uid || !member_auth) {
      wx.hideLoading();
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }
    
    // 显示保存提示
    wx.showLoading({
      title: '保存档案...',
      mask: true
    });

    // 构建表单数据（不包含图片）
    const formData = this._buildFormData();

    // 构建API请求URL - 按照网页端的修改地址格式
    const requestUrl = `${app.globalData.http_api}s=member&app=cmda&c=home&m=edit&id=${this.data.villagerId}&api_auth_uid=${member_uid}&api_auth_code=${member_auth}`;

    console.log('修改档案URL:', requestUrl);

    // 提交数据
    wx.request({
      url: requestUrl,
      method: 'POST',
      data: formData,
      header: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      success: res => {
        wx.hideLoading();
        console.log('修改结果:', res.data);

        if (res.data.code == 1) {
          wx.showToast({
            title: '修改成功',
            icon: 'success'
          });

          // 修改成功，返回上一页并刷新
          setTimeout(() => {
            // 返回上一页
            let pages = getCurrentPages();
            let prevPage = pages[pages.length - 2];
            // 如果存在上一页
            if (prevPage) {
              // 调用上一页的刷新方法
              if (typeof prevPage.loadDetail === 'function') {
                prevPage.loadDetail();
              }
            }
            wx.navigateBack();
          }, 1500);
        } else {
          wx.showToast({
            title: res.data.msg || '修改失败',
            icon: 'none'
          });
        }
      },
      fail: err => {
        wx.hideLoading();
        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        });
      }
    });
  },
  
  /**
   * 构建提交的表单数据
   * @private
   */
  _buildFormData: function() {
    // 创建一个完整的数据副本
    let submitData = { ...this.data.villager };

    // 确保关键字段存在
    submitData.id = this.data.villagerId;
    submitData.shoujihaoma = this.data.villager.shoujihaoma || '';
    submitData.yktzh = this.data.villager.yktzh || '';
    submitData.ylbxkyx = this.data.villager.ylbxkyx || '';
    submitData.ylbxkzh = this.data.villager.ylbxkzh || '';
    submitData.bdcdjh = this.data.villager.bdcdjh || '';

    // 特殊处理户籍属性 - 复选框
    submitData.hujishuxing = this.data.selectedHujishuxing.join('、');

    // 构建表单数据字符串
    return this._buildFormDataString(submitData);
  },

  
  /**
   * 构建表单数据字符串
   * @private
   */
  _buildFormDataString: function(submitData) {
    let formDataString = "is_ajax=1";
    formDataString += "&id=" + this.data.villagerId;
    formDataString += "&catid=" + (submitData.catid || 2);
    formDataString += "&model=cmda";
    formDataString += "&module=cmda";
    formDataString += "&action=edit";
    
    // 添加所有数据字段，跳过特殊处理的字段
    const skipFields = ['id', 'catid', 'hujishuxing'];
    
    for (const key in submitData) {
      if (submitData.hasOwnProperty(key) && submitData[key] !== undefined) {
        if (skipFields.includes(key)) continue; // 跳过特殊处理的字段
        
        let value = submitData[key];
        
        // 特殊处理数组和对象
        if (typeof value === 'object' && value !== null) {
          value = Array.isArray(value) ? JSON.stringify(value) : JSON.stringify(value);
        }
        
        formDataString += "&data[" + key + "]=" + encodeURIComponent(value || '');
      }
    }
    
    // 特殊处理户籍属性
    this.data.selectedHujishuxing.forEach(value => {
      formDataString += "&data[hujishuxing][]=" + encodeURIComponent(value);
    });

    
    return formDataString;
  }
}) 