.container {
  padding: 30rpx;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.header {
  margin-bottom: 40rpx;
  text-align: center;
}

.title {
  font-size: 40rpx;
  font-weight: bold;
  color: #1e3a5f;
  margin-bottom: 15rpx;
}

.subtitle {
  font-size: 24rpx;
  color: #64748b;
}

.content {
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.section {
  margin-bottom: 40rpx;
}

.section:last-child {
  margin-bottom: 0;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #334155;
  margin-bottom: 20rpx;
}

.section-content {
  color: #475569;
  font-size: 28rpx;
  line-height: 1.6;
}

.paragraph {
  margin-bottom: 20rpx;
}

.list-item {
  margin-bottom: 12rpx;
  padding-left: 20rpx;
} 