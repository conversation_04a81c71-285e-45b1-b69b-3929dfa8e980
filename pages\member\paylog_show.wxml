<view class="portlet">
  <view class="portlet-title">
    <view class="caption">交易详情</view>
    <view class="actions" bindtap="goBack">
      <text class="back-icon">返回</text>
    </view>
  </view>
  
  <view class="portlet-body">
    <view class="form-group">
      <view class="form-label">流水号</view>
      <view class="form-value">{{detail.id}}</view>
    </view>

    <view class="form-group">
      <view class="form-label">交易描述</view>
      <view class="form-value">
        <text wx:if="{{detail.url}}" class="link-text" bindtap="openUrl" data-url="{{detail.url}}">{{detail.title}}</text>
        <text wx:else>{{detail.title}}</text>
      </view>
    </view>

    <view class="form-group">
      <view class="form-label">用途类型</view>
      <view class="form-value">{{detail.touid}}</view>
    </view>

    <view class="form-group">
      <view class="form-label">交易金额</view>
      <view class="form-value">
        <text class="{{detail.value >= 0 ? 'income' : 'expense'}}">¥{{detail.value}}元</text>
      </view>
    </view>

    <view class="form-group">
      <view class="form-label">付款方式</view>
      <view class="form-value">{{detail.pay_type}}</view>
    </view>

    <view class="form-group">
      <view class="form-label">支付状态</view>
      <view class="form-value status-box">
        <text class="{{detail.status == 1 ? 'status-paid' : 'status-unpaid'}}">
          {{detail.status == 1 ? '已付款' : '未付款'}}
        </text>
        <view wx:if="{{detail.status != 1}}" class="pay-now" bindtap="handlePay" data-id="{{detail.id}}">
          立即处理
        </view>
      </view>
    </view>

    <view class="form-group" wx:if="{{detail.paytime}}">
      <view class="form-label">付款时间</view>
      <view class="form-value">{{detail.paytime}}</view>
    </view>

    <view class="form-group" wx:if="{{detail.result}}">
      <view class="form-label">交易信息</view>
      <view class="form-value">{{detail.result}}</view>
    </view>

    <view class="form-group">
      <view class="form-label">创建时间</view>
      <view class="form-value">{{detail.inputtime}}</view>
    </view>
  </view>
</view> 