/* 全局容器 */
.container {
  min-height: 100vh;
  background-color: #f7f7f7;
  padding-bottom: 60rpx;
  position: relative;
}

/* 顶部背景 */
.top-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 240rpx;
  background: #1677ff;
  z-index: 0;
}

/* 用户信息卡片 */
.user-card {
  margin: 30rpx;
  border-radius: 20rpx;
  background-color: #fff;
  box-shadow: 0 6rpx 20rpx rgba(22, 119, 255, 0.08);
  padding: 30rpx;
  position: relative;
  z-index: 1;
}

.user-info-content {
  display: flex;
  align-items: flex-start;
  margin-bottom: 30rpx;
}

.user-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  margin-right: 20rpx;
  border: 4rpx solid rgba(255, 255, 255, 0.8);
  box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.1);
  flex-shrink: 0;
}

.user-details {
  flex: 1;
  min-width: 0;
}

.user-name {
  margin-bottom: 8rpx;
}

.username {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-right: 12rpx;
  display: inline-block;
}

.user-role {
  font-size: 24rpx;
  color: #1677ff;
  background-color: rgba(22, 119, 255, 0.1);
  padding: 4rpx 16rpx;
  border-radius: 100rpx;
  display: inline-block;
}

.action-arrow {
  padding: 0 10rpx;
}

.arrow-icon {
  width: 28rpx;
  height: 28rpx;
  opacity: 0.3;
}

.login-tip {
  font-size: 24rpx;
  color: #999;
  margin-top: 8rpx;
  display: block;
}

/* 账户统计 */
.account-stats {
  display: flex;
  flex-direction: column;
  background-color: rgba(22, 119, 255, 0.02);
  border-radius: 16rpx;
  padding: 20rpx 0;
}

.account-stats-row {
  display: flex;
  justify-content: space-between;
  padding: 10rpx 0;
}

.account-stats-divider {
  height: 1rpx;
  background-color: #eee;
  margin: 10rpx 30rpx;
}

.stat-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-value {
  font-size: 32rpx;
  color: #1677ff;
  font-weight: 600;
  max-width: 200rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  text-align: center;
}

.stat-label {
  font-size: 24rpx;
  color: #999;
  margin-top: 6rpx;
}

.stat-divider {
  width: 1rpx;
  height: 50rpx;
  background-color: #eee;
}

/* 标题 */
.section-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
  margin: 40rpx 30rpx 20rpx;
  position: relative;
  z-index: 1;
}

/* 快捷操作区 */
.quick-actions {
  display: flex;
  justify-content: space-between;
  margin: 0 30rpx 30rpx;
  position: relative;
  z-index: 1;
}

.quick-action-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.quick-icon-wrapper {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  background-color: rgba(22, 119, 255, 0.08);
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 12rpx;
}

.quick-icon {
  width: 50rpx;
  height: 50rpx;
}

.quick-text {
  font-size: 26rpx;
  color: #666;
}

/* 菜单列表 */
.menu-list {
  margin: 0 30rpx;
  position: relative;
  z-index: 1;
}

.menu-section {
  background: white;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(22, 119, 255, 0.05);
  margin-bottom: 20rpx;
}

.menu-title {
  font-size: 28rpx;
  color: #999;
  padding: 24rpx 30rpx 12rpx;
  border-bottom: 1rpx solid #f5f5f5;
}

.menu-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 28rpx 30rpx;
  border-bottom: 1rpx solid #f5f5f5;
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-item-left {
  display: flex;
  align-items: center;
}

.menu-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 20rpx;
}

.menu-text {
  font-size: 30rpx;
  color: #333;
}

.menu-item-hover {
  background-color: #f9f9f9;
}

/* 退出登录按钮 */
.logout-btn {
  margin: 40rpx 30rpx;
  padding: 24rpx 0;
  text-align: center;
  background-color: #fff;
  color: #1677ff;
  border-radius: 16rpx;
  font-size: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(22, 119, 255, 0.05);
}

/* 版本信息 */
.version-info {
  text-align: center;
  font-size: 24rpx;
  color: rgba(22, 119, 255, 0.6);
  padding: 30rpx 0;
}

/* 会员状态标识样式 */
.member-status {
  display: flex;
  flex-wrap: wrap;
  gap: 8rpx;
  margin-top: 12rpx;
}

.status-badge {
  padding: 4rpx 12rpx;
  border-radius: 6rpx;
  font-size: 20rpx;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f5f5;
  border: 1rpx solid #d9d9d9;
}

.status-badge text {
  color: #999;
}

/* 激活状态样式 */
.status-badge.verify.active {
  background-color: #f6ffed;
  border: 1rpx solid #b7eb8f;
}

.status-badge.verify.active text {
  color: #52c41a;
}

.status-badge.mobile.active {
  background-color: #e6f7ff;
  border: 1rpx solid #91d5ff;
}

.status-badge.mobile.active text {
  color: #1890ff;
}

.status-badge.email.active {
  background-color: #f9f0ff;
  border: 1rpx solid #d3adf7;
}

.status-badge.email.active text {
  color: #722ed1;
}

.status-badge.avatar.active {
  background-color: #fff7e6;
  border: 1rpx solid #ffd591;
}

.status-badge.avatar.active text {
  color: #fa8c16;
}

.status-badge.complete.active {
  background-color: #f0f5ff;
  border: 1rpx solid #adc6ff;
}

.status-badge.complete.active text {
  color: #2f54eb;
}

.status-badge.lock.active {
  background-color: #fff1f0;
  border: 1rpx solid #ffa39e;
}

.status-badge.lock.active text {
  color: #f5222d;
}

/* 未激活状态样式 */
.status-badge.inactive {
  background-color: #f5f5f5;
  border: 1rpx solid #d9d9d9;
}

.status-badge.inactive text {
  color: #999;
}