/* 导入wxParse的样式 */
@import "../../wxParse/wxParse.wxss";

.container {
  min-height: 100vh;
  background-color: #f5f7fa;
  padding-bottom: 20rpx; /* 为底部操作栏留出空间，从120rpx减少至20rpx */
  position: relative;
}

/* 顶部状态栏 */
.status-bar {
  background: #fff;
  padding: 20rpx 30rpx;
  border-bottom: 1rpx solid #e9ecef;
  position: sticky;
  top: 0;
  z-index: 100;
  display: flex;
  align-items: center;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.status-progress {
  position: absolute;
  bottom: 0;
  left: 0;
  height: 4rpx;
  background: linear-gradient(to right, #3498db, #2ecc71);
  width: 100%;
  animation: progressLoading 1.5s infinite ease-in-out;
}

@keyframes progressLoading {
  0% { width: 0%; opacity: 1; }
  50% { width: 70%; opacity: 0.7; }
  100% { width: 100%; opacity: 0; }
}

.status-indicator {
  font-size: 28rpx;
  padding: 8rpx 20rpx;
  border-radius: 30rpx;
  display: inline-flex;
  align-items: center;
}

.status-text {
  font-weight: 500;
}

.status-bar.success {
  background-color: #fff;
  border-bottom-color: #e9ecef;
}

.status-bar.loading {
  background-color: #fff;
  border-bottom-color: #e7f1f5;
}

.status-bar.error {
  background-color: #fff;
  border-bottom-color: #f9e6e6;
}

/* 加载中状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
  height: 70vh;
}

.loading-animation {
  position: relative;
  width: 100rpx;
  height: 100rpx;
  margin-bottom: 40rpx;
}

.loading-spinner {
  width: 80rpx;
  height: 80rpx;
  border: 6rpx solid #f1f3f5;
  border-top: 6rpx solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.loading-spinner.small {
  width: 36rpx;
  height: 36rpx;
  border-width: 4rpx;
  position: static;
  transform: none;
  margin-right: 10rpx;
}

@keyframes spin {
  0% { transform: translate(-50%, -50%) rotate(0deg); }
  100% { transform: translate(-50%, -50%) rotate(360deg); }
}

.loading-spinner.small {
  animation: spinSmall 1s linear infinite;
}

@keyframes spinSmall {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  color: #7f8c8d;
  font-size: 28rpx;
}

/* 档案滚动容器 */
.archive-scroll {
  height: calc(100vh - 120rpx - 100rpx); /* 减去顶部状态栏和底部操作栏高度 */
  overflow-y: auto;
}

/* 档案容器 */
.archive-container {
  padding: 30rpx;
}

/* 档案头部 */
.archive-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: linear-gradient(135deg, #3498db, #2980b9);
  border-radius: 24rpx;
  padding: 40rpx 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 8rpx 25rpx rgba(52, 152, 219, 0.2);
  color: #fff;
  position: relative;
  overflow: hidden;
  min-height: 200rpx;
}

.archive-blur-bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 0;
  background-color: rgba(52, 152, 219, 0.2);
  filter: blur(20px);
  opacity: 0.15;
  background-size: 100% 100%;
  background-repeat: no-repeat;
  transform: scale(1.1);
  transform-origin: center;
}

.archive-header::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 150rpx;
  height: 150rpx;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  transform: translate(50%, -50%);
  z-index: 1;
}

.archive-info {
  flex: 1;
  position: relative;
  z-index: 2;
}

.archive-title {
  margin-bottom: 15rpx;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}

/* 个人照片缩略图样式 */
.archive-thumb {
  width: 180rpx;
  height: 230rpx;
  margin-left: 30rpx;
  border-radius: 12rpx;
  overflow: hidden;
  position: relative;
  z-index: 2;
  transform: rotate(0deg);
  transition: all 0.3s ease;
}

.archive-thumb:active {
  transform: scale(1.05) rotate(0deg);
}

.thumb-shadow {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  box-shadow: inset 0 0 20rpx rgba(0,0,0,0.3);
  z-index: 1;
}

.thumb-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.archive-name {
  font-size: 44rpx;
  font-weight: bold;
  margin-right: 16rpx;
}

.archive-status {
  font-size: 20rpx;
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
  margin-top: 8rpx;
}

.status-active {
  background-color: rgba(39, 174, 96, 0.3);
  color: #fff;
  border: 1rpx solid rgba(39, 174, 96, 0.5);
}

.status-inactive {
  background-color: rgba(231, 76, 60, 0.3);
  color: #fff;
  border: 1rpx solid rgba(231, 76, 60, 0.5);
}

.archive-id {
  font-size: 24rpx;
  opacity: 0.9;
  margin-bottom: 20rpx;
  display: block;
}

.archive-meta {
  display: flex;
  font-size: 24rpx;
  opacity: 0.9;
}

.meta-item {
  display: flex;
  align-items: center;
  margin-right: 30rpx;
}

.meta-icon {
  width: 28rpx;
  height: 28rpx;
  margin-right: 6rpx;
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
  opacity: 0.9;
}

.meta-value {
  font-size: 24rpx;
}

.views-icon::before {
  content: '👁️';
}

.comments-icon::before {
  content: '💬';
}

/* 快速操作区 */
.quick-actions {
  display: flex;
  justify-content: space-between;
  padding: 20rpx 15rpx;
  margin-bottom: 30rpx;
  background: #fff;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.03);
}

.quick-action-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12rpx 8rpx;
  flex: 1;
  margin: 0 5rpx;
  transition: all 0.3s ease;
}

.quick-action-btn:active {
  transform: scale(0.95);
  opacity: 0.8;
}

.quick-action-icon {
  width: 44rpx;
  height: 44rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 6rpx;
  font-size: 22rpx;
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

/* 更新图片图标 */
.update-image-icon {
  background: linear-gradient(135deg, #ff6b6b, #ee5a52);
}

/* 更新信息图标 */
.update-info-icon {
  background: linear-gradient(135deg, #4ecdc4, #44a08d);
}

/* 新增档案图标 */
.add-archive-icon {
  background: linear-gradient(135deg, #45b7d1, #3498db);
}

/* 新增备注图标 */
.add-note-icon {
  background: linear-gradient(135deg, #f9ca24, #f0932b);
}

.quick-action-text {
  font-size: 20rpx;
  color: #7f8c8d;
  text-align: center;
  line-height: 1.2;
  white-space: nowrap;
}

/* 内容区块通用样式 */
.section {
  background: #fff;
  border-radius: 16rpx;
  margin-bottom: 30rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.03);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f1f3f5;
}

.section-title-container {
  display: flex;
  align-items: center;
}

.section-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 16rpx;
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #2c3e50;
}

.section-badge {
  font-size: 24rpx;
  background: #f1f3f5;
  color: #7f8c8d;
  padding: 4rpx 16rpx;
  border-radius: 20rpx;
}

.total-photos {
  background: #e7f5fe;
  color: #3498db;
}

.section-content {
  padding: 20rpx 30rpx 30rpx;
}

/* 照片区域样式 */
.media-section {
  margin-top: 30rpx;
  margin-bottom: 30rpx;
  position: relative;
  background: #fff;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.03);
}

/* 照片标签区域 */
.photo-tabs-container {
  padding: 0;
  position: relative;
  border-bottom: 1rpx solid #f0f3f5;
  background-color: #fff;
  overflow: hidden;
}

.photo-tabs {
  white-space: nowrap;
  display: flex;
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
  padding: 0 10rpx;
  box-sizing: border-box;
  width: 100%;
}

.photo-tabs::-webkit-scrollbar {
  display: none; /* Chrome, Safari and Opera */
}

.photo-tab {
  display: inline-flex;
  padding: 16rpx 12rpx;
  font-size: 24rpx;
  color: #666;
  position: relative;
  margin-right: 10rpx;
  transition: all 0.3s;
  text-align: center;
  align-items: center;
  flex-shrink: 0;
  width: auto;
  min-width: 80rpx;
  max-width: 150rpx;
  overflow: hidden;
  box-sizing: border-box;
}

.photo-tab text {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  max-width: 100%;
  display: inline-block;
}

.photo-tab.active {
  color: #3498db;
  font-weight: 500;
  position: relative;
}

.photo-tab.active::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 6rpx;
  background-color: #3498db;
  border-radius: 3rpx;
}

.photo-tab-count {
  display: inline-block;
  font-size: 20rpx;
  line-height: 30rpx;
  padding: 0 8rpx;
  min-width: 16rpx;
  background-color: #f0f3f5;
  color: #999;
  border-radius: 15rpx;
  margin-left: 8rpx;
  text-align: center;
  flex-shrink: 0;
}

.photo-tab.active .photo-tab-count {
  background-color: #e7f5fe;
  color: #3498db;
}

/* 照片内容区域 */
.photo-content {
  padding: 10rpx;
  position: relative;
}

/* 照片容器 */
.photo-container {
  display: none;
  opacity: 0;
  animation: fadeIn 0.3s ease forwards;
}

.photo-container.active {
  display: block;
  opacity: 1;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

/* 媒体滚动区 */
.media-scroll {
  width: 100%;
  overflow: hidden;
}

/* 照片网格 */
.media-grid {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -5rpx;
  padding: 5rpx;
  box-sizing: border-box;
}

.media-item {
  width: calc(33.333% - 10rpx);
  height: 160rpx;
  margin: 5rpx;
  border-radius: 8rpx;
  overflow: hidden;
  position: relative;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);
}

.media-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.media-overlay {
  position: absolute;
  bottom: 6rpx;
  right: 6rpx;
  background: rgba(0, 0, 0, 0.5);
  color: #fff;
  border-radius: 50%;
  width: 36rpx;
  height: 36rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.media-number {
  font-size: 20rpx;
  line-height: 1;
}

/* 空状态 */
.media-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx 0;
  color: #95a5a6;
}

.media-empty-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
  opacity: 0.3;
}

.media-empty-text {
  font-size: 28rpx;
}

/* 基本信息区域 */
.info-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 30rpx 30rpx;
}

.info-cell {
  display: flex;
  flex-direction: column;
}

.info-label {
  font-size: 24rpx;
  color: #7f8c8d;
  margin-bottom: 10rpx;
}

.info-value {
  font-size: 28rpx;
  color: #2c3e50;
  font-weight: 500;
}

.id-number {
  letter-spacing: 1rpx;
  font-family: monospace;
}

/* 时间线样式 */
.status-timeline {
  padding: 10rpx 0;
}

.timeline-item {
  display: flex;
  align-items: flex-start;
  position: relative;
  padding-bottom: 30rpx;
}

.timeline-item:last-child {
  padding-bottom: 0;
}

.timeline-item:not(:last-child)::after {
  content: '';
  position: absolute;
  top: 24rpx;
  left: 8rpx;
  width: 2rpx;
  height: calc(100% - 24rpx);
  background-color: #e9ecef;
}

.timeline-point {
  width: 18rpx;
  height: 18rpx;
  border-radius: 50%;
  background-color: #3498db;
  margin-right: 20rpx;
  margin-top: 6rpx;
  position: relative;
  z-index: 2;
}

.timeline-content {
  flex: 1;
}

.timeline-title {
  font-size: 24rpx;
  color: #7f8c8d;
  margin-bottom: 6rpx;
}

.timeline-value {
  font-size: 28rpx;
  color: #2c3e50;
  font-weight: 500;
}

/* 附加信息表格 */
.info-table {
  width: 100%;
}

.info-row {
  display: flex;
  padding: 24rpx 0;
  border-bottom: 1rpx solid #f1f3f5;
}

.info-row:last-child {
  border-bottom: none;
}

.info-row-label {
  flex: 0 0 30%;
  font-size: 26rpx;
  color: #7f8c8d;
}

.info-row-value {
  flex: 1;
  font-size: 26rpx;
  color: #2c3e50;
}

/* 反馈区域样式 */
.feedback-list {
  width: 100%;
}

.feedback-item {
  display: flex;
  align-items: center;
  padding: 24rpx 0;
  border-bottom: 1rpx solid #f1f3f5;
}

.feedback-item:last-child {
  border-bottom: none;
}

.feedback-indicator {
  width: 12rpx;
  height: 12rpx;
  border-radius: 50%;
  background-color: #3498db;
  margin-right: 16rpx;
}

.feedback-content {
  flex: 1;
}

.feedback-title {
  font-size: 28rpx;
  color: #2c3e50;
}

.feedback-arrow {
  width: 30rpx;
  height: 30rpx;
  position: relative;
}

.feedback-arrow::after {
  content: '';
  position: absolute;
  top: 50%;
  right: 5rpx;
  width: 12rpx;
  height: 12rpx;
  border-top: 2rpx solid #bbb;
  border-right: 2rpx solid #bbb;
  transform: translateY(-50%) rotate(45deg);
}

/* 评论区样式 */
.comments-section {
  margin-bottom: 20rpx;
}

.comments-list {
  padding: 0;
}

.comments-empty {
  padding: 30rpx 0;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #f9f9f9;
  border-radius: 8rpx;
  margin: 10rpx 0;
}

.empty-icon {
  width: 60rpx;
  height: 60rpx;
  margin-bottom: 10rpx;
  opacity: 0.6;
}

.empty-text {
  color: #95a5a6;
  font-size: 26rpx;
}

.comment-item {
  padding: 24rpx 20rpx;
  border-bottom: 1rpx solid #f1f3f5;
}

.comment-item:last-child {
  border-bottom: none;
}

.comment-item-header {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.comment-item-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin-right: 20rpx;
  background-color: #f1f3f5;
}

.comment-item-meta {
  flex: 1;
}

.comment-item-author {
  font-size: 28rpx;
  font-weight: 500;
  color: #2c3e50;
  margin-bottom: 6rpx;
  display: flex;
  align-items: center;
}

.comment-item-tag {
  font-size: 20rpx;
  background: #3498db;
  color: #fff;
  padding: 2rpx 10rpx;
  border-radius: 6rpx;
  margin-left: 10rpx;
}

.comment-item-time {
  font-size: 24rpx;
  color: #95a5a6;
}

.comment-item-content {
  font-size: 28rpx;
  line-height: 1.6;
  color: #333;
}

.comment-images {
  display: flex;
  flex-wrap: wrap;
  margin-top: 20rpx;
}

.comment-image-item {
  width: 30%;
  height: 180rpx;
  margin: 0 1.66% 20rpx 0;
  border-radius: 8rpx;
  overflow: hidden;
}

.comment-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.loading-status {
  padding: 20rpx 0;
  text-align: center;
}

.loading-more {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #95a5a6;
  font-size: 24rpx;
}

.loading-end {
  color: #95a5a6;
  font-size: 24rpx;
}

/* 底部操作栏 */
.action-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12rpx 20rpx;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.08);
  z-index: 99;
}

.action-button {
  height: 80rpx;
  width: 80rpx;
  padding: 0;
  margin: 0;
  border-radius: 50%;
  font-size: 26rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: #f5f7fa;
  border: none;
  position: relative;
}

.action-button.share-button {
  background: linear-gradient(135deg, #27ae60, #2ecc71);
  color: #fff;
}

.action-button::after {
  border: none;
}

.button-icon {
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
}

.button-text {
  font-size: 20rpx;
  margin-top: 4rpx;
}

.back-icon::before {
  content: '←';
  font-weight: bold;
}

.share-icon::before {
  content: '↗';
}

/* 评论输入区域 */
.comment-input-area {
  flex: 1;
  padding: 0 20rpx;
}

.comment-input-box {
  background: #f5f7fa;
  height: 80rpx;
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  padding: 0 30rpx;
  color: #95a5a6;
  font-size: 28rpx;
  border: 1rpx solid #e9ecef;
}

.input-placeholder {
  color: #95a5a6;
}

/* 评论弹出层 */
.comment-modal {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  z-index: 1000;
  transform: translateY(100%);
  transition: transform 0.3s ease;
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);
  border-radius: 30rpx 30rpx 0 0;
}

.comment-modal-show {
  transform: translateY(0);
}

.comment-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f1f3f5;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 500;
}

.comment-modal-close {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-icon {
  font-size: 40rpx;
  color: #95a5a6;
}

.comment-modal-body {
  padding: 30rpx;
}

.comment-modal-textarea {
  width: 100%;
  min-height: 200rpx;
  font-size: 30rpx;
  line-height: 1.5;
  padding: 20rpx 0;
}

.image-preview {
  display: flex;
  flex-wrap: wrap;
  margin-top: 20rpx;
}

.image-preview-item {
  width: 180rpx;
  height: 180rpx;
  margin: 0 20rpx 20rpx 0;
  border-radius: 8rpx;
  overflow: hidden;
  position: relative;
}

.image-preview-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.image-preview-delete {
  position: absolute;
  top: 0;
  right: 0;
  width: 40rpx;
  height: 40rpx;
  background: rgba(0, 0, 0, 0.5);
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 30rpx;
}

.comment-modal-footer {
  padding: 20rpx 30rpx;
  border-top: 1rpx solid #f1f3f5;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.upload-btn {
  background: none;
  padding: 0;
  display: flex;
  align-items: center;
  font-size: 28rpx;
  color: #3498db;
}

.upload-btn::after {
  border: none;
}

.upload-icon {
  width: 32rpx;
  height: 32rpx;
  border-radius: 50%;
  background: #e7f5fe;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 10rpx;
  position: relative;
}

.upload-icon::before {
  content: '+';
  color: #3498db;
  font-weight: bold;
}

.image-count {
  margin-left: 6rpx;
  color: #3498db;
}

.comment-modal-actions {
  display: flex;
  align-items: center;
}

.comment-modal-btn {
  margin: 0 10rpx;
  height: 70rpx;
  border-radius: 8rpx;
  font-size: 28rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 30rpx;
}

.comment-modal-btn::after {
  border: none;
}

.comment-modal-btn-cancel {
  background: #f1f3f5;
  color: #7f8c8d;
}

.comment-modal-btn-submit {
  background: #95a5a6;
  color: #fff;
}

.comment-modal-btn-submit.is-active {
  background: linear-gradient(to right, #3498db, #2980b9);
}

/* 底部间距调整 */
.bottom-spacer {
  height: 20rpx;
}

/* wxParse 样式补充 */
.wxParse-p {
  margin-bottom: 20rpx;
  line-height: 1.6;
}

.wxParse-img {
  max-width: 100%;
  margin: 10rpx 0;
}

/* 针对评论中的图片处理 */
.wxParse-p-with-img {
  margin-top: 20rpx;
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
}

.wxParse-img {
  max-width: 32%;
  margin: 5rpx 0.66%;
  height: 200rpx;
  object-fit: cover;
  border-radius: 8rpx;
}

.wxParse-img-single {
  max-width: 100%;
  max-height: 400rpx;
  margin: 10rpx 0;
  height: auto;
  object-fit: contain;
}

.wxParse-img-double {
  width: 48%;
  max-height: 250rpx;
  margin: 10rpx 1%;
  object-fit: cover;
}

/* 图片计数指示器 */
.image-count-indicator {
  width: 100%;
  text-align: right;
  font-size: 24rpx;
  color: #7f8c8d;
  padding: 10rpx 0;
}

/* 添加反馈信息区域的样式 */
.feedback-section .section-icon.feedback-icon {
  background-color: #5c6bc0;
  position: relative;
}

.feedback-section .section-icon.feedback-icon:before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 12px;
  height: 12px;
  background-color: white;
  border-radius: 50%;
  transform: translate(-50%, -50%);
}

.feedback-list {
  width: 100%;
}

.feedback-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 15px;
  border-bottom: 1px solid #eee;
  background-color: white;
}

.feedback-item:last-child {
  border-bottom: none;
}

.feedback-title {
  flex: 1;
  font-size: 14px;
  color: #333;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.feedback-arrow {
  color: #bbb;
  font-size: 18px;
  margin-left: 10px;
}

/* Tab指示器和容器 */
.tab-indicator-container {
  position: relative;
  overflow: hidden;
}

/* 照片容器的动画效果 */
.photo-container {
  display: none;
  animation: fadeIn 0.3s ease;
}

.photo-container.active {
  display: block;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

/* 默认所有照片容器都是隐藏的 */
.photo-container:not(.active) {
  display: none;
}

/* 为相关照片区域添加下边距 */
.media-section {
  margin-bottom: 30rpx;
} 