.container {
  padding: 20rpx;
  background: #f5f5f5;
  min-height: 100vh;
}

/* 会员信息卡片 */
.member-card {
  background: linear-gradient(135deg, #3498db, #5dade2);
  padding: 30rpx;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(52, 152, 219, 0.2);
  color: #fff;
}

.member-info {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.member-avatar {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  overflow: hidden;
  background: #fff;
  margin-right: 20rpx;
  border: 4rpx solid rgba(255, 255, 255, 0.5);
}

.avatar-img {
  width: 100%;
  height: 100%;
}

.member-details {
  flex: 1;
}

.member-name {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.member-id {
  font-size: 24rpx;
  opacity: 0.8;
}

.member-score-box {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: rgba(255, 255, 255, 0.2);
  padding: 16rpx 24rpx;
  border-radius: 12rpx;
}

.score-label {
  font-size: 28rpx;
}

.score-value {
  font-size: 40rpx;
  font-weight: bold;
}

/* 未登录提示 */
.login-tip {
  background: #fff;
  padding: 40rpx 30rpx;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);
}

.tip-text {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 30rpx;
}

.login-btn {
  background: #3498db;
  color: #fff;
  font-size: 28rpx;
  padding: 16rpx 60rpx;
  border-radius: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(52, 152, 219, 0.3);
}

/* 标签页导航 */
.tab-nav {
  display: flex;
  justify-content: space-around;
  background: #fff;
  padding: 10rpx 20rpx;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 20rpx 0;
  position: relative;
}

.tab-item.active {
  font-weight: bold;
  color: #3498db;
}

.tab-item.active:after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 30%;
  width: 40%;
  height: 6rpx;
  background: #3498db;
  border-radius: 3rpx;
}

.tab-text {
  font-size: 28rpx;
}

.tab-content {
  min-height: 300rpx;
}

/* 商品列表 */
.exchange-list {
  margin-bottom: 30rpx;
}

.exchange-item {
  background: #fff;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);
  position: relative;
}

.item-header {
  display: flex;
  margin-bottom: 24rpx;
}

.item-thumb {
  width: 180rpx;
  height: 180rpx;
  border-radius: 12rpx;
  margin-right: 20rpx;
  background: #f8f8f8;
}

.item-info {
  flex: 1;
}

.item-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 12rpx;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

.item-description {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
  overflow: hidden;
}

/* 价格和规格信息 */
.price-section {
  background: #f8f8f8;
  border-radius: 12rpx;
  padding: 20rpx;
  margin-bottom: 24rpx;
}

.price-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.base-price, .stock-info {
  display: flex;
  flex-direction: column;
}

.price-label, .stock-label {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 6rpx;
}

.price-value {
  font-size: 34rpx;
  color: #3498db;
  font-weight: bold;
}

.stock-value {
  font-size: 28rpx;
  color: #666;
}

.stock-value.no-stock {
  color: #e74c3c;
}

/* 规格选择 */
.sku-section {
  margin-top: 20rpx;
}

.sku-title {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 16rpx;
}

.sku-list {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.sku-item {
  background: #fff;
  border: 2rpx solid #ddd;
  border-radius: 8rpx;
  padding: 12rpx 24rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  transition: all 0.3s;
}

.sku-item.selected {
  border-color: #3498db;
  background: #ebf5fb;
  transform: translateY(-2rpx);
  box-shadow: 0 2rpx 8rpx rgba(52, 152, 219, 0.15);
}

.sku-name {
  font-size: 26rpx;
  color: #333;
  margin-bottom: 6rpx;
}

.sku-price {
  font-size: 24rpx;
  color: #3498db;
}

/* 兑换按钮 */
.exchange-btn {
  background: linear-gradient(to right, #2980b9, #3498db);
  color: #fff;
  border-radius: 12rpx;
  padding: 24rpx;
  text-align: center;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 4rpx 12rpx rgba(52, 152, 219, 0.2);
  transition: all 0.3s;
}

.exchange-btn:active {
  opacity: 0.8;
  transform: translateY(2rpx);
}

.exchange-btn.disabled {
  background: linear-gradient(to right, #ccc, #ddd);
  box-shadow: none;
}

.btn-text {
  font-size: 32rpx;
  font-weight: bold;
}

.btn-price {
  font-size: 28rpx;
  background: rgba(255, 255, 255, 0.2);
  padding: 4rpx 16rpx;
  border-radius: 20rpx;
}

/* 历史记录 */
.history-list {
  margin-bottom: 30rpx;
}

.history-item {
  background: #fff;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 20rpx;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);
}

.history-main {
  flex: 1;
}

.history-title {
  font-size: 30rpx;
  color: #333;
  font-weight: bold;
  margin-bottom: 16rpx;
}

.history-info {
  display: flex;
  font-size: 24rpx;
  color: #666;
  margin-bottom: 10rpx;
}

.history-label {
  color: #999;
  margin-right: 12rpx;
  white-space: nowrap;
}

.history-value {
  color: #666;
  flex: 1;
}

.history-price {
  font-size: 34rpx;
  color: #3498db;
  font-weight: bold;
  margin-left: 20rpx;
}

.empty-tip {
  text-align: center;
  color: #999;
  font-size: 28rpx;
  padding: 60rpx 0;
  background: #fff;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
}

/* 加载动画 */
.loading-container {
  position: fixed;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.7);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 999;
}

.loading-spinner {
  width: 80rpx;
  height: 80rpx;
  border: 8rpx solid #f3f3f3;
  border-top: 8rpx solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 已售罄浮标样式 */
.sold-out-badge {
  position: absolute;
  top: 0;
  right: 0;
  background-color: #e74c3c;
  color: white;
  font-size: 28rpx;
  padding: 15rpx 10rpx;
  border-radius: 0 16rpx 0 16rpx;
  font-weight: bold;
  z-index: 10;
  writing-mode: vertical-rl;
  text-orientation: upright;
  letter-spacing: 2rpx;
  height: auto;
  box-shadow: 0 2rpx 10rpx rgba(231, 76, 60, 0.5);
}

/* 库存充足浮标样式 */
.stock-abundant-badge {
  position: absolute;
  top: 0;
  right: 0;
  background-color: #2ecc71;
  color: white;
  font-size: 28rpx;
  padding: 15rpx 10rpx;
  border-radius: 0 16rpx 0 16rpx;
  font-weight: bold;
  z-index: 10;
  writing-mode: vertical-rl;
  text-orientation: upright;
  letter-spacing: 2rpx;
  height: auto;
  box-shadow: 0 2rpx 10rpx rgba(46, 204, 113, 0.5);
}

/* 库存紧张浮标样式 */
.stock-low-badge {
  position: absolute;
  top: 0;
  right: 0;
  background-color: #ff9ff3;
  color: white;
  font-size: 28rpx;
  padding: 15rpx 10rpx;
  border-radius: 0 16rpx 0 16rpx;
  font-weight: bold;
  z-index: 10;
  writing-mode: vertical-rl;
  text-orientation: upright;
  letter-spacing: 2rpx;
  height: auto;
  box-shadow: 0 2rpx 10rpx rgba(255, 159, 243, 0.5);
}

/* 我的兑换列表 */
.my-exchange-list {
  margin-bottom: 30rpx;
}

.my-exchange-item {
  background: #fff;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 20rpx;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);
}

.my-exchange-main {
  flex: 1;
}

.my-exchange-title {
  font-size: 30rpx;
  color: #333;
  font-weight: bold;
  margin-bottom: 16rpx;
}

.my-exchange-info {
  display: flex;
  font-size: 24rpx;
  color: #666;
  margin-bottom: 10rpx;
}

.my-exchange-label {
  color: #999;
  margin-right: 12rpx;
  white-space: nowrap;
}

.my-exchange-value {
  color: #666;
  flex: 1;
}

.my-exchange-price {
  font-size: 34rpx;
  color: #3498db;
  font-weight: bold;
  margin-left: 20rpx;
}

/* 兑换状态样式 */
.status-0 {
  color: #f39c12; /* 处理中 */
}

.status-1 {
  color: #2ecc71; /* 已完成 */
}

.status-2 {
  color: #e74c3c; /* 已取消 */
}

.status-3 {
  color: #3498db; /* 配送中 */
} 