var app = getApp();

Page({
    data: {
        listData: [],
        loading: false,
        page: 1,
        limit: 10,
        hasMore: false,
        hidden: true,
        currentTab: 0,
        refreshing: false
    },

    onLoad: function(options) {
        if (options && options.tid) {
            this.setData({ currentTab: parseInt(options.tid) || 0 });
        }
        this.loadNoticeList();
    },

    onPullDownRefresh: function() {
        this.setData({
            refreshing: true,
            page: 1,
            listData: []
        });
        this.loadNoticeList(true);
    },

    // 切换通知类别标签
    switchTab: function(e) {
        const tid = parseInt(e.currentTarget.dataset.tid);
        
        // 如果点击当前标签，不执行操作
        if (tid === this.data.currentTab) return;
        
        this.setData({
            currentTab: tid,
            page: 1,
            listData: [],
            hasMore: false
        });
        
        this.loadNoticeList();
    },

    // 加载更多
    loadMore: function() {
        if (!this.data.loading && this.data.hasMore) {
            this.loadNoticeList();
        }
    },

    // 加载通知列表
    loadNoticeList: function(isPullDown = false) {
        if (this.data.loading) return;
        
        const self = this;
        const member_uid = wx.getStorageSync('member_uid');
        const member_auth = wx.getStorageSync('member_auth');
        
        if (!member_uid || !member_auth) {
            wx.showToast({
                title: '请先登录',
                icon: 'none',
                duration: 2000
            });
            
            if (isPullDown) {
                wx.stopPullDownRefresh();
                this.setData({ refreshing: false });
            }
            
            return;
        }
        
        this.setData({ 
            loading: true,
            hidden: false 
        });
        
        // 构建API URL
        const requestUrl = `${app.globalData.http_api}&s=member&app=notice&c=home&m=index&tid=${this.data.currentTab}&api_auth_uid=${member_uid}&api_auth_code=${member_auth}&api_call_function=member_content_comment`;
        
        console.log('加载通知列表:', requestUrl);
        
        wx.request({
            url: requestUrl,
            method: 'GET',
            data: {
                page: this.data.page,
                pagesize: this.data.limit
            },
            success: function(res) {
                if (res.data.code == 1) {
                    const newData = res.data.data || [];
                    const hasMore = newData.length === self.data.limit;
                    
                    // 格式化时间
                    newData.forEach(item => {
                        if (item.inputtime) {
                            item.inputtime = self.formatTime(item.inputtime);
                        }
                    });
                    
                    self.setData({
                        listData: self.data.page === 1 ? newData : [...self.data.listData, ...newData],
                        page: self.data.page + 1,
                        hasMore: hasMore,
                        hidden: true
                    });
                } else {
                    console.log('获取通知列表失败:', res.data.msg);
                    wx.showToast({
                        title: res.data.msg || '加载失败',
                        icon: 'none',
                        duration: 2000
                    });
                }
            },
            fail: function(err) {
                console.log('请求错误:', err);
                wx.showToast({
                    title: '网络错误，请重试',
                    icon: 'none',
                    duration: 2000
                });
            },
            complete: function() {
                self.setData({ 
                    loading: false,
                    hidden: true 
                });
                
                if (isPullDown) {
                    wx.stopPullDownRefresh();
                    self.setData({ refreshing: false });
                }
            }
        });
    },

    // 标记消息为已读
    markAsRead: function(e) {
        const id = e.currentTarget.dataset.id;
        const self = this;
        
        // 检查消息是否已读
        const listData = this.data.listData;
        const index = listData.findIndex(item => item.id == id);
        
        if (index > -1 && listData[index].is_read == 1) {
            // 已读消息，直接查看详情
            this.viewNoticeDetail(id);
            return;
        }
        
        const member_uid = wx.getStorageSync('member_uid');
        const member_auth = wx.getStorageSync('member_auth');
        
        if (!member_uid || !member_auth) {
            wx.showToast({
                title: '请先登录',
                icon: 'none',
                duration: 2000
            });
            return;
        }
        
        const requestUrl = `${app.globalData.http_api}&s=member&app=notice&c=home&m=read&api_auth_uid=${member_uid}&api_auth_code=${member_auth}`;
        
        wx.request({
            url: requestUrl,
            method: 'POST',
            data: {
                id: id
            },
            success: (res) => {
                if (res.data.code == 1) {
                    // 更新本地消息状态
                    if (index > -1) {
                        listData[index].is_read = 1;
                        self.setData({ listData });
                    }
                    
                    // 查看消息详情
                    self.viewNoticeDetail(id);
                } else {
                    console.log('标记已读失败:', res.data.msg);
                }
            },
            fail: function(err) {
                console.log('请求错误:', err);
            }
        });
    },
    
    // 查看消息详情
    viewNoticeDetail: function(id) {
        // 查找消息数据
        const notice = this.data.listData.find(item => item.id == id);
        
        if (notice) {
            wx.navigateTo({
                url: `../member/notice-detail?id=${id}&title=${encodeURIComponent(notice.title)}`
            });
        }
    },
    
    // 格式化时间戳
    formatTime: function(timestamp) {
        if (!timestamp) return '';
        
        const ts = parseInt(timestamp);
        if (isNaN(ts)) return timestamp;
        
        const date = new Date(ts * 1000);
        const now = new Date();
        const diff = Math.floor((now - date) / 1000);
        
        // 今天内
        if (diff < 86400 && date.getDate() === now.getDate()) {
            const hours = date.getHours().toString().padStart(2, '0');
            const minutes = date.getMinutes().toString().padStart(2, '0');
            return `今天 ${hours}:${minutes}`;
        }
        
        // 昨天
        const yesterday = new Date();
        yesterday.setDate(yesterday.getDate() - 1);
        if (date.getDate() === yesterday.getDate() && 
            date.getMonth() === yesterday.getMonth() && 
            date.getFullYear() === yesterday.getFullYear()) {
            const hours = date.getHours().toString().padStart(2, '0');
            const minutes = date.getMinutes().toString().padStart(2, '0');
            return `昨天 ${hours}:${minutes}`;
        }
        
        // 一周内
        if (diff < 604800) {
            const days = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
            const day = days[date.getDay()];
            const hours = date.getHours().toString().padStart(2, '0');
            const minutes = date.getMinutes().toString().padStart(2, '0');
            return `${day} ${hours}:${minutes}`;
        }
        
        // 更早
        const year = date.getFullYear();
        const month = (date.getMonth() + 1).toString().padStart(2, '0');
        const day = date.getDate().toString().padStart(2, '0');
        
        return `${year}-${month}-${day}`;
    }
}); 