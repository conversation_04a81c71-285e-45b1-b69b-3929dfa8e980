<view class="post-container">
  <!-- 顶部背景 -->
  <view class="top-bg">
    <view class="page-title">
      <text class="title-text">发布信息：</text>
      <text class="subtitle-text">选择保密，只有主管理员可见，其它任何人不可见！</text>
    </view>
  </view>

  <view class="post-card">
    <form bindsubmit="formSubmit">
      <!-- 提示信息 -->
      <view class="notice-card">
        <view class="notice-content">
          <image src="../../icons/jg.png" class="notice-icon"></image>
          <text class="notice-text">请认真对待，勿发布虚假信息！</text>
        </view>
      </view>

      <!-- 发布类型 -->
      <view class="form-section">
        <view class="section-title">发布类型</view>
        <view class="radio-group-container">
          <radio-group name="fabuleixing" class="radio-group" bindchange="onPublishTypeChange">
            <label class="radio-button {{publishType === '1' ? 'active' : ''}}">
              <view class="radio-button-inner">
                <radio value="1" checked="{{true}}" color="#07c160" />
                <text>反应问题</text>
              </view>
            </label>
            <label class="radio-button {{publishType === '2' ? 'active' : ''}}">
              <view class="radio-button-inner">
                <radio value="2" color="#07c160" />
                <text>发布信息</text>
              </view>
            </label>
          </radio-group>
        </view>
      </view>
      
      <!-- 问题类型 -->
      <view class="form-section" wx:if="{{publishType === '1'}}">
        <view class="section-title">问题类型</view>
        <view class="radio-group-container">
          <radio-group name="leixing" class="radio-group">
            <label class="radio-button">
              <view class="radio-button-inner">
                <radio value="1" color="#07c160" />
                <text>文明新风</text>
              </view>
            </label>
            <label class="radio-button">
              <view class="radio-button-inner">
                <radio value="2" color="#07c160" />
                <text>卫生监督</text>
              </view>
            </label>
            <label class="radio-button">
              <view class="radio-button-inner">
                <radio value="3" color="#07c160" />
                <text>野外焚烧</text>
              </view>
            </label>
            <label class="radio-button">
              <view class="radio-button-inner">
                <radio value="4" color="#07c160" />
                <text>建言建议</text>
              </view>
            </label>
          </radio-group>
        </view>
      </view>

      <!-- 公开设置 -->
      <view class="form-section" wx:if="{{publishType === '1'}}">
        <view class="section-title">
          <block wx:if="{{isPrivate}}">
            公开设置：（您选择了保密，只有管理员可见！）
          </block>
          <block wx:else>
            公开设置
          </block>
        </view>
        <view class="radio-group-container">
          <radio-group name="gongkai" class="radio-group" bindchange="onPrivacyChange">
            <label class="radio-button">
              <view class="radio-button-inner">
                <radio value="1" color="#07c160" />
                <text>公开</text>
              </view>
            </label>
            <label class="radio-button">
              <view class="radio-button-inner">
                <radio value="2" color="#07c160" />
                <text>保密</text>
              </view>
            </label>
          </radio-group>
        </view>
      </view>

      <!-- 信息类型 -->
      <view class="form-section" wx:if="{{publishType === '2'}}">
        <view class="section-title">信息类型</view>
        <view class="radio-group-container">
          <radio-group name="xinxileixing" class="radio-group">
            <label class="radio-button">
              <view class="radio-button-inner">
                <radio value="1" color="#07c160" />
                <text>出售</text>
              </view>
            </label>
            <label class="radio-button">
              <view class="radio-button-inner">
                <radio value="2" color="#07c160" />
                <text>求购</text>
              </view>
            </label>
            <label class="radio-button">
              <view class="radio-button-inner">
                <radio value="3" color="#07c160" />
                <text>其它</text>
              </view>
            </label>
          </radio-group>
        </view>
      </view>
      
      <!-- 主题 -->
      <view class="form-section">
        <view class="section-title">主题</view>
        <view class="input-container">
          <input class="form-input" placeholder="请简要描述主题" type="text" name="title"/>
        </view>
      </view>

      <!-- 内容 -->
      <view class="form-section">
        <view class="section-title">内容</view>
        <view class="textarea-container">
          <textarea class="form-textarea" placeholder="请详细描述您要发布的内容..." name="content" />
        </view>
      </view>

           <!-- 图片上传 -->
      <view class="form-section">
        <view class="section-title">上传图片</view>
        <view class="upload-section">
          <view class="upload-btn" bindtap="uploadFile" disabled="{{isUploading}}">
            <block wx:if="{{isUploading}}">
              <view class="loading-container">
                <view class="loading-spinner"></view>
                <text class="loading-text">上传中...</text>
              </view>
            </block>
            <block wx:else>
              <view class="upload-content">
                <image src="../../icons/upphoto.png" class="upload-icon"></image>
                <text class="upload-text">点击上传图片</text>
                <text class="upload-desc">支持jpg、png格式，单张不超过2MB</text>
              </view>
            </block>
          </view>

          <!-- 图片预览 -->
          <view class="image-preview-grid" wx:if="{{tupianxinxi.length > 0}}">
            <view class="preview-item" wx:for="{{tupianxinxi}}" wx:key="index">
              <image src="{{item}}" mode="aspectFill" class="preview-image"/>
              <view class="delete-btn" bindtap="deleteImage" data-index="{{index}}">
                <text class="delete-icon">×</text>
              </view>
            </view>
          </view>
        </view>
      </view>

     <!-- 区域 -->
      <view class="form-section">
        <view class="section-title">区域</view>
        <view class="radio-group-container">
          <radio-group name="quyu" class="radio-group" bindchange="onAreaChange">
            <label class="radio-button {{areaType === '1' ? 'active' : ''}}">
              <view class="radio-button-inner">
                <radio value="1" checked="{{true}}" color="#07c160" />
                <text>本村组</text>
              </view>
            </label>
            <label class="radio-button {{areaType === '2' ? 'active' : ''}}">
              <view class="radio-button-inner">
                <radio value="2" color="#07c160" />
                <text>其它村组</text>
              </view>
            </label>
          </radio-group>
        </view>
      </view>

      <!-- 本村组信息显示区域 -->
      <view class="form-section" wx:if="{{areaType === '1' || !areaType}}">
        <!-- 标题和村组名称在同一行 -->
        <view style="display: flex; align-items: center;">
          <view class="section-title" style="margin-bottom: 0; margin-right: 10px;">您的所属村组：</view>
          
          <!-- 显示会员所属村组 -->
          <view style="flex: 1; background-color: #f8f8f8; padding: 6px 12px; border-radius: 4px; display: flex; align-items: center;">
            <image src="../../icons/jg.png" style="width: 16px; height: 16px; margin-right: 6px;"></image>
            <text style="color: #333; font-size: 14px;">{{memberSscz || '加载中...'}}</text>
            <!-- 本村组模式下不需要隐藏input，会在表单提交时使用selectedVillageId -->
          </view>
        </view>
      </view>

      <!-- 所属村组选择 - 仅当选择"其它村组"时显示 -->
      <view class="form-section" wx:if="{{areaType === '2'}}">
        <!-- 标题和已选择的村组在同一行 -->
        <view style="display: flex; align-items: center; margin-bottom: 10px;">
          <view class="section-title" style="margin-bottom: 0; margin-right: 10px;">所属村组：</view>
          
          <!-- 显示已选择的村组 -->
          <view class="selected-village" wx:if="{{selectedVillageName}}" style="flex: 1; background-color: #f8f8f8; padding: 6px 12px; border-radius: 4px; display: flex; align-items: center;">
            <image src="../../icons/collect-active.png" style="width: 16px; height: 16px; margin-right: 6px;" wx:if="{{selectedVillageId}}"></image>
            <text class="selected-text" style="color: #333; font-size: 14px;">已选 {{selectedVillageName}}</text>
          </view>
        </view>
        
        <!-- 村组选择区域（一行显示） -->
        <view style="display: flex; flex-direction: row; gap: 12px; margin-top: 4px;">
          <!-- 一级村组选择 -->
          <view class="select-container" style="flex: 1; position: relative;">
            <view style="position: absolute; top: -18px; left: 2px; font-size: 12px; color: #666;">选择村</view>
            <picker mode="selector" range="{{villageGroupColumns[0]}}" range-key="region_name" value="{{villageGroupIndices[0]}}" bindchange="onVillageLevel1Change">
              <view class="picker-view {{!villageGroupColumns[0][villageGroupIndices[0]] ? 'placeholder' : ''}}" style="padding: 10px 12px; background-color: #f8f8f8; border-radius: 4px; display: flex; justify-content: space-between; align-items: center;">
                <text style="color: {{villageGroupColumns[0][villageGroupIndices[0]] ? '#333' : '#999'}};">{{villageGroupColumns[0][villageGroupIndices[0]].region_name || '请选择村'}}</text>
                <image src="../../icons/arrow-down.png" class="picker-arrow" style="width: 16px; height: 16px;" wx:if="{{!arrowIconMissing}}"></image>
              </view>
            </picker>
          </view>
          
          <!-- 二级村组选择 -->
          <view class="select-container" style="flex: 1; position: relative;" wx:if="{{villageGroupColumns[1].length > 0}}">
            <view style="position: absolute; top: -18px; left: 2px; font-size: 12px; color: #666;">选择组</view>
            <picker mode="selector" range="{{villageGroupColumns[1]}}" range-key="region_name" value="{{villageGroupIndices[1]}}" bindchange="onVillageLevel2Change">
              <view class="picker-view {{!villageGroupColumns[1][villageGroupIndices[1]] ? 'placeholder' : ''}}" style="padding: 10px 12px; background-color: #f8f8f8; border-radius: 4px; display: flex; justify-content: space-between; align-items: center;">
                <text style="color: {{villageGroupColumns[1][villageGroupIndices[1]] ? '#333' : '#999'}};">{{villageGroupColumns[1][villageGroupIndices[1]].region_name || '请选择组'}}</text>
                <image src="../../icons/arrow-down.png" class="picker-arrow" style="width: 16px; height: 16px;" wx:if="{{!arrowIconMissing}}"></image>
              </view>
            </picker>
          </view>
        </view>
      </view>

      <!-- 统一的隐藏村组ID字段 -->
      <input hidden="true" name="sscz" value="{{selectedVillageId}}"/>

 
      <!-- 提交按钮 -->
      <view class="submit-section safe-bottom">
        <button class="submit-button" formType="submit" disabled="{{isSubmitting}}">
          <block wx:if="{{isSubmitting}}">
            <view class="loading-container">
              <view class="loading-spinner"></view>
              <text class="loading-text">提交中...</text>
            </view>
          </block>
          <block wx:else>
            <text>提交反馈</text>
          </block>
        </button>
      </view>
    </form>
  </view>
</view>

