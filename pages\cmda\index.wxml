<view class="container">
  <!-- 顶部背景 -->
  <view class="top-bg"></view>
  
  <!-- 页面标题 -->
  <view class="page-header">
    <text class="page-title">村民档案管理中心</text>
  </view>
  
  <!-- 概览卡片 -->
  <view class="overview-card">
    <view class="overview-header">
      <view class="overview-title">户籍人口总数</view>
      <view class="total-count">{{totalCount}}<text class="count-unit">人</text></view>
    </view>
    
    <!-- 操作按钮组 -->
    <view class="quick-actions">
      <navigator url="/pages/cmda/list" redirect="true" open-type="redirect" class="action-item">
        <image src="../../icons/news.png" class="action-icon"></image>
        <text>全部档案</text>
      </navigator>
      <view class="action-item" bindtap="addNew">
        <image src="../../icons/up.png" class="action-icon"></image>
        <text>添加档案</text>
      </view>
      <view class="action-item" bindtap="viewExport">
        <image src="../../icons/jd.png" class="action-icon"></image>
        <text>导出数据</text>
      </view>
      <view class="action-item" bindtap="viewSettings">
        <image src="../../icons/lx.png" class="action-icon"></image>
        <text>设置</text>
      </view>
    </view>
  </view>
  
  <!-- 特殊人群统计卡片 -->
  <view class="section-card">
    <view class="section-header">
      <view class="section-title">各类人群统计</view>
      <view class="section-more" bindtap="viewMoreStats">查看更多</view>
    </view>
    
    <!-- 加载中提示 -->
    <view class="loading-tip" wx:if="{{loading}}">
      <view class="loading-spinner"></view>
      <text>数据加载中...</text>
    </view>
    
    <!-- 错误提示 -->
    <view class="error-tip" wx:if="{{error}}">
      <image src="../../icons/error.png" class="error-icon"></image>
      <text>{{error}}</text>
    </view>
    
    <!-- 统计网格 -->
    <view class="stats-grid" wx:if="{{!loading && !error && gridList.length > 0}}">
      <view 
        class="grid-item" 
        wx:for="{{gridList}}" 
        wx:key="name" 
        bindtap="viewDetail" 
        data-type="{{item.name}}"
        style="background-color: {{item.color}}15;">
        <view class="grid-item-content">
          <view class="item-dot" style="background-color: {{item.color}};"></view>
          <view class="item-name">{{item.name}}</view>
          <view class="item-count">{{item.count}}</view>
        </view>
        <view class="item-tag" style="background-color: {{item.color}};">{{item.percentText}}</view>
      </view>
    </view>
    
    <view class="empty-state" wx:if="{{!loading && !error && gridList.length === 0}}">
      <image src="../../icons/empty.png" class="empty-icon"></image>
      <text class="empty-text">暂无统计数据</text>
    </view>
  </view>
  
  <!-- 村证件卡片 -->
  <view class="section-card">
    <view class="section-header">
      <view class="section-title">村证件</view>
      <view class="section-more" bindtap="manageVillageDocs">管理证件</view>
    </view>
    
    <!-- 加载中提示 -->
    <view class="loading-tip" wx:if="{{villageDocsLoading}}">
      <view class="loading-spinner"></view>
      <text>数据加载中...</text>
    </view>
    
    <!-- 错误提示 -->
    <view class="error-tip" wx:if="{{villageDocsError}}">
      <image src="../../icons/error.png" class="error-icon"></image>
      <text>{{villageDocsError}}</text>
    </view>
    
    <!-- 证件列表 -->
    <view class="docs-list" wx:if="{{!villageDocsLoading && !villageDocsError && villageDocs.length > 0}}">
      <view class="doc-item" wx:for="{{villageDocs}}" wx:key="id" bindtap="viewDocImage" data-url="{{item.imageUrl}}">
        <view class="doc-thumb" style="background-image: url('{{item.imageUrl}}')"></view>
        <view class="doc-info">
          <view class="doc-name">{{item.name}}</view>
          <view class="doc-action">
            <text>点击查看</text>
            <image src="../../icons/see.png" class="preview-icon"></image>
          </view>
        </view>
      </view>
    </view>
    
    <view class="empty-state" wx:if="{{!villageDocsLoading && !villageDocsError && villageDocs.length === 0}}">
      <image src="../../icons/empty.png" class="empty-icon"></image>
      <text class="empty-text">暂无村证件数据</text>
    </view>
  </view>
  
  <!-- 待办事项卡片 -->
  <view class="section-card">
    <view class="section-header">
      <view class="section-title">待办事项</view>
      <view class="section-more" bindtap="viewTasks">查看全部</view>
    </view>
    
    <view class="task-list">
      <view class="empty-state small">
        <text class="empty-text">暂无待办事项</text>
      </view>
      <!-- 这里将来可以添加待办事项列表 -->
    </view>
  </view>
</view> 