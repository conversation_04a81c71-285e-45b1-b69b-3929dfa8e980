<view class="container">
  <!-- 顶部背景 -->
  <view class="top-bg"></view>
  
  <!-- 页面标题 -->
  <view class="page-header">
    <text class="page-title">各类人群统计详情</text>
  </view>
  
  <!-- 统计卡片 -->
  <view class="stats-card">
    <view class="stats-header">
      <view class="stats-double">
        <view class="stats-block">
          <view class="stats-title">人口总数</view>
          <view class="total-count">{{totalCount}}<text class="count-unit">人</text></view>
        </view>
        <view class="stats-block">
          <view class="stats-title">特殊人群</view>
          <view class="total-count special-count">{{totalOptions}}<text class="count-unit">人</text></view>
        </view>
      </view>
    </view>
    
    <!-- 加载中提示 -->
    <view class="loading-tip" wx:if="{{loading}}">
      <view class="loading-spinner"></view>
      <text>数据加载中...</text>
    </view>
    
    <!-- 统计列表 -->
    <view class="stats-list" wx:if="{{!loading && statsList.length > 0}}">
      <view 
        class="stats-item" 
        wx:for="{{statsList}}" 
        wx:key="name" 
        bindtap="viewTypeDetail" 
        data-type="{{item.name}}">
        
        <view class="stats-item-header">
          <view class="item-name-wrap">
            <view class="item-dot" style="background-color: {{item.color}};"></view>
            <view class="item-name">{{item.name}}</view>
          </view>
          <view class="item-count-card">
            <view class="card-top" style="background-color: {{item.color}};">
              <text class="item-count">{{item.count}}</text>
            </view>
            <view class="card-bottom">
              <text class="item-percent">{{item.percentText}}</text>
            </view>
          </view>
        </view>
        
        <view class="progress-bar-wrap">
          <view class="progress-bar-bg"></view>
          <view class="progress-bar" style="width: {{item.percentage}}%; background-color: {{item.color}};"></view>
        </view>
      </view>
    </view>
    
    <view class="empty-state" wx:if="{{!loading && statsList.length === 0}}">
      <image src="../../icons/empty.png" class="empty-icon"></image>
      <text class="empty-text">暂无统计数据</text>
    </view>
  </view>
  
  <!-- 统计说明 -->
  <view class="stats-note">
    <view class="note-title">数据说明</view>
    <view class="note-content">
      <text>1. 点击各项数据可查看该类型的详细村民名单</text>
      <text>2. 特殊人群分类根据户籍属性自动生成</text>
      <text>3. 统计数据时时自动更新</text>
    </view>
  </view>
</view> 