var app=getApp();

const defaultAvatarUrl = '../../icons/vip.png'

Page({
  data: {
    avatarUrl: defaultAvatarUrl,
    member: wx.getStorageSync('member'),
    loading: false,
    ssczName: '未设置' // 所属村组名称
  },

  onShow: function(){
    this.loadMemberInfo();
  },

  onLoad: function(){
    this.loadMemberInfo();
  },

  // 将时间戳转换为可读日期格式
  formatTimestamp: function(timestamp) {
    if (!timestamp) return '-';
    
    // 将字符串转为数字
    const ts = parseInt(timestamp);
    
    // 检查是否是有效数字
    if (isNaN(ts)) return '-';
    
    // 创建日期对象（时间戳需要乘以1000转换为毫秒）
    const date = new Date(ts * 1000);
    
    // 格式化为yyyy-MM-dd HH:mm:ss
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    const hours = date.getHours().toString().padStart(2, '0');
    const minutes = date.getMinutes().toString().padStart(2, '0');
    const seconds = date.getSeconds().toString().padStart(2, '0');
    
    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
  },

  // 加载会员信息
  loadMemberInfo: function() {
    const member_uid = wx.getStorageSync('member_uid');
    if (!member_uid) {
      wx.showToast({
        title: '用户未登录',
        icon: 'none',
        duration: 2000
      });
      return;
    }

    this.setData({ loading: true });

    // 记录API调用
    const requestUrl = `${app.globalData.http_api}&appid=${app.globalData.appid}&appsecret=${app.globalData.appsecret}&s=httpapi&m=member&uid=${member_uid}`;
    console.log('获取会员信息API:', requestUrl);

    wx.request({
      url: requestUrl,
      method: 'GET',
      success: (res) => {
        console.log('会员信息响应:', res.data);
        
        if (res.data.code == 1) {
          // 更新会员信息
          const memberData = res.data.data;
          
          // 格式化日期时间
          if (memberData.regtime) {
            memberData.regtime = this.formatTimestamp(memberData.regtime);
          }
          
          if (memberData.lastlogintime) {
            memberData.lastlogintime = this.formatTimestamp(memberData.lastlogintime);
          }
          
          // 保存到缓存
          wx.setStorageSync('member', memberData);
          
          // 更新页面数据
          this.setData({
            member: memberData,
            avatarUrl: memberData.avatar || defaultAvatarUrl,
            loading: false
          });

          // 如果有所属村组ID，则获取其名称
          if (memberData.sscz) {
            this.loadSsczName(memberData.sscz);
          }
        } else {
          wx.showToast({
            title: res.data.msg || '获取信息失败',
            icon: 'none',
            duration: 2000
          });
          this.setData({ loading: false });
        }
      },
      fail: (err) => {
        console.log('请求错误:', err);
        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none',
          duration: 2000
        });
        this.setData({ loading: false });
      }
    });
  },

  // 加载所属村组名称
  loadSsczName: function(ssczId) {
    if (!ssczId) {
      this.setData({ ssczName: '未设置' });
      return;
    }

    const ssczIdStr = String(ssczId); // 转换为字符串确保比较一致
    console.log('正在查找村组ID:', ssczIdStr);
    
    // 首先查询一级村组
    wx.request({
      url: app.globalData.http_api + 's=api&c=api&m=linkage_ld&mid=member&file=&code=sscz&parent_id=0',
      method: 'GET',
      success: (res) => {
        console.log('获取一级村组数据:', res.data);
        
        if (res.data && res.data.data && Array.isArray(res.data.data)) {
          // 尝试在一级中查找
          const level1Item = res.data.data.find(item => String(item.region_id) === ssczIdStr);
          
          if (level1Item) {
            // 找到一级匹配
            console.log('找到一级村组匹配:', level1Item.region_name);
            this.setData({ ssczName: level1Item.region_name });
            return;
          }
          
          // 如果在一级中未找到，则需要检查所有二级
          this.checkLevel2Sscz(res.data.data, ssczIdStr);
        }
      },
      fail: (err) => {
        console.error('获取村组数据失败:', err);
        this.setData({ ssczName: ssczIdStr + ' (查询失败)' });
      }
    });
  },

  // 检查二级村组
  checkLevel2Sscz: function(level1Options, ssczIdStr) {
    // 用于跟踪已完成的请求数量
    let completedRequests = 0;
    const totalRequests = level1Options.length;
    let found = false;
    
    console.log('开始检查二级村组, 总共需查询:', totalRequests);
    
    // 检查所有可能的二级选项
    level1Options.forEach(parent => {
      wx.request({
        url: app.globalData.http_api + 's=api&c=api&m=linkage_ld&mid=member&file=&code=sscz&parent_id=' + parent.region_id,
        method: 'GET',
        success: (res) => {
          completedRequests++;
          console.log('二级村组查询进度:', completedRequests, '/', totalRequests);
          
          if (!found && res.data && res.data.data && Array.isArray(res.data.data)) {
            const level2Item = res.data.data.find(item => String(item.region_id) === ssczIdStr);
            
            if (level2Item) {
              // 找到二级匹配
              found = true;
              console.log('找到二级村组匹配:', parent.region_name, '-', level2Item.region_name);
              this.setData({ 
                ssczName: parent.region_name + ' - ' + level2Item.region_name 
              });
            }
          }
          
          // 如果所有请求都完成了，但仍然没有找到匹配项
          if (completedRequests === totalRequests && !found) {
            // 如果所有二级都找不到，则直接显示ID
            console.log('未找到匹配的村组, 显示ID:', ssczIdStr);
            this.setData({ ssczName: ssczIdStr });
          }
        },
        fail: (err) => {
          completedRequests++;
          console.error('获取二级村组数据失败:', err);
          
          // 检查是否所有请求都已完成
          if (completedRequests === totalRequests && !found) {
            this.setData({ ssczName: ssczIdStr });
          }
        }
      });
    });
  },

  nickInput: function(t) {
    console.log(t);
    var postParams = "is_ajax=1&"
    + "&data[name]=" +t.detail.value;
    wx.request({
      url: app.globalData.http_api + "s=member&c=account&m=index&api_auth_code=" + wx.getStorageSync('member_auth') + "&api_auth_uid=" +wx.getStorageSync('member_uid'),
      data: postParams,
      method: 'post',
      header: { 'Content-Type': 'application/x-www-form-urlencoded'},
      success: function(res){        
        var ret = res.data;
        if (ret.code == 1) {
            wx.removeStorageSync('member');
            wx.setStorageSync('member', res.data.data);
            this.setData({
              member:res.data.data
            })
            wx.showToast({
                  icon: 'success',
                  duration: 2000
            });
        } else {
              wx.showModal({
                  showCancel: false,
                  content: ret.msg
              })
        }
      }
    })
  },

  onChooseAvatar(e) {
    const { avatarUrl } = e.detail 
    this.setData({
      avatarUrl,
    })
    wx.uploadFile({
      url: app.globalData.http_api + "s=member&c=account&m=avatar&api_auth_code=" + wx.getStorageSync('member_auth') + "&api_auth_uid=" +wx.getStorageSync('member_uid'),
      filePath: avatarUrl,
      name:'file',
      formData: {
        is_ajax:1
      },
      header: { 'Content-Type': 'application/x-www-form-urlencoded'},
      success: function(res){
        var ret = JSON.parse(res.data);
        console.log('filePath',ret);
        if (ret.code == 1) {
              // 储存会员信息
              wx.removeStorageSync('member');
              ret.data.avatar += '&r='+Math.random(); //变更图片随机数
              wx.setStorageSync('member', ret.data);
              wx.showToast({
                  icon: 'success',
                  duration: 2000
              });
              this.setData({
                avatarUrl: ret.data.avatar,
              })
        } else {
              wx.showModal({
                  showCancel: false,
                  content: ret.msg
              })
        }
      }
    })
  }
})