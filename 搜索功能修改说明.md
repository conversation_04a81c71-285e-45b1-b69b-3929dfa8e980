# 首页搜索功能修改说明

## 修改背景
根据用户反馈，原来的搜索功能不能执行搜索，需要参考 `pages/cmda/list` 中的搜索参数来实现村民档案搜索功能。

## 主要修改内容

### 1. 搜索对象变更
- **修改前**: 搜索工单标题、内容或编号
- **修改后**: 搜索村民姓名或身份证号

### 2. 搜索目标页面变更
- **修改前**: 跳转到 `/pages/ticket/list`（工单列表）
- **修改后**: 跳转到 `/pages/cmda/list`（村民档案列表）

### 3. 权限控制增强
- **新增**: 用户登录状态验证
- **新增**: 管理员权限验证
- **提示**: 未登录用户会提示先登录
- **提示**: 非管理员用户会提示权限不足

### 4. 快捷功能区调整
- **修改前**: "工单查询" → `/pages/ticket/list`
- **修改后**: "村民档案" → `/pages/cmda/list`

## 技术实现细节

### 1. 搜索参数传递
```javascript
// 首页搜索执行
wx.navigateTo({
  url: '/pages/cmda/list?search=' + encodeURIComponent(keyword)
});
```

### 2. 村民档案列表页面接收参数
```javascript
// pages/cmda/list.js onLoad方法中新增
if (options && options.search) {
  const searchKeyword = decodeURIComponent(options.search);
  this.setData({
    searchKeyword: searchKeyword,
    isSearching: true
  });
}
```

### 3. 权限验证逻辑
```javascript
// 检查登录状态
if (!member || !member_auth || !member_uid) {
  // 提示登录
}

// 检查管理员权限
if (!member.is_admin || member.is_admin <= 0) {
  // 提示权限不足
}
```

## 搜索流程

### 用户操作流程
1. 用户在首页搜索框输入村民姓名或身份证号
2. 点击搜索按钮或按回车键
3. 系统验证用户登录状态和管理员权限
4. 验证通过后跳转到村民档案列表页面
5. 村民档案列表页面自动执行搜索并显示结果

### 权限验证流程
1. 检查用户是否已登录
   - 未登录：提示登录并跳转到登录页面
   - 已登录：继续下一步
2. 检查用户是否为管理员
   - 非管理员：提示权限不足
   - 管理员：执行搜索跳转

## 兼容性保证

### 1. 搜索历史功能保持不变
- 搜索历史的保存和加载机制不变
- 搜索历史的显示和清空功能不变
- 只是搜索内容从工单变为村民

### 2. 界面样式保持不变
- 搜索框的外观和交互效果不变
- 搜索历史的展示样式不变
- 只修改了占位符文本

### 3. 最新更新列表功能不受影响
- 工单和公告的最新更新显示正常
- 筛选和加载更多功能正常
- 权限控制逻辑保持不变

## 测试要点

### 1. 权限测试
- [ ] 未登录用户搜索时的提示和跳转
- [ ] 普通用户搜索时的权限提示
- [ ] 管理员用户的正常搜索功能

### 2. 搜索功能测试
- [ ] 搜索关键词的正确传递
- [ ] 村民档案列表页面的搜索结果显示
- [ ] 搜索历史的正常保存和使用

### 3. 界面测试
- [ ] 搜索框占位符文本正确显示
- [ ] 快捷功能区"村民档案"按钮正常
- [ ] 其他功能不受影响

## 文件修改清单

1. **pages/home/<USER>
   - 修改搜索框占位符文本

2. **pages/home/<USER>
   - 修改 `performSearch` 方法，增加权限验证
   - 修改快捷功能区配置，"工单查询"改为"村民档案"

3. **pages/cmda/list.js**
   - 修改 `onLoad` 方法，支持接收搜索参数

4. **pages/home/<USER>
   - 更新功能说明文档

5. **test-home-page.md**
   - 更新测试清单

## 注意事项

1. **权限要求**: 村民档案搜索功能仅限管理员使用
2. **数据安全**: 搜索功能遵循现有的权限控制机制
3. **用户体验**: 非管理员用户会收到友好的权限提示
4. **向后兼容**: 不影响现有的其他功能模块
