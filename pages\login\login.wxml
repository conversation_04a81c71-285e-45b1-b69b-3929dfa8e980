<view class="login-container">
  <view class="login-header">
    <image src="../../icons/logo.png" class="logo-image" mode="aspectFit" />
    <text class="login-title">内部工作系统</text>
    <text class="login-subtitle">请使用工作账号登录</text>
  </view>

  <form class="login-form">
    <view class="input-group">
      <view class="input-label">账号</view>
      <view class="input-wrapper">
        <image src="../../icons/user.png" class="input-icon" />
        <input
          placeholder="请输入工作账号"
          type="text"
          class="input-field"
          bindinput="getUserName"
          value="{{userName}}"
          placeholder-style="color: #bbb;"
        />
        <image
          src="../../icons/shutdown.png"
          class="clear-icon"
          hidden="{{!userName}}"
          bindtap="clear"
          catchtap="clear"
        />
      </view>

      <view class="input-label">密码</view>
      <view class="input-wrapper">
        <image src="../../icons/lock.png" class="input-icon" />
        <input
          placeholder="请输入密码"
          class="input-field"
          password="{{!showPassword}}"
          bindinput="getUserPwd"
          value="{{userPwd}}"
          placeholder-style="color: #bbb;"
        />
        <image
          src="{{showPassword ? '../../icons/see.png' : '../../icons/nosee.png'}}"
          class="password-icon"
          bindtap="togglePasswordVisibility"
        />
      </view>
    </view>

    <view class="options-row">
      <view class="remember-pwd">
        <checkbox checked="{{remember}}" bindtap="toggleRemember" color="#4facfe" />
        <text>记住我</text>
      </view>
    </view>

    <button class="login-btn" bindtap="login" loading="{{isLoading}}" hover-class="btn-hover">
      <text class="btn-text">登录</text>
    </button>
  </form>
</view>


