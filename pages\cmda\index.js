const app = getApp();

Page({
  data: {
    loading: true,
    statsData: {},
    totalCount: 0,
    member: null,
    error: null,
    gridList: [],
    recentDocs: [], // 最近更新的文档
    tasks: [], // 待办事项
    villageDocsLoading: true,
    villageDocsError: null,
    villageDocs: [], // 村证件数据
    hasLoadedData: false // 标记是否已加载数据
  },

  onLoad: function() {
    // 获取会员信息并加载数据
    this.checkMemberAndLoadData();
  },

  onShow: function() {
    // 如果从其他页面返回，判断是否需要重新加载数据
    if (!this.data.hasLoadedData) {
      this.checkMemberAndLoadData();
    }
  },

  // 检查会员信息并加载数据
  checkMemberAndLoadData: function() {
    // 检查会员信息
    var member = wx.getStorageSync('member');
    var member_auth = wx.getStorageSync('member_auth');
    var member_uid = wx.getStorageSync('member_uid');
    
    if (member == "" || !member_auth || !member_uid) {
      wx.showModal({
        title: '提示',
        content: '请先登录',
        showCancel: false,
        success: function() {
          wx.reLaunch({ url: "../login/login" });
        }
      });
      return;
    }
    
    // 检查是否为管理员
    if (!member.is_admin || member.is_admin <= 0) {
      wx.showModal({
        title: '提示',
        content: '此功能仅管理员可用',
        showCancel: false,
        success: function() {
          wx.navigateBack();
        }
      });
      return;
    }
    
    // 设置会员信息
    this.setData({ 
      member: member,
      hasLoadedData: true // 标记数据将被加载
    });
    
    // 加载数据
    this.loadStats();
    this.loadVillageDocs(); // 加载村证件数据
  },
  
  // 加载统计数据
  loadStats: function() {
    this.setData({ loading: true, error: null });
    
    wx.showLoading({ title: '加载中...' });
    
    // 获取验证信息
    var member_uid = wx.getStorageSync('member_uid');
    var member_auth = wx.getStorageSync('member_auth');
    
    if (!member_uid || !member_auth) {
      wx.hideLoading();
      this.setData({
        loading: false,
        error: '请先登录'
      });
      return;
    }
    
    // 构建API请求URL
    const requestUrl = app.globalData.http_api + 
      's=httpapi&id=10' +
      '&appid=' + app.globalData.appid +
      '&appsecret=' + app.globalData.appsecret;
    
    wx.request({
      url: requestUrl,
      method: 'GET',
      header: {
        'content-type': 'application/json'
      },
      success: res => {
        wx.hideLoading();
        
        if (res.data.code == 1 && res.data.data) {
          const responseData = res.data.data;
          
          if (responseData.summary && Array.isArray(responseData.data)) {
            // 获取总记录数
            const totalCount = responseData.summary.total_records || 0;
            
            // 创建一个兼容的statsData对象（用于其他函数）
            const statsData = {'总计': totalCount};
            
            // 直接使用API返回的数据
            const gridList = [];
            
            // 遍历数据并添加一些UI相关属性
            responseData.data.forEach(item => {
              if (item && item.name && item.count !== undefined) {
                // 保存到statsData以备其他函数使用
                statsData[item.name] = item.count;
                
                // 添加到gridList并分配颜色
                gridList.push({
                  name: item.name,
                  count: item.count,
                  color: this.getTypeColor(item.name),
                  percentage: item.percent ? parseFloat(item.percent) : 0,
                  percentText: item.percent || '0%'
                });
              }
            });
            
            // 只显示前6个最多的类别
            const displayList = gridList.slice(0, 6);
            
            this.setData({
              loading: false,
              statsData: statsData,
              totalCount: totalCount,
              gridList: displayList
            });
          } else {
            // 旧格式处理（备用）
            this.setData({
              loading: false,
              error: '数据格式不正确'
            });
          }
        } else {
          this.setData({
            loading: false,
            error: res.data.msg || '获取统计数据失败'
          });
        }
      },
      fail: err => {
        wx.hideLoading();
        
        this.setData({
          loading: false,
          error: '网络错误，请重试'
        });
      }
    });
  },
  
  // 加载村证件数据
  loadVillageDocs: function() {
    this.setData({ villageDocsLoading: true, villageDocsError: null });
    
    // 获取验证信息
    var member_uid = wx.getStorageSync('member_uid');
    var member_auth = wx.getStorageSync('member_auth');
    
    if (!member_uid || !member_auth) {
      this.setData({
        villageDocsLoading: false,
        villageDocsError: '请先登录'
      });
      return;
    }
    
    // 构建API请求URL
    const requestUrl = app.globalData.http_api + 
      's=httpapi&m=site' +
      '&appid=' + app.globalData.appid +
      '&appsecret=' + app.globalData.appsecret;
    
    wx.request({
      url: requestUrl,
      method: 'GET',
      header: {
        'content-type': 'application/json'
      },
      success: res => {
        if (res.data.code == 1 && res.data.data && res.data.data.cunzj) {
          // 处理数据格式并转化为方便渲染的数组
          const docsData = res.data.data.cunzj;
          const villageDocs = [];
          
          // 遍历证件数据，格式化为适合前端展示的结构
          for (let i = 0; i < docsData.length; i++) {
            const doc = docsData[i];
            if (doc && doc[1] && doc[2]) {
              villageDocs.push({
                name: doc[1],
                imageUrl: doc[2],
                id: i + 1
              });
            }
          }
          
          this.setData({
            villageDocsLoading: false,
            villageDocs: villageDocs
          });
        } else {
          this.setData({
            villageDocsLoading: false,
            villageDocsError: '获取村证件数据失败'
          });
        }
      },
      fail: err => {
        this.setData({
          villageDocsLoading: false,
          villageDocsError: '网络错误，请重试'
        });
      }
    });
  },
  
  // 为不同类型分配固定颜色
  getTypeColor: function(key) {
    const colorMap = {
      '党员': '#E74C3C',      // 红色
      '孤儿': '#9B59B6',      // 紫色
      '重点优抚': '#3498DB',  // 蓝色
      '计生优抚': '#1ABC9C',  // 青绿色
      '脱贫户': '#2ECC71',    // 绿色
      '低保户': '#F1C40F',    // 黄色
      '残疾户': '#E67E22',    // 橙色
      '五保户': '#34495E',    // 深蓝色
      '兵役': '#16A085',      // 深青色
      '销户': '#7F8C8D',      // 灰色
      '特困': '#D35400',      // 深橙色
      '监测户': '#27AE60'     // 浅绿色
    };
    
    return colorMap[key] || `#${Math.floor(Math.random()*16777215).toString(16)}`;
  },
  
  // 查看某类型的详情列表
  viewDetail: function(e) {
    const type = e.currentTarget.dataset.type;
    if (!type) return;
    
    // 使用hujishuxing作为直接参数名称
    const url = './list?type=' + encodeURIComponent(type) + '&field=hujishuxing&hujishuxing=' + encodeURIComponent(type);
    
    wx.navigateTo({
      url: url,
      fail: function(err) {
        wx.showToast({
          title: '页面跳转失败',
          icon: 'none'
        });
      }
    });
  },
  
  // 查看所有村民档案
  viewAllVillagers: function(e) {
    // 使用绝对路径并尝试多种导航方式
    try {
      wx.redirectTo({
        url: '/pages/cmda/list',
        fail: function(err) {
          // 尝试使用navigateTo
          wx.navigateTo({
            url: '/pages/cmda/list',
            fail: function(err2) {
              // 尝试使用reLaunch
              wx.reLaunch({
                url: '/pages/cmda/list',
                fail: function(err3) {
                  wx.showToast({
                    title: '页面跳转失败',
                    icon: 'none'
                  });
                }
              });
            }
          });
        }
      });
    } catch (err) {
      wx.showToast({
        title: '页面跳转失败',
        icon: 'none'
      });
    }
  },
  
  // 添加新档案
  addNew: function() {
    wx.navigateTo({
      url: './cmdaedit'
    });
  },
  
  // 导出数据
  viewExport: function() {
    wx.showModal({
      title: '导出数据',
      content: '此功能暂未开放，敬请期待',
      showCancel: false
    });
  },
  
  // 设置页面
  viewSettings: function() {
    wx.showModal({
      title: '设置',
      content: '此功能暂未开放，敬请期待',
      showCancel: false
    });
  },
  
  // 查看更多统计数据
  viewMoreStats: function() {
    // 不再传递数据，而是在statsDetail页面直接获取最新数据
    wx.navigateTo({
      url: './statsDetail'
    });
  },
  
  // 查看村证件详情图片
  viewDocImage: function(e) {
    const imageUrl = e.currentTarget.dataset.url;
    if (!imageUrl) return;
    
    wx.previewImage({
      current: imageUrl,
      urls: [imageUrl]
    });
  },
  
  // 管理村证件
  manageVillageDocs: function() {
    wx.showModal({
      title: '管理村证件',
      content: '此功能暂未开放，敬请期待',
      showCancel: false
    });
  },
  
  // 查看待办事项列表
  viewTasks: function() {
    wx.showModal({
      title: '待办事项',
      content: '此功能暂未开放，敬请期待',
      showCancel: false
    });
  },
  
  // 下拉刷新
  onPullDownRefresh: function() {
    // 重置加载状态
    this.setData({ hasLoadedData: false });
    // 重新加载数据
    this.checkMemberAndLoadData();
    wx.stopPullDownRefresh();
  }
}); 