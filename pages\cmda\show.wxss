/* 页面容器 */
.container {
  min-height: 100vh;
  background: #f8f9fa;
  position: relative;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 60vh;
  color: #6c757d;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #e9ecef;
  border-top: 4rpx solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 30rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #6c757d;
}

/* 错误状态 */
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 60vh;
  padding: 40rpx;
  text-align: center;
}

.error-icon {
  font-size: 120rpx;
  margin-bottom: 30rpx;
}

.error-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #dc3545;
  margin-bottom: 20rpx;
}

.error-message {
  font-size: 28rpx;
  color: #6c757d;
  margin-bottom: 40rpx;
  line-height: 1.5;
}

.retry-button {
  background: #007bff;
  color: white;
  border: none;
  border-radius: 50rpx;
  padding: 20rpx 40rpx;
  font-size: 28rpx;
}

/* 主要内容区域 */
.main-content {
  min-height: calc(100vh - 120rpx);
  padding-bottom: 120rpx;
}

.content-wrapper {
  padding: 30rpx;
}

/* 用户信息卡片 */
.user-card {
  background: white;
  border-radius: 24rpx;
  padding: 40rpx;
  margin-bottom: 30rpx;
  display: flex;
  align-items: center;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.user-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  margin-right: 30rpx;
  overflow: hidden;
  background: #e9ecef;
  display: flex;
  align-items: center;
  justify-content: center;
}

.avatar-image {
  width: 100%;
  height: 100%;
}

.avatar-placeholder {
  font-size: 48rpx;
  font-weight: 600;
  color: #6c757d;
}

.user-info {
  flex: 1;
}

.user-name {
  font-size: 36rpx;
  font-weight: 600;
  color: #212529;
  margin-bottom: 10rpx;
}

.user-id {
  font-size: 24rpx;
  color: #6c757d;
  margin-bottom: 15rpx;
}

.user-status {
  display: inline-block;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
  font-weight: 500;
}

.user-status.active {
  background: #d4edda;
  color: #155724;
}

.user-status.inactive {
  background: #f8d7da;
  color: #721c24;
}

/* 信息卡片 */
.info-card {
  background: white;
  border-radius: 24rpx;
  padding: 40rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.card-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #212529;
  margin-bottom: 30rpx;
  padding-bottom: 20rpx;
  border-bottom: 2rpx solid #f8f9fa;
}

.info-list {
  display: flex;
  flex-direction: column;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f8f9fa;
}

.info-item:last-child {
  border-bottom: none;
}

.info-label {
  font-size: 28rpx;
  color: #6c757d;
  font-weight: 500;
}

.info-value {
  font-size: 28rpx;
  color: #212529;
  font-weight: 500;
  text-align: right;
  max-width: 60%;
  word-break: break-all;
}

/* 图片资料卡片 */
.photo-status-card {
  background: white;
  border-radius: 24rpx;
  padding: 40rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.photo-status-list {
  display: flex;
  flex-direction: column;
}

.photo-status-item {
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f8f9fa;
}

.photo-status-item:last-child {
  border-bottom: none;
}

.photo-status-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.photo-status-right {
  display: flex;
  align-items: center;
  gap: 15rpx;
}

.photo-status-label {
  font-size: 28rpx;
  color: #495057;
  font-weight: 500;
}

.photo-status-indicator {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.photo-status-indicator .status-text {
  font-size: 26rpx;
  font-weight: 600;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
}

.photo-status-indicator.has-photo .status-text {
  background: #d4edda;
  color: #155724;
}

.photo-status-indicator.no-photo .status-text {
  background: #f8d7da;
  color: #721c24;
}

.photo-count {
  font-size: 24rpx;
  color: #6c757d;
  font-weight: 500;
}

/* 图片操作按钮 */
.photo-action-btn {
  padding: 8rpx 16rpx;
  border-radius: 16rpx;
  font-size: 22rpx;
  font-weight: 500;
  border: none;
  min-width: 80rpx;
  text-align: center;
  transition: all 0.3s ease;
}

.photo-action-btn.upload {
  background: linear-gradient(135deg, #28a745, #20c997);
  color: white;
}

.photo-action-btn.update {
  background: linear-gradient(135deg, #007bff, #0056b3);
  color: white;
}

.photo-action-btn:active {
  transform: scale(0.95);
  opacity: 0.8;
}

/* 操作卡片 */
.action-card {
  background: white;
  border-radius: 24rpx;
  padding: 40rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.action-grid {
  display: flex;
  justify-content: space-between;
  gap: 20rpx;
}

.action-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 30rpx 20rpx;
  border-radius: 16rpx;
  background: #f8f9fa;
  transition: all 0.3s ease;
}

.action-item.primary {
  background: linear-gradient(135deg, #007bff, #0056b3);
  color: white;
}

.action-item:active {
  transform: scale(0.95);
}

.action-icon {
  font-size: 48rpx;
  margin-bottom: 15rpx;
}

.action-item.primary .action-icon {
  filter: brightness(0) invert(1);
}

.action-text {
  font-size: 24rpx;
  font-weight: 500;
  color: #495057;
}

.action-item.primary .action-text {
  color: white;
}

/* 时间信息卡片 */
.time-card {
  background: white;
  border-radius: 24rpx;
  padding: 40rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.time-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15rpx 0;
}

.time-item:first-child {
  border-bottom: 1rpx solid #f8f9fa;
  padding-bottom: 20rpx;
  margin-bottom: 20rpx;
}

.time-label {
  font-size: 28rpx;
  color: #6c757d;
  font-weight: 500;
}

.time-value {
  font-size: 26rpx;
  color: #495057;
}

/* 底部操作栏 */
.bottom-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 20rpx 30rpx;
  padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
  border-top: 1rpx solid #e9ecef;
  display: flex;
  gap: 20rpx;
  z-index: 1000;
}

.bottom-btn {
  flex: 1;
  height: 80rpx;
  border-radius: 40rpx;
  font-size: 28rpx;
  font-weight: 500;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

.bottom-btn.primary {
  background: linear-gradient(135deg, #007bff, #0056b3);
  color: white;
}

.bottom-btn.secondary {
  background: #f8f9fa;
  color: #6c757d;
  border: 2rpx solid #e9ecef;
}

.bottom-btn:active {
  transform: scale(0.98);
}

/* 响应式调整 */
@media (max-width: 750rpx) {
  .content-wrapper {
    padding: 20rpx;
  }

  .user-card,
  .info-card,
  .photo-status-card,
  .action-card,
  .time-card {
    padding: 30rpx;
    margin-bottom: 20rpx;
  }

  .action-grid {
    gap: 15rpx;
  }

  .action-item {
    padding: 25rpx 15rpx;
  }
}
