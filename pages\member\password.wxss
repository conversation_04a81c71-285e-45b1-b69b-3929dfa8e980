@import "../login/login.wxss";
@import "../member/index.wxss";
.userPwd{
    margin-top: 0;
    margin-bottom: 30rpx;
}
.warp-pos image.yz-pos{
    width: 190rpx;
    height: 55rpx;
     margin-top: -27.5rpx;
}
.fw{
    font-size: 26rpx;
    color: #bcbdc1;
    text-align: center;
    margin-top: 40rpx;
}
.fw text{
     color: #389fcf;
}
.login-username-info {
  margin-bottom: 70rpx;
  text-align: center
}

.container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 30rpx;
  box-sizing: border-box;
}

.password-card {
  background: white;
  border-radius: 20rpx;
  padding: 40rpx 30rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
  margin: 0 auto;
  width: 100%;
  box-sizing: border-box;
}

.password-header {
  margin-bottom: 50rpx;
  text-align: center;
}

.password-title {
  font-size: 40rpx;
  color: #333;
  font-weight: 600;
  margin-bottom: 20rpx;
  display: block;
}

.password-username {
  font-size: 28rpx;
  color: #666;
  background: #f8f9fa;
  padding: 12rpx 24rpx;
  border-radius: 8rpx;
  display: inline-block;
}

.form-item {
  margin-bottom: 36rpx;
  width: 100%;
  box-sizing: border-box;
}

.form-label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 16rpx;
  display: block;
  font-weight: 500;
}

.input-wrap {
  position: relative;
  display: flex;
  align-items: center;
  width: 100%;
  box-sizing: border-box;
}

.form-input {
  width: 100%;
  height: 96rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  padding: 0 24rpx;
  padding-right: 80rpx;
  font-size: 30rpx;
  color: #333;
  box-sizing: border-box;
  transition: all 0.3s ease;
}

.form-input:focus {
  background: #f0f2f5;
  box-shadow: 0 0 0 2rpx rgba(24, 144, 255, 0.1);
}

.form-input::placeholder {
  color: #999;
  font-size: 28rpx;
}

.input-icon {
  position: absolute;
  right: 24rpx;
  width: 36rpx;
  height: 36rpx;
  opacity: 0.6;
}

.submit-btn {
  width: 100%;
  height: 96rpx;
  background: linear-gradient(135deg, #1890ff, #096dd9);
  color: white;
  font-size: 32rpx;
  font-weight: 500;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 50rpx;
  box-shadow: 0 6rpx 16rpx rgba(24, 144, 255, 0.25);
  box-sizing: border-box;
  transition: all 0.3s ease;
}

.submit-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 12rpx rgba(24, 144, 255, 0.2);
  opacity: 0.9;
}

/* 添加输入框聚焦时的动画效果 */
@keyframes focusAnimation {
  0% { transform: scale(1); }
  50% { transform: scale(1.02); }
  100% { transform: scale(1); }
}

.form-input:focus {
  animation: focusAnimation 0.3s ease;
}
