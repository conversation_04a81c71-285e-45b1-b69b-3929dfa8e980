<view class="container">
  <!-- 头部区域 -->
  <view class="header">
    <view class="page-title">问答论坛</view>
    <view class="search-bar">
      <input type="text" placeholder="搜索问题" bindinput="onSearchInput" />
      <view class="search-btn" bindtap="doSearch">
        <image src="../../icons/search.png" class="search-icon"></image>
      </view>
    </view>
  </view>

  <!-- 问题分类标签 -->
  <scroll-view scroll-x enable-flex class="category-tabs">
    <view class="category-item {{currentCategory === 'all' ? 'active' : ''}}" data-category="all" bindtap="switchCategory">全部</view>
    <view class="category-item {{currentCategory === 'hot' ? 'active' : ''}}" data-category="hot" bindtap="switchCategory">热门</view>
    <view class="category-item {{currentCategory === 'unanswered' ? 'active' : ''}}" data-category="unanswered" bindtap="switchCategory">待解答</view>
    <view class="category-item {{currentCategory === 'inprocess' ? 'active' : ''}}" data-category="inprocess" bindtap="switchCategory">解决中</view>
    <view class="category-item {{currentCategory === 'solved' ? 'active' : ''}}" data-category="solved" bindtap="switchCategory">已解决</view>
  </scroll-view>

  <!-- 问题列表 -->
  <view class="question-list">
    <block wx:for="{{questions}}" wx:key="id">
      <view class="question-item">
        <navigator url="../wenda/detail?id={{item.id}}" class="question-main">
          <view class="question-header">
            <image src="{{item.avatar}}" class="user-avatar" mode="aspectFill"></image>
            <view class="question-info">
              <text class="username">{{item.username}}</text>
              <text class="post-time">{{item.create_time}}</text>
            </view>
            <view class="question-status {{item.status_value == 1 ? 'solved' : (item.status_value == 2 ? 'inprocess' : 'unsolved')}}">
              {{item.status_text}}
            </view>
          </view>
          <view class="question-title">{{item.title}}</view>
          <view class="question-content">{{item.content}}</view>
        </navigator>
        <view class="question-footer">
          <view class="stat-item">
            <image src="../../icons/see.png" class="stat-icon"></image>
            <text>{{item.view_count}}</text>
          </view>
          <view class="stat-item">
            <image src="../../icons/comment.png" class="stat-icon"></image>
            <text>{{item.answer_count}}</text>
          </view>
          <view class="digg-buttons">
            <view class="digg-item helpful" catchtap="moduleDigg" data-id="{{item.id}}" data-value="1">
              <image src="../../icons/up.png" class="digg-icon"></image>
              <text>有帮助（<text id="digg_{{item.id}}_1">{{item.like_count || '-'}}</text>）</text>
            </view>
            <view class="digg-item unhelpful" catchtap="moduleDigg" data-id="{{item.id}}" data-value="0">
              <image src="../../icons/shutdown.png" class="digg-icon"></image>
              <text>没帮助（<text id="digg_{{item.id}}_0">{{item.unlike_count || '-'}}</text>）</text>
            </view>
          </view>
        </view>
      </view>
    </block>
  </view>

  <!-- 加载更多 -->
  <view class="loading-status">
    <view class="loading-more" wx:if="{{isLoading}}">
      <text>加载中...</text>
    </view>
    <view class="no-more" wx:if="{{!hasMore && questions.length > 0}}">
      <text>已经到底啦</text>
    </view>
    <view class="empty-list" wx:if="{{!isLoading && questions.length === 0}}">
      <text>暂无相关内容</text>
    </view>
  </view>

  <!-- 发布按钮 -->
  <view class="float-btn" bindtap="createQuestion">
    <image src="../../icons/add.png" class="add-icon"></image>
    <text class="float-btn-text">发布问题</text>
  </view>
</view>
