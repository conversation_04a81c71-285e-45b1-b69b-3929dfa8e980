.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f7fa;
  position: relative;
  z-index: 1;
}

/* 标签栏样式 - 更新设计 */
.tabs-container {
  background-color: #fff;
  position: sticky;
  top: 90rpx;
  z-index: 2;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  width: 100%;
}

.tabs-scroll {
  width: 100%;
}

.tabs {
  display: flex;
  width: 100%;
  justify-content: space-between;
  padding: 0 10rpx;
}

.tab {
  flex: 1;
  padding: 24rpx 5rpx;
  position: relative;
  transition: all 0.3s;
  text-align: center;
}

.tab text {
  font-size: 24rpx;
  color: #666;
  transition: all 0.3s;
  display: block;
}

.tab.active text {
  color: #1890ff;
  font-weight: 500;
}

.tab.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 40rpx;
  height: 4rpx;
  background-color: #1890ff;
  border-radius: 4rpx;
}

/* 列表内容样式 - 重新设计 */
.news-list {
  flex: 1;
  height: calc(100vh - 180rpx);
  position: relative;
  z-index: 1;
}

.news-list-content {
  padding: 20rpx;
}

/* 卡片样式升级 */
.news-card {
  background: #ffffff;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
  overflow: hidden;
  position: relative;
  display: flex;
  border-left: none;
  transition: transform 0.2s;
}

.news-card:active {
  transform: scale(0.98);
}

/* 状态指示器 - 新设计 */
.status-indicator {
  width: 8rpx;
  height: 100%;
  position: absolute;
  left: 0;
  top: 0;
  background-color: #e8e8e8;
}

.status-urgent {
  background-color: #ff4d4f;
}

.status-completed {
  background-color: #52c41a;
}

.status-pending {
  background-color: #faad14;
}

.status-processing {
  background-color: #1890ff;
}

.status-waiting {
  background-color: #722ed1;
}

.status-replied {
  background-color: #13c2c2;
}

.news-card-content {
  padding: 24rpx 24rpx 24rpx 36rpx;
  flex: 1;
}

/* ID徽章 - 新组件 */
.id-badge {
  display: inline-block;
  background-color: #f0f5ff;
  color: #1890ff;
  font-size: 22rpx;
  padding: 4rpx 12rpx;
  border-radius: 6rpx;
  margin-bottom: 12rpx;
}

/* 标题样式更新 */
.news-title {
  font-size: 30rpx;
  color: #333;
  line-height: 1.4;
  font-weight: 500;
  margin-bottom: 16rpx;
}

.highlight-title {
  color: #ff4d4f;
  font-weight: 600;
}

/* 标签容器 - 新组件 */
.tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
  margin-bottom: 16rpx;
}

.tag {
  display: inline-flex;
  align-items: center;
  padding: 4rpx 16rpx;
  border-radius: 4rpx;
  font-size: 22rpx;
}

.tag-type {
  background-color: #e6f7ff;
  color: #1890ff;
  border: 1px solid #91d5ff;
}

.tag-department {
  background-color: #f9f0ff;
  color: #722ed1;
  border: 1px solid #d3adf7;
}

.tag-status {
  background-color: #f6ffed;
  color: #52c41a;
  border: 1px solid #b7eb8f;
}

/* 状态标签颜色 */
.tag-status.status-pending {
  background-color: #fff7e6;
  color: #faad14;
  border: 1px solid #ffe58f;
}

.tag-status.status-processing {
  background-color: #e6f7ff;
  color: #1890ff;
  border: 1px solid #91d5ff;
}

.tag-status.status-completed {
  background-color: #f6ffed;
  color: #52c41a;
  border: 1px solid #b7eb8f;
}

.tag-status.status-waiting {
  background-color: #f9f0ff;
  color: #722ed1;
  border: 1px solid #d3adf7;
}

.tag-status.status-replied {
  background-color: #e6fffb;
  color: #13c2c2;
  border: 1px solid #87e8de;
}

.news-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 16rpx;
  padding-top: 16rpx;
  border-top: 1px solid #f0f0f0;
}

.news-time {
  display: flex;
  align-items: center;
  color: #999;
  font-size: 24rpx;
}

.news-stats {
  display: flex;
  gap: 20rpx;
}

.stat-item {
  display: flex;
  align-items: center;
  color: #999;
  font-size: 24rpx;
}

.icon-small {
  width: 28rpx;
  height: 28rpx;
  margin-right: 8rpx;
}

/* 加载状态样式 */
.loading-container {
  padding: 30rpx 0;
  text-align: center;
}

.loading-tip {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #999;
  font-size: 24rpx;
}

.loading-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 12rpx;
}

.no-more-tip {
  color: #999;
  font-size: 24rpx;
}

/* 无数据状态样式 - 改进 */
.no-data {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 0;
}

.no-data-icon {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 20rpx;
  opacity: 0.7;
}

.no-data-text {
  font-size: 28rpx;
  color: #999;
}

/* 搜索框 - 改进 */
.search-container {
  padding: 16rpx 24rpx;
  background-color: #fff;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  display: flex;
  align-items: center;
  gap: 20rpx;
  position: sticky;
  top: 0;
  z-index: 2;
}

.search-box {
  flex: 1;
  display: flex;
  align-items: center;
  background-color: #f5f5f5;
  border-radius: 30rpx;
  padding: 8rpx 20rpx;
  height: 70rpx;
  position: relative;
}

.search-btn {
  padding: 0 30rpx;
  height: 70rpx;
  line-height: 70rpx;
  background-color: #1890ff;
  color: #fff;
  border-radius: 8rpx;
  font-size: 28rpx;
  white-space: nowrap;
  margin-left: 10rpx;
}

.search-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 10rpx;
}

.search-input {
  flex: 1;
  height: 60rpx;
  font-size: 28rpx;
}

.placeholder {
  color: #999;
}

.clear-btn {
  position: absolute;
  right: 20rpx;
  top: 50%;
  transform: translateY(-50%);
  padding: 10rpx;
  z-index: 3;
}

.clear-icon {
  width: 32rpx;
  height: 32rpx;
  opacity: 0.5;
  display: block;
}

/* 操作菜单样式 */
.action-menu {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 999;
}

.action-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
}

.action-content {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  border-radius: 20rpx 20rpx 0 0;
  padding: 20rpx 0;
}

.action-item {
  padding: 30rpx;
  text-align: center;
  font-size: 32rpx;
  color: #333;
  border-bottom: 1rpx solid #eee;
}

.action-item.delete {
  color: #ff4d4f;
}

.action-item:last-child {
  border-bottom: none;
}