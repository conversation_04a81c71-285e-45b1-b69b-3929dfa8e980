var app = getApp();//获取appid
var http_url = app.globalData.http_api + "s=member&app=scorelog&c=home&m=index";

Page({

    /**
     * 页面的初始数据
     */
    data: {
        listData:[],
        hidden: true,
        page: 1,
        hasMore: false,
        isRefreshing: false,
        currentTab: 0,
        tabs: [
            {name: "全部", tid: ""},
            {name: "收入", tid: "1"},
            {name: "消费", tid: "-1"}
        ]
    },

    onLoad:function(options){
      var member = wx.getStorageSync('member');
      if (member == "") {
        // 未登录跳转登录界面
        wx.reLaunch({ url: "../login/login" });
        return;
      }
      this.loadData();
    },

    switchTab: function(e) {
        var index = e.currentTarget.dataset.index;
        this.setData({
            currentTab: index,
            page: 1,
            listData: [],
            hasMore: false
        });
        this.loadData();
    },

    onRefresh: function() {
        this.setData({
            isRefreshing: true,
            page: 1,
            hasMore: false
        });
        this.loadData(true);
    },

    loadData: function(isRefresh = false) {
        var self = this;
        var currentTab = this.data.tabs[this.data.currentTab];
        var url = http_url;
        
        if (currentTab.tid !== "") {
            url += '&tid=' + currentTab.tid + '&total=0&order=&field=id';
        }
        
        url += '&api_call_function=member_paylog_list';
        url += '&api_auth_uid=' + wx.getStorageSync('member_uid');
        url += '&api_auth_code=' + wx.getStorageSync('member_auth');
        
        wx.request({
            url: url,
            method: 'GET',
            data: {
                page: this.data.page
            },
            success: function(res) {
                if (res.data.code == 1) {
                    self.setData({
                        listData: res.data.data,
                        hasMore: res.data.data.length >= 10 // 假设每页10条数据
                    });
                } else {
                    wx.showToast({
                        icon: 'error',
                        title: res.data.msg,
                        duration: 2000
                    });
                }
            },
            complete: function() {
                if (isRefresh) {
                    self.setData({
                        isRefreshing: false
                    });
                }
                self.setData({
                    hidden: true
                });
            }
        });
    },

    onReachBottom:function(){
        if (!this.data.hasMore || !this.data.hidden) {
            return;
        }

        this.setData({hidden:false});
        var self=this;
        var currentTab = this.data.tabs[this.data.currentTab];
        var url = http_url;
        
        if (currentTab.tid !== "") {
            url += '&tid=' + currentTab.tid + '&total=0&order=&field=id';
        }
        
        url += '&api_call_function=member_paylog_list';
        url += '&api_auth_uid=' + wx.getStorageSync('member_uid');
        url += '&api_auth_code=' + wx.getStorageSync('member_auth');

        wx.request({
            url: url,
            method: 'GET',
            data: {
                page: this.data.page + 1
            },
            success: function(res){
                if (res.data.code == 1) {
                    if (res.data.data.length > 0) {
                        self.setData({
                            listData: self.data.listData.concat(res.data.data),
                            page: self.data.page + 1,
                            hasMore: res.data.data.length >= 10 // 假设每页10条数据
                        });
                    } else {
                        self.setData({
                            hasMore: false
                        });
                    }
                } else {
                    wx.showToast({
                        image: '../../icons/shutdown.png',
                        title: res.data.msg,
                        duration: 999999999
                    })
                }
            }
        })
    }
})