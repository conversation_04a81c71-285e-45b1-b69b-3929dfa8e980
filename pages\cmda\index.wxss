.container {
  padding: 0 0 40rpx 0;
  min-height: 100vh;
  background-color: #f8f9fa;
  position: relative;
}

.top-bg {
  height: 220rpx;
  background: linear-gradient(45deg, #3a8ef0, #2c6dd5);
  width: 100%;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1;
}

.page-header {
  position: relative;
  z-index: 2;
  padding: 30rpx 30rpx 20rpx;
}

.page-title {
  color: #ffffff;
  font-size: 38rpx;
  font-weight: bold;
}

/* 概览卡片样式 */
.overview-card {
  position: relative;
  z-index: 2;
  margin: 0 30rpx 30rpx;
  background: #ffffff;
  border-radius: 15rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
  padding: 30rpx 20rpx;
}

.overview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 20rpx;
  margin-bottom: 20rpx;
}

.overview-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
}

.total-count {
  font-size: 48rpx;
  font-weight: bold;
  color: #3a8ef0;
  display: flex;
  align-items: flex-end;
}

.count-unit {
  font-size: 24rpx;
  color: #999;
  margin-left: 5rpx;
  margin-bottom: 8rpx;
}

/* 快捷操作按钮 */
.quick-actions {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 15rpx;
}

.action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20rpx 0;
  border-radius: 10rpx;
  background-color: #f8f9fa;
}

.action-icon {
  width: 48rpx;
  height: 48rpx;
  margin-bottom: 10rpx;
}

.action-item text {
  font-size: 24rpx;
  color: #666;
}

/* 部分卡片通用样式 */
.section-card {
  position: relative;
  z-index: 2;
  margin: 0 30rpx 30rpx;
  background: #ffffff;
  border-radius: 15rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
  padding: 20rpx;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 15rpx;
  margin-bottom: 15rpx;
}

.section-title {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
  position: relative;
  padding-left: 15rpx;
}

.section-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 6rpx;
  bottom: 6rpx;
  width: 6rpx;
  background-color: #3a8ef0;
  border-radius: 3rpx;
}

.section-more {
  font-size: 24rpx;
  color: #3a8ef0;
}

/* 加载提示样式 */
.loading-tip {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx 0;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #3a8ef0;
  border-radius: 50%;
  margin-bottom: 15rpx;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-tip text {
  font-size: 26rpx;
  color: #999;
}

/* 错误提示样式 */
.error-tip {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx 0;
}

.error-icon {
  width: 80rpx;
  height: 80rpx;
  margin-bottom: 15rpx;
  opacity: 0.7;
}

.error-tip text {
  font-size: 26rpx;
  color: #e74c3c;
  text-align: center;
}

/* 统计网格样式 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15rpx;
}

.grid-item {
  border-radius: 10rpx;
  padding: 15rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.03);
  position: relative;
  height: 100rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.grid-item-content {
  display: flex;
  align-items: center;
}

.item-dot {
  width: 14rpx;
  height: 14rpx;
  border-radius: 50%;
  margin-right: 10rpx;
}

.item-name {
  font-size: 26rpx;
  color: #333;
  flex: 1;
}

.item-count {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-left: 10rpx;
}

.item-tag {
  position: absolute;
  top: 10rpx;
  right: 10rpx;
  font-size: 20rpx;
  color: #fff;
  padding: 2rpx 8rpx;
  border-radius: 20rpx;
}

/* 空状态样式 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx 0;
}

.empty-state.small {
  padding: 30rpx 0;
}

.empty-icon {
  width: 80rpx;
  height: 80rpx;
  margin-bottom: 15rpx;
  opacity: 0.5;
}

.empty-text {
  color: #999;
  font-size: 26rpx;
}

/* 村证件列表样式 */
.docs-list {
  padding: 10rpx 0;
}

.doc-item {
  display: flex;
  align-items: center;
  padding: 15rpx 10rpx;
  border-bottom: 1rpx solid #f5f5f5;
}

.doc-item:last-child {
  border-bottom: none;
}

.doc-thumb {
  width: 120rpx;
  height: 80rpx;
  background-size: cover;
  background-position: center;
  border-radius: 8rpx;
  margin-right: 15rpx;
  border: 1rpx solid #f0f0f0;
  background-color: #f9f9f9;
}

.doc-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.doc-name {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
}

.doc-action {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.doc-action text {
  font-size: 22rpx;
  color: #3a8ef0;
  margin-right: 5rpx;
}

.preview-icon {
  width: 24rpx;
  height: 24rpx;
}

/* 最近更新和待办卡片样式 */
.task-list {
  min-height: 100rpx;
} 