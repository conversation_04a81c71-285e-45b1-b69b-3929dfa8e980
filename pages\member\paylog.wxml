<view class="container">
  <!-- Tab 导航栏 -->
  <view class="tab-nav">
    <view class="tab-item {{currentTab == 0 ? 'active' : ''}}" bindtap="switchTab" data-tab="0">
      <text>全部</text>
      <view class="tab-line"></view>
    </view>
    <view class="tab-item {{currentTab == 1 ? 'active' : ''}}" bindtap="switchTab" data-tab="1">
      <text>收入</text>
      <view class="tab-line"></view>
    </view>
    <view class="tab-item {{currentTab == -1 ? 'active' : ''}}" bindtap="switchTab" data-tab="-1">
      <text>支出</text>
      <view class="tab-line"></view>
    </view>
  </view>

  <!-- 记录列表 -->
  <scroll-view 
    class="content-list" 
    scroll-y="true" 
    enable-back-to-top="true"
    bindscrolltolower="loadMore"
    refresher-enabled="{{true}}"
    refresher-triggered="{{isRefreshing}}"
    bindrefresherrefresh="onRefresh"
  >
    <block wx:for="{{listData}}" wx:key="id">
      <view class="record-card" bindtap="goToDetail" data-id="{{item.id}}">
        <view class="record-header">
          <view class="record-title-wrap">
            <text class="record-title">{{item.title}}</text>
            <text class="record-type {{item.value > 0 ? 'type-income' : 'type-expense'}}">
              {{item.value > 0 ? '收入' : '支出'}}
            </text>
          </view>
          <text class="record-amount {{item.value > 0 ? 'amount-income' : 'amount-expense'}}">
            {{item.value > 0 ? '+' : ''}}¥{{item.value}}
          </text>
        </view>
        <view class="record-info">
          <view class="info-item">
            <text class="info-label">流水号：</text>
            <text class="info-value">{{item.id}}</text>
          </view>
          <view class="info-item">
            <text class="status-tag {{item.status == 1 ? 'status-paid' : 'status-unpaid'}}">
              {{item.status == 1 ? '已付款' : '未付款'}}
            </text>
          </view>
        </view>
        <view class="record-footer">
          <text class="record-time">{{item.inputtime}}</text>
          <view class="record-action">
            <text class="action-text">查看详情</text>
            <image class="action-icon" src="../../icons/arrow-right.png" mode="aspectFit"></image>
          </view>
        </view>
      </view>
    </block>

    <!-- 加载状态 -->
    <view class="loading-state" hidden="{{hidden}}">
      <view class="loading-content" wx:if="{{hasMore!='true'}}">
        <view class="loading-spinner"></view>
        <text class="loading-text">正在加载...</text>
      </view>
      <view class="no-more" wx:else>
        <view class="divider">
          <view class="divider-line"></view>
          <text class="no-more-text">没有更多数据了</text>
          <view class="divider-line"></view>
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <view class="empty-state" wx:if="{{listData.length === 0 && hidden}}">
      <image class="empty-icon" src="../../icons/empty-paylog.png" mode="aspectFit"></image>
      <text class="empty-text">暂无收支记录</text>
    </view>
  </scroll-view>
</view>