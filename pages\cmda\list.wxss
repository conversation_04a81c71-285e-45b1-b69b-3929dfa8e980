.container {
  padding: 0 0 40rpx 0;
  min-height: 100vh;
  background-color: #f6f6f6;
  position: relative;
}

.top-bg {
  height: 220rpx;
  background: linear-gradient(45deg, #3a8ef0, #2c6dd5);
  width: 100%;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1;
}

.page-header {
  position: relative;
  z-index: 2;
  padding: 30rpx 30rpx 20rpx;
}

.page-title {
  color: #ffffff;
  font-size: 38rpx;
  font-weight: bold;
}

/* 搜索框样式 */
.search-box {
  position: relative;
  z-index: 2;
  padding: 0 30rpx 20rpx;
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.search-input-wrap {
  display: flex;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.9);
  padding: 15rpx 20rpx;
  border-radius: 35rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
  flex: 1;
}

.search-icon {
  width: 36rpx;
  height: 36rpx;
  margin-right: 15rpx;
}

.search-input {
  flex: 1;
  height: 40rpx;
  font-size: 28rpx;
  color: #333;
}

.clear-icon {
  width: 36rpx;
  height: 36rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.clear-icon image {
  width: 28rpx;
  height: 28rpx;
  opacity: 0.5;
}

.search-btn {
  background: linear-gradient(45deg, #3a8ef0, #2c6dd5);
  color: #fff;
  height: 70rpx;
  width: 120rpx;
  border-radius: 35rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
  font-weight: 500;
}

.content-area {
  position: relative;
  z-index: 2;
  margin: 20rpx 30rpx;
  background: #ffffff;
  border-radius: 15rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
  padding: 20rpx;
  min-height: 800rpx;
}

/* 搜索结果提示样式 */
.search-result-tip {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10rpx 10rpx 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
  margin-bottom: 15rpx;
  font-size: 26rpx;
  color: #666;
}

.back-to-all {
  color: #3a8ef0;
  font-size: 26rpx;
  padding: 6rpx 15rpx;
  border: 1rpx solid #3a8ef0;
  border-radius: 30rpx;
}

/* 筛选标签样式 */
.filter-tag-wrap {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10rpx 10rpx 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
  margin-bottom: 15rpx;
}

.filter-tag {
  display: flex;
  align-items: center;
  font-size: 26rpx;
  color: #666;
  background-color: #f5f5f5;
  padding: 6rpx 15rpx;
  border-radius: 30rpx;
}

.clear-filter {
  color: #e74c3c;
  margin-left: 15rpx;
  font-size: 24rpx;
}

.back-to-stats {
  display: flex;
  align-items: center;
  font-size: 26rpx;
  color: #3a8ef0;
  background-color: rgba(58, 142, 240, 0.1);
  padding: 8rpx 15rpx;
  border-radius: 30rpx;
}

.stats-icon {
  width: 28rpx;
  height: 28rpx;
  margin-right: 6rpx;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.empty-icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 20rpx;
  opacity: 0.5;
}

.empty-text {
  color: #999;
  font-size: 28rpx;
}

.cmda-list {
  width: 100%;
}

.cmda-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 25rpx 10rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.cmda-info {
  display: flex;
  flex-direction: column;
}

.cmda-name {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 8rpx;
}

.cmda-id {
  font-size: 24rpx;
  color: #666;
}

.cmda-meta {
  display: flex;
  align-items: center;
}

.cmda-address {
  font-size: 24rpx;
  color: #999;
  max-width: 200rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin-right: 10rpx;
}

.arrow-icon {
  width: 32rpx;
  height: 32rpx;
}

/* 加载更多提示 */
.loading-more {
  text-align: center;
  padding: 20rpx 0;
}

.loading-more text {
  font-size: 24rpx;
  color: #999;
}

.footer-actions {
  margin: 40rpx 30rpx 20rpx;
}

.add-btn {
  background: linear-gradient(45deg, #3a8ef0, #2c6dd5);
  color: #fff;
  text-align: center;
  padding: 20rpx 0;
  border-radius: 10rpx;
  font-size: 30rpx;
  font-weight: 500;
} 