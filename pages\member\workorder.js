var app = getApp();
const util = require('../../utils/util.js');

Page({
  data: {
    listData: [],
    page: 1,
    limit: 10,
    hasMore: false,
    loading: false,
    refreshing: false,
    keyword: '',
    isSearching: false,
    hidden: true
  },

  onLoad: function(options) {
    this.checkLoginAndLoadData();
  },

  // 检查登录状态并加载数据
  checkLoginAndLoadData: function() {
    const member = wx.getStorageSync('member');
    if (!member) {
      wx.reLaunch({ 
        url: "../login/login",
        success: () => {
          wx.showToast({
            title: '请先登录',
            icon: 'none'
          });
        }
      });
      return;
    }

    this.loadData(true);
  },

  // 加载数据
  loadData: function(isRefresh = false) {
    if (this.data.loading) return;
    
    this.setData({ 
      loading: true,
      hidden: false
    });
    
    const member_uid = wx.getStorageSync('member_uid');
    const member_auth = wx.getStorageSync('member_auth');
    
    if (!member_uid || !member_auth) {
      this.setData({ 
        loading: false,
        hidden: true 
      });
      wx.reLaunch({ 
        url: "../login/login",
        success: () => {
          wx.showToast({
            title: '登录信息已过期',
            icon: 'none'
          });
        }
      });
      return;
    }

    const page = isRefresh ? 1 : this.data.page;
    
    let requestUrl = `${app.globalData.http_api}&s=member&app=workorder&c=home&m=index&api_auth_code=${member_auth}&api_auth_uid=${member_uid}&page=${page}&pagesize=${this.data.limit}`;
    
    // 如果有搜索关键词，添加到请求URL
    if (this.data.isSearching && this.data.keyword) {
      requestUrl += `&keyword=${encodeURIComponent(this.data.keyword)}`;
    }
    
    console.log('请求URL:', requestUrl);

    wx.request({
      url: requestUrl,
      method: 'GET',
      success: (res) => {
        console.log('返回数据:', res.data);
        if (res.data.code == 1) {
          const list = res.data.data.list || [];
          // 格式化时间
          const formattedList = list.map(item => ({
            ...item,
            inputtime: this.formatTime(item.inputtime)
          }));
          
          // 判断是否还有更多数据
          const hasMore = list.length >= this.data.limit;
          
          this.setData({
            listData: isRefresh ? formattedList : [...this.data.listData, ...formattedList],
            page: isRefresh ? 2 : this.data.page + 1,
            hasMore: hasMore,
            hidden: true,
            refreshing: false
          });
        } else {
          wx.showToast({
            title: res.data.msg || '加载失败',
            icon: 'none',
            duration: 2000
          });
          
          this.setData({
            hidden: true,
            refreshing: false
          });
        }
      },
      fail: (err) => {
        console.log('请求失败:', err);
        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none',
          duration: 2000
        });
        
        this.setData({
          hidden: true,
          refreshing: false
        });
      },
      complete: () => {
        this.setData({ 
          loading: false
        });
        
        if (this.data.refreshing) {
          wx.stopPullDownRefresh();
        }
      }
    });
  },

  // 搜索输入处理
  onSearchInput: function(e) {
    this.setData({
      keyword: e.detail.value
    });
  },
  
  // 执行搜索
  doSearch: function() {
    if (!this.data.keyword.trim()) {
      if (this.data.isSearching) {
        // 如果之前在搜索状态，清除搜索并重新加载
        this.clearSearch();
      }
      return;
    }
    
    this.setData({
      isSearching: true,
      listData: [],
      page: 1,
      hasMore: false
    });
    
    this.loadData(true);
  },
  
  // 清除搜索
  clearSearch: function() {
    this.setData({
      keyword: '',
      isSearching: false,
      listData: [],
      page: 1,
      hasMore: false
    });
    
    this.loadData(true);
  },

  // 跳转到工单详情
  navigateToDetail: function(e) {
    const id = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: '/pages/workorder/show?id=' + id
    });
  },

  // 跳转到创建工单页面
  navigateToCreate: function() {
    wx.switchTab({
      url: '/pages/workorder/post'
    });
  },

  // 滚动到底部加载更多
  onReachBottom: function() {
    if (!this.data.hasMore || this.data.loading) return;
    this.loadData(false);
  },

  // 下拉刷新
  onPullDownRefresh: function() {
    this.setData({
      refreshing: true,
      page: 1,
      listData: []
    });
    
    this.loadData(true);
  },
  
  // 格式化时间戳
  formatTime: function(timestamp) {
    if (!timestamp) return '';
    
    const ts = parseInt(timestamp);
    if (isNaN(ts)) return timestamp;
    
    const date = new Date(ts * 1000);
    const now = new Date();
    const diff = Math.floor((now - date) / 1000);
    
    // 今天内
    if (diff < 86400 && date.getDate() === now.getDate()) {
      const hours = date.getHours().toString().padStart(2, '0');
      const minutes = date.getMinutes().toString().padStart(2, '0');
      return `今天 ${hours}:${minutes}`;
    }
    
    // 昨天
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    if (date.getDate() === yesterday.getDate() && 
        date.getMonth() === yesterday.getMonth() && 
        date.getFullYear() === yesterday.getFullYear()) {
      const hours = date.getHours().toString().padStart(2, '0');
      const minutes = date.getMinutes().toString().padStart(2, '0');
      return `昨天 ${hours}:${minutes}`;
    }
    
    // 一周内
    if (diff < 604800) {
      const days = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
      const day = days[date.getDay()];
      const hours = date.getHours().toString().padStart(2, '0');
      const minutes = date.getMinutes().toString().padStart(2, '0');
      return `${day} ${hours}:${minutes}`;
    }
    
    // 更早
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    
    return `${year}-${month}-${day}`;
  }
}); 