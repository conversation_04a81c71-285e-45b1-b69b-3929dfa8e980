.notice-detail {
  padding: 40rpx 30rpx;
  background: #fff;
  box-sizing: border-box;
  width: 100%;
  min-height: 100vh;
}

/* 公文标题样式 */
.notice-header {
  text-align: center;
  margin-bottom: 40rpx;
}

.notice-title {
  font-size: 40rpx;
  font-weight: bold;
  color: #000;
  margin-bottom: 20rpx;
  display: block;
  line-height: 1.5;
  word-break: break-all;
}

/* 公文头部信息 */
.notice-meta {
  display: flex;
  justify-content: space-between;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 40rpx;
  padding-bottom: 20rpx;
  border-bottom: 2rpx solid #000;
}

/* 公文正文样式 */
.notice-content {
  font-size: 32rpx;
  line-height: 1.8;
  color: #333;
  width: 100%;
  box-sizing: border-box;
  text-indent: 2em;
  word-break: break-all;
  overflow-x: hidden;
}

/* 富文本容器 */
.rich-text-container {
  width: 100%;
  box-sizing: border-box;
  overflow-x: hidden;
}

/* 图片样式 */
.rich-text-container image {
  max-width: 100% !important;
  height: auto !important;
  display: block;
  margin: 20rpx auto;
}

/* 表格样式 */
.rich-text-container table {
  width: 100% !important;
  max-width: 100% !important;
  border-collapse: collapse;
  margin: 20rpx 0;
  border: 2rpx solid #000;
  table-layout: fixed;
  word-break: break-all;
}

.rich-text-container table td {
  border: 1rpx solid #000;
  padding: 15rpx;
  text-align: center;
  word-break: break-all;
}

/* 文本样式 */
.rich-text-container p {
  margin: 20rpx 0;
  text-indent: 2em;
  line-height: 1.8;
  word-break: break-all;
}

.rich-text-container a {
  color: #000;
  text-decoration: underline;
  word-break: break-all;
}

/* 其他富文本元素 */
.rich-text-container div,
.rich-text-container span,
.rich-text-container strong,
.rich-text-container em {
  word-break: break-all;
  max-width: 100%;
  box-sizing: border-box;
}

/* 公文落款样式 */
.notice-footer {
  margin-top: 60rpx;
  text-align: right;
  padding-right: 60rpx;
  word-break: break-all;
}

.notice-date {
  font-size: 28rpx;
  color: #333;
  margin-top: 20rpx;
}

/* 公文印章样式 */
.notice-seal {
  position: absolute;
  right: 60rpx;
  bottom: 40rpx;
  width: 120rpx;
  height: 120rpx;
  opacity: 0.8;
} 