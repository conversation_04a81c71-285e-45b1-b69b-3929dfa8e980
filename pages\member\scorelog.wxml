<view class="container">
  <!-- Tab 导航栏 -->
  <view class="tab-nav">
    <view class="tab-item {{currentTab == index ? 'active' : ''}}" 
          wx:for="{{tabs}}" 
          wx:key="index" 
          data-index="{{index}}" 
          bindtap="switchTab">
      <text>{{item.name}}</text>
      <view class="tab-line"></view>
    </view>
  </view>

  <!-- 记录列表 -->
  <scroll-view 
    class="content-list" 
    scroll-y="true" 
    enable-back-to-top="true"
    bindscrolltolower="loadMore"
    refresher-enabled="{{true}}"
    refresher-triggered="{{isRefreshing}}"
    bindrefresherrefresh="onRefresh"
  >
    <block wx:for="{{listData}}" wx:key="id">
      <view class="record-card">
        <view class="record-header">
          <view class="record-title-wrap">
            <text class="record-title">{{item.note}}</text>
            <text class="record-type {{item.value > 0 ? 'type-income' : 'type-expense'}}">
              {{item.value > 0 ? '获得' : '消费'}}
            </text>
          </view>
          <text class="record-amount {{item.value > 0 ? 'amount-income' : 'amount-expense'}}">
            {{item.value > 0 ? '+' : ''}}{{item.value}}
          </text>
        </view>
        <view class="record-footer">
          <text class="record-time">{{item.inputtime}}</text>
        </view>
      </view>
    </block>

    <!-- 加载状态 -->
    <view class="loading-state" hidden="{{hidden}}">
      <view class="loading-content" wx:if="{{hasMore!='true'}}">
        <view class="loading-spinner"></view>
        <text class="loading-text">正在加载...</text>
      </view>
      <view class="no-more" wx:else>
        <view class="divider">
          <view class="divider-line"></view>
          <text class="no-more-text">没有更多数据了</text>
          <view class="divider-line"></view>
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <view class="empty-state" wx:if="{{listData.length === 0 && hidden}}">
      <image class="empty-icon" src="../../icons/empty-score.png" mode="aspectFit"></image>
      <text class="empty-text">暂无积分记录</text>
    </view>
  </scroll-view>
</view>