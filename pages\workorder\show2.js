var WxParse = require('../../wxParse/wxParse.js');

var app = getApp();
var http_url = app.globalData.http_api + "s=workorder&c=show";
http_url+= '&api_call_function=module_show';
var member_url = app.globalData.http_api + "s=api&app=workorder&c=module&api_auth_code=" + wx.getStorageSync('member_auth') + "&api_auth_uid=" + wx.getStorageSync('member_uid');

// 评论相关URL
var comment_url = app.globalData.http_api + "s=workorder&c=comment&page=1";
comment_url += '&api_call_function=module_comment_list';
var comment_save_url = app.globalData.http_api + "s=workorder&m=post&c=comment&api_auth_code=" + wx.getStorageSync('member_auth') + "&api_auth_uid=" + wx.getStorageSync('member_uid');
// 评论框图片上传URL
var upload_url = app.globalData.http_api + "s=api&c=file&m=ueditor&action=uploadimage&encode=utf-8";

Page({
  data:{
    id:'',
    content:'',
    supports: 0,
    upsImg:"../../icons/ups.png",
    collectImg:"../../icons/collect.png",
    commentList: [], // 评论列表
    commentText: '', // 评论内容
    isLoading: false, // 是否正在加载
    hasMore: true, // 是否还有更多评论
    page: 1, // 当前页码
    tempImagePaths: [], // 临时图片路径数组
    uploadedImages: [], // 已上传的图片URL数组
    member_uid: wx.getStorageSync('member_uid'), // 当前登录用户ID
  },

  onLoad:function(options){
    wx.showLoading({
      title: '加载中...',
    });
    var self=this;
    
    // 先获取点赞状态
    wx.request({
      url: app.globalData.http_api + "s=zan&mid=workorder&id=" + options.id,
      header: {
        'content-type': 'application/json'
      },
      dataType: 'json',
      method: 'GET',
      success: function(res) {
        if (res.data.code == 1) {
          // 直接使用服务器返回的数据
          self.setData({
            supports: res.data.data.b
          });
        }
      }
    });
    
    // 获取工单详情
    wx.request({
      url: http_url,
      data: {
        id:options.id
      },
      header: {
        'content-type': 'application/json'
      },
      dataType:'json',
      method: 'GET', 
      success: function(res){
        if (res.data.code == 1) {
          // 是否收藏
          wx.request({
            url: member_url +'&m=is_favorite',
            data: {
              id: options.id
            },
            header: {
              'content-type': 'application/json'
            },
            dataType: 'json',
            method: 'GET',
            success: function (sc) {
              if (sc.data.code == 1) {
                self.setData({
                  collectImg: "../../icons/collect-active.png",
                })
              }
            }
          });
          
          // 格式化文章内容
          var article = res.data.data.content;
          WxParse.wxParse('data', 'html', article, self);

          self.setData({
            content:res.data.data,
            id: options.id
          })
          wx.hideLoading();
          self.loadComments(); // 加载评论列表
        } else {
          wx.hideLoading();
          wx.showModal({
            showCancel: false,
            content: res.data.msg
          })
        }
      },
      fail: function() {
        wx.hideLoading();
        wx.showModal({
          showCancel: false,
          content: '加载失败，请重试'
        });
      }
    })
  },
   getCommentList:function(){//评论跳转

      wx.navigateTo({
        url: '../workorder/comment?id='+this.data.content.id
     })
   },


   up:function(){//点赞
     var self = this;
     
     // 显示加载中
     wx.showLoading({
       title: '点赞中...',
       mask: true
     });
     
     // 使用与网页端相同的API
     wx.request({
       url: app.globalData.http_api + "s=api&app=workorder&c=module&m=digg&id=" + self.data.id + "&value=1",
       header: {
         'content-type': 'application/json'
       },
       dataType: 'json',
       method: 'GET',
       success: function (sc) {
         wx.hideLoading();
         
         if (sc.data.code == 1) {
           // 更新点赞数
           const newSupports = sc.data.data;
           
           // 更新页面上的点赞数
           self.setData({
             supports: newSupports,
             upsImg: "../../icons/ups-active.png" // 更新点赞图标为激活状态
           });
           
           // 显示成功提示
           wx.showToast({
             icon: 'success',
             title: sc.data.msg || '点赞成功',
             duration: 2000
           });
           
           // 同时更新服务器上的点赞状态
           wx.request({
             url: app.globalData.http_api + "s=zan&mid=workorder&id=" + self.data.id,
             header: {
               'content-type': 'application/json'
             },
             dataType: 'json',
             method: 'GET',
             success: function(res) {
               if (res.data.code == 1) {
                 // 更新点赞数，使用b字段（有帮助的数量）
                 self.setData({
                   supports: res.data.data.b || newSupports
                 });
               }
             }
           });
         } else {
           wx.showModal({
             showCancel: false,
             content: sc.data.msg || '点赞失败'
           });
         }
       },
       fail: function() {
         wx.hideLoading();
         wx.showModal({
           showCancel: false,
           content: '网络错误，请重试'
         });
       }
     });
   },
   collect: function (){//收藏
     var self =this;
     wx.request({
       url: member_url + '&m=favorite',
       data: {
         id: self.data.id
       },
       header: {
         'content-type': 'application/json'
       },
       dataType: 'json',
       method: 'GET',
       success: function (sc) {
         if (sc.data.code == 1) {
           wx.showToast({
             icon: 'success',
             title: sc.data.msg,
             duration: 2000
           });
           if (sc.data.msg =='收藏成功') {
             self.setData(
               {
                 collectImg: "../../icons/collect-active.png",
               })
           } else {
             self.setData(
               {
                 collectImg: "../../icons/collect.png",
               })
           }
         } else {
           wx.showModal({
             showCancel: false,
             content: sc.data.msg
           })
         }
       }
     });

   },

   // 加载评论列表
   loadComments: function() {
     if (this.data.isLoading || !this.data.hasMore) return;
     
     this.setData({ isLoading: true });
     
     wx.request({
       url: comment_url,
       data: {
         id: this.data.id,
         page: this.data.page
       },
       header: {
         'content-type': 'application/json'
       },
       success: (res) => {
         if (res.data.code == 1) {
           const newList = res.data.data.list;
           // 处理每条评论的内容，并添加唯一标识符
           newList.forEach((item, index) => {
             // 使用评论ID和时间戳组合作为唯一标识符
             item.uniqueKey = `${item.id || ''}_${item.inputtime || ''}_${index}`;
             // 为每条评论创建一个唯一的节点名称
             const nodeName = `comment_${item.uniqueKey}`;
             WxParse.wxParse(nodeName, 'html', item.content, this, 5);
             // 将解析后的内容保存到评论对象中
             item.parsedContent = this.data[nodeName];
           });
           
           this.setData({
             commentList: [...this.data.commentList, ...newList],
             hasMore: newList.length > 0,
             page: this.data.page + 1
           });
         }
       },
       complete: () => {
         this.setData({ isLoading: false });
       }
     });
   },

   // 获取评论输入内容
   getText: function(e) {
     this.setData({
       commentText: e.detail.value
     });
   },

   // 选择图片
   chooseImage: function() {
     // 检查用户是否已登录
     const member_auth = wx.getStorageSync('member_auth');
     const member_uid = wx.getStorageSync('member_uid');
     
     if (!member_auth || !member_uid) {
       wx.showModal({
         title: '提示',
         content: '请先登录后再上传图片',
         confirmText: '去登录',
         success: function(res) {
           if (res.confirm) {
             wx.navigateTo({
               url: '../login/login'
             });
           }
         }
       });
       return;
     }
     
     wx.chooseMedia({
       count: 9, // 最多可以选择9张图片
       mediaType: ['image'],
       sourceType: ['album', 'camera'],
       success: (res) => {
         const tempFiles = res.tempFiles;
         this.setData({
           tempImagePaths: tempFiles.map(file => file.tempFilePath)
         });
         // 选择图片后批量上传
         this.uploadImages(tempFiles);
       }
     });
   },

   // 批量上传图片
   uploadImages: function(tempFiles) {
     wx.showLoading({
       title: '上传中...',
       mask: true
     });

     const uploadPromises = tempFiles.map(file => {
       return new Promise((resolve, reject) => {
         wx.uploadFile({
           url: upload_url,
           filePath: file.tempFilePath,
           name: 'upfile',
           header: {
             'content-type': 'multipart/form-data'
           },
           success: (res) => {
             try {
               const data = JSON.parse(res.data);
               if (data.state === 'SUCCESS' && data.url) {
                 resolve(data.url);
               } else {
                 reject(new Error('上传失败'));
               }
             } catch (e) {
               reject(e);
             }
           },
           fail: reject
         });
       });
     });

     Promise.all(uploadPromises)
       .then(urls => {
         wx.hideLoading();
         // 将所有图片URL添加到评论内容中
         const imageHtml = urls.map(url => `<img src="${url}" />`).join('');
         const currentText = this.data.commentText;
         this.setData({
           commentText: currentText + imageHtml,
           uploadedImages: [...this.data.uploadedImages, ...urls]
         });
         wx.showToast({
           title: '图片上传成功',
           icon: 'success'
         });
       })
       .catch(error => {
         wx.hideLoading();
         wx.showToast({
           title: '部分图片上传失败',
           icon: 'none'
         });
       });
   },

   // 修改保存评论的方法
   saveComment: function() {
     // 检查用户是否已登录
     const member_auth = wx.getStorageSync('member_auth');
     const member_uid = wx.getStorageSync('member_uid');
     
     if (!member_auth || !member_uid) {
       wx.showModal({
         title: '提示',
         content: '请先登录后再发表评论',
         confirmText: '去登录',
         success: function(res) {
           if (res.confirm) {
             wx.navigateTo({
               url: '../login/login'
             });
           }
         }
       });
       return;
     }
     
     if (!this.data.commentText.trim()) {
       wx.showToast({
         title: '请输入评论内容',
         icon: 'none'
       });
       return;
     }

     // 使用最新的登录信息构建URL
     const current_comment_save_url = app.globalData.http_api + "s=workorder&m=post&c=comment&api_auth_code=" + member_auth + "&api_auth_uid=" + member_uid;

     wx.request({
       url: current_comment_save_url + "&id=" + this.data.id,
       data: {
         content: this.data.commentText
       },
       header: {
         'content-type': 'application/x-www-form-urlencoded'
       },
       dataType: 'json',
       method: 'POST',
       success: (res) => {
         if (res.data.code == 1) {
           wx.showToast({
             title: res.data.msg,
             icon: 'success',
             duration: 1000
           });
           
           // 清空输入框
           this.setData({
             commentText: '',
             tempImagePaths: [],
             uploadedImages: []
           });
           
           // 延迟重新加载评论列表，避免抖动
           setTimeout(() => {
             this.setData({
               commentList: [],
               page: 1,
               hasMore: true
             });
             this.loadComments();
           }, 300);

           // 延迟更新评论数
           setTimeout(() => {
             wx.request({
               url: http_url,
               data: {
                 id: this.data.id
               },
               header: {
                 'content-type': 'application/json'
               },
               dataType:'json',
               method: 'GET', 
               success: (res) => {
                 if (res.data.code == 1) {
                   this.setData({
                     'content.comments': res.data.data.comments
                   });
                 }
               }
             });
           }, 500);
         } else {
           wx.showToast({
             title: res.data.msg || '评论失败',
             icon: 'none'
           });
         }
       }
     });
   },

   // 上拉加载更多
   onReachBottom: function() {
     this.loadComments();
   },

   // 预览图片佐证区域的图片
   previewEvidenceImage: function(e) {
     const urls = e.currentTarget.dataset.urls.map(item => item.file);
     const current = e.currentTarget.dataset.current.file;
     wx.previewImage({
       urls: urls,
       current: current
     });
   },

   // 处理图片佐证区域图片长按事件
   handleEvidenceImageLongPress: function(e) {
     const current = e.currentTarget.dataset.current;
     wx.showActionSheet({
       itemList: ['保存图片'],
       success: (res) => {
         if (res.tapIndex === 0) {
           wx.downloadFile({
             url: current.file,
             success: (res) => {
               if (res.statusCode === 200) {
                 wx.saveImageToPhotosAlbum({
                   filePath: res.tempFilePath,
                   success: () => {
                     wx.showToast({
                       title: '保存成功',
                       icon: 'success'
                     });
                   },
                   fail: () => {
                     wx.showToast({
                       title: '保存失败',
                       icon: 'none'
                     });
                   }
                 });
               }
             }
           });
         }
       }
     });
   },

   // 预览评论区域的图片
   previewCommentImage: function(e) {
     const src = e.currentTarget.dataset.src;
     const urls = this.data.commentList
       .flatMap(comment => {
         if (comment.parsedContent && comment.parsedContent.nodes) {
           return comment.parsedContent.nodes
             .filter(node => node.tag === 'img' && node.attr)
             .map(node => node.attr.src);
         }
         return [];
       })
       .filter(url => url);
     
     wx.previewImage({
       urls: urls,
       current: src
     });
   },

   // 处理评论区域图片长按事件
   handleCommentImageLongPress: function(e) {
     const src = e.currentTarget.dataset.src;
     if (!src) return;
     
     wx.showActionSheet({
       itemList: ['保存图片'],
       success: (res) => {
         if (res.tapIndex === 0) {
           wx.showLoading({
             title: '保存中...',
             mask: true
           });
           
           wx.downloadFile({
             url: src,
             success: (res) => {
               if (res.statusCode === 200) {
                 wx.saveImageToPhotosAlbum({
                   filePath: res.tempFilePath,
                   success: () => {
                     wx.hideLoading();
                     wx.showToast({
                       title: '保存成功',
                       icon: 'success'
                     });
                   },
                   fail: (err) => {
                     wx.hideLoading();
                     wx.showToast({
                       title: '保存失败',
                       icon: 'none'
                     });
                     console.error('保存图片失败:', err);
                   }
                 });
               } else {
                 wx.hideLoading();
                 wx.showToast({
                   title: '下载失败',
                   icon: 'none'
                 });
               }
             },
             fail: (err) => {
               wx.hideLoading();
               wx.showToast({
                 title: '下载失败',
                 icon: 'none'
               });
               console.error('下载图片失败:', err);
             }
           });
         }
       },
       fail: (err) => {
         console.error('显示操作菜单失败:', err);
       }
     });
   },

   // 完成工单
   completeOrder: function() {
     if (this.data.content.jindu == 3) return; // 如果已经是完成状态，直接返回
     
     var self = this;
     wx.showModal({
       title: '提示',
       content: '确定要完成工单吗？',
       success: function(res) {
         if (res.confirm) {
           wx.request({
             url: app.globalData.http_api + "s=workorder&c=api&m=myda&id=" + self.data.id + 
                  "&api_auth_code=" + wx.getStorageSync('member_auth') + 
                  "&api_auth_uid=" + wx.getStorageSync('member_uid'),
             header: {
               'content-type': 'application/json'
             },
             dataType: 'json',
             method: 'GET',
             success: function(res) {
               if (res.data.code == 1) {
                 wx.showToast({
                   title: '工单已完成',
                   icon: 'success'
                 });
                 
                 // 重新加载工单详情
                 wx.request({
                   url: http_url,
                   data: {
                     id: self.data.id
                   },
                   header: {
                     'content-type': 'application/json'
                   },
                   dataType:'json',
                   method: 'GET', 
                   success: function(res) {
                     if (res.data.code == 1) {
                       // 格式化文章内容
                       var article = res.data.data.content;
                       WxParse.wxParse('data', 'html', article, self);
                       
                       self.setData({
                         content: res.data.data
                       });
                     }
                   }
                 });
               } else {
                 wx.showModal({
                   showCancel: false,
                   content: res.data.msg
                 });
               }
             }
           });
         }
       }
     });
   },

   // 保存图片到相册
   saveImage: function(e) {
     const url = e.currentTarget.dataset.url;
     wx.showLoading({
       title: '保存中...',
       mask: true
     });
     
     wx.downloadFile({
       url: url,
       success: function(res) {
         if (res.statusCode === 200) {
           wx.saveImageToPhotosAlbum({
             filePath: res.tempFilePath,
             success: function() {
               wx.hideLoading();
               wx.showToast({
                 title: '保存成功',
                 icon: 'success'
               });
             },
             fail: function() {
               wx.hideLoading();
               wx.showToast({
                 title: '保存失败',
                 icon: 'none'
               });
             }
           });
         } else {
           wx.hideLoading();
           wx.showToast({
             title: '下载失败',
             icon: 'none'
           });
         }
       },
       fail: function() {
         wx.hideLoading();
         wx.showToast({
           title: '下载失败',
           icon: 'none'
         });
       }
     });
   },

   // 添加阻止触摸移动事件
   preventTouchMove: function() {
     return false;
   },

})