.home-container {
  min-height: 100vh;
  background-color: #f5f7fa;
}

/* 顶部蓝色区域 */
.header-section {
  background: linear-gradient(135deg, #4A90E2 0%, #357ABD 100%);
  height: 240rpx;
  position: relative;
  padding: 80rpx 40rpx 40rpx;
}

.header-content {
  color: white;
  text-align: left;
}

.welcome-text {
  font-size: 28rpx;
  opacity: 0.9;
  display: block;
  margin-bottom: 10rpx;
}

.app-title {
  font-size: 48rpx;
  font-weight: bold;
  display: block;
}

/* 搜索区域 */
.search-container {
  background: white;
  margin: -60rpx 30rpx 30rpx;
  border-radius: 20rpx;
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.1);
  padding: 30rpx;
  position: relative;
  z-index: 10;
}

.search-wrapper {
  width: 100%;
}

.search-box {
  display: flex;
  align-items: center;
  gap: 20rpx;
  margin-bottom: 30rpx;
}

.search-input-wrap {
  display: flex;
  align-items: center;
  background: #f8f9fa;
  border: 1rpx solid #e0e0e0;
  border-radius: 25rpx;
  padding: 0 20rpx;
  height: 80rpx;
  flex: 1;
}

.search-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 15rpx;
}

.search-input {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}

.clear-icon {
  width: 32rpx;
  height: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 10rpx;
}

.clear-icon image {
  width: 24rpx;
  height: 24rpx;
  opacity: 0.5;
}

.search-btn {
  background: linear-gradient(45deg, #4A90E2, #357ABD);
  color: #fff;
  height: 80rpx;
  width: 120rpx;
  border-radius: 25rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  font-weight: bold;
  transition: all 0.3s ease;
}

.search-btn:active {
  transform: scale(0.95);
  opacity: 0.8;
}

/* 快捷标签 */
.quick-tags {
  display: flex;
  gap: 10rpx;
  margin-bottom: 25rpx;
  flex-wrap: wrap;
  justify-content: flex-start;
}

.tag-item {
  padding: 8rpx 16rpx;
  background: #f0f2f5;
  border-radius: 16rpx;
  font-size: 20rpx;
  color: #666;
  border: 2rpx solid transparent;
  white-space: nowrap;
  transition: all 0.3s ease;
  flex-shrink: 0;
}

.tag-item.active {
  background: #e6f3ff;
  color: #4A90E2;
  border-color: #4A90E2;
}

.tag-item:active {
  transform: scale(0.95);
  opacity: 0.8;
}



/* 搜索结果提示 */
.search-result-tip {
  background: #f0f8ff;
  margin: 0 30rpx 20rpx;
  border-radius: 15rpx;
  padding: 20rpx 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-left: 4rpx solid #4A90E2;
}

.search-result-tip text {
  font-size: 26rpx;
  color: #4A90E2;
}

.back-to-all {
  font-size: 24rpx;
  color: #666;
  padding: 8rpx 16rpx;
  background: white;
  border-radius: 12rpx;
  border: 1rpx solid #ddd;
}

/* 加载状态 */
.loading-state {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 60rpx 0;
}

.loading-text {
  font-size: 26rpx;
  color: #999;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 0;
}

.empty-icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 30rpx;
  opacity: 0.3;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
  margin-bottom: 10rpx;
}

.empty-tip {
  font-size: 24rpx;
  color: #ccc;
}

/* 今日审批事项 */
.approval-section {
  background: white;
  margin: 0 30rpx 30rpx;
  border-radius: 20rpx;
  padding: 30rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 25rpx;
}

.approval-list {
  margin-bottom: 20rpx;
}

.approval-item {
  display: flex;
  align-items: center;
  padding: 25rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.approval-item:last-child {
  border-bottom: none;
}

.approval-avatar {
  width: 90rpx;
  height: 90rpx;
  border-radius: 45rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 25rpx;
}

.avatar-text {
  color: white;
  font-size: 28rpx;
  font-weight: bold;
}

.approval-info {
  flex: 1;
}

.approval-name {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.approval-type {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 10rpx;
}

.approval-tags {
  display: flex;
  gap: 10rpx;
  flex-wrap: wrap;
}

.tag {
  padding: 4rpx 12rpx;
  border-radius: 10rpx;
  font-size: 20rpx;
}

.tag.pending {
  background: #fff3cd;
  color: #856404;
}

.tag.urgent {
  background: #f8d7da;
  color: #721c24;
}

.tag.time {
  background: #d1ecf1;
  color: #0c5460;
}

/* 加载更多容器 */
.load-more-container {
  padding: 30rpx 0;
  text-align: center;
  border-top: 1rpx solid #f0f0f0;
}

.load-more-btn {
  background: linear-gradient(45deg, #4A90E2, #357ABD);
  color: white;
  padding: 20rpx 40rpx;
  border-radius: 25rpx;
  font-size: 28rpx;
  display: inline-block;
  transition: all 0.3s ease;
}

.load-more-btn:active {
  transform: scale(0.95);
  opacity: 0.8;
}

.loading-tip {
  color: #999;
  font-size: 26rpx;
  padding: 20rpx 0;
}

.no-more-tip {
  color: #ccc;
  font-size: 24rpx;
  padding: 20rpx 0;
}



/* 功能卡片区域 */
.function-cards {
  margin: 0 30rpx 30rpx;
}

.card-row {
  display: flex;
  gap: 20rpx;
}

.function-card {
  flex: 1;
  padding: 40rpx 30rpx;
  border-radius: 20rpx;
  color: white;
  position: relative;
  overflow: hidden;
}

.function-card.red {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
}

.function-card.orange {
  background: linear-gradient(135deg, #ffa726 0%, #ff9800 100%);
}

.card-icon {
  font-size: 48rpx;
  margin-bottom: 15rpx;
}

.card-title {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.card-subtitle {
  font-size: 24rpx;
  opacity: 0.9;
}

/* 新闻资讯 */
.news-section {
  background: white;
  margin: 0 30rpx 30rpx;
  border-radius: 20rpx;
  padding: 30rpx;
}

.news-list {
  margin-top: 25rpx;
}

.news-item {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.news-item:last-child {
  border-bottom: none;
}

.news-content {
  flex: 1;
  margin-right: 20rpx;
}

.news-title {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
  line-height: 1.4;
}

.news-meta {
  display: flex;
  gap: 15rpx;
  font-size: 22rpx;
  color: #999;
}

.news-image {
  width: 120rpx;
  height: 80rpx;
  border-radius: 8rpx;
  background: #f0f0f0;
}
