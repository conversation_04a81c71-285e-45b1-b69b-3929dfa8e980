const app = getApp()

Page({
  data: {
    member: null,
    exchangeList: [],
    historyList: [],
    myExchangeList: [], // 添加我的兑换列表
    page: 1,
    pageSize: 10,
    loading: false,
    activeTab: 'exchange' // 默认显示兑换商品标签页
  },

  onLoad: function() {
    this.initPage()
  },

  onShow: function() {
    // 每次页面显示时重新获取会员信息
    this.getMemberInfo()
  },

  onPullDownRefresh: function() {
    this.initPage()
    wx.stopPullDownRefresh()
  },

  // 初始化页面数据
  initPage: function() {
    wx.showLoading({
      title: '加载中...',
    })
    
    Promise.all([
      this.getMemberInfo(),
      this.getExchangeList(),
      this.getExchangeHistory(),
      this.getMyExchangeList() // 添加获取我的兑换列表
    ]).finally(() => {
      wx.hideLoading()
    })
  },

  // 获取会员信息
  getMemberInfo: function() {
    return new Promise((resolve) => {
      // 从本地存储获取会员信息
      const member_uid = wx.getStorageSync('member_uid')
      const member_auth = wx.getStorageSync('member_auth')
      
      if (!member_uid || !member_auth) {
        wx.showToast({
          title: '请先登录',
          icon: 'none',
          duration: 2000
        })
        resolve(false)
        return
      }

      // 获取详细信息
      wx.request({
        url: app.globalData.http_api,
        method: 'GET',
        data: {
          s: 'member',
          c: 'home',
          m: 'index',
          api_auth_code: member_auth,
          api_auth_uid: member_uid
        },
        success: (res) => {
          console.log('会员信息响应：', res)
          if (res.data && res.data.code == 1) {
            // 更新会员信息
            const memberData = res.data.data
            // 保存到缓存
            wx.setStorageSync('member', memberData)
            // 更新页面数据
            this.setData({
              member: memberData
            })
            resolve(true)
          } else {
            this.showError(res.data ? res.data.msg : '获取会员信息失败')
            resolve(false)
          }
        },
        fail: (err) => {
          console.error('获取会员信息失败：', err)
          this.showError('网络请求失败')
          resolve(false)
        }
      })
    })
  },

  // 获取可兑换商品列表
  getExchangeList: function() {
    return new Promise((resolve) => {
      const { page, pageSize } = this.data
      
      if (this.data.loading) {
        resolve(false)
        return
      }
      
      this.setData({ loading: true })
      
      wx.request({
        url: app.globalData.http_api,
        method: 'GET',
        data: {
          s: 'jifen',
          c: 'search',
          api_call_function: 'module_list',
          more: 1,
          page: page,
          pagesize: pageSize
        },
        success: (res) => {
          console.log('API响应：', res)
          if (res.data && res.data.data) {
            // 处理返回的数据
            const exchangeList = res.data.data.map(item => this.processExchangeItem(item))
            this.setData({
              exchangeList: exchangeList
            })
            resolve(true)
          } else {
            this.showError(res.data ? res.data.msg : '获取数据失败')
            resolve(false)
          }
        },
        fail: (err) => {
          console.error('请求失败：', err)
          this.showError('网络请求失败')
          resolve(false)
        },
        complete: () => {
          this.setData({ loading: false })
        }
      })
    })
  },
  
  // 处理商品数据
  processExchangeItem: function(item) {
    // 解析price_sku字符串为对象
    let priceSku = {}
    if (item.price_sku) {
      try {
        // 尝试直接解析
        priceSku = JSON.parse(item.price_sku)
      } catch (e) {
        console.log('直接解析失败，尝试修复JSON格式：', item.price_sku)
        try {
          // 尝试修复JSON格式
          const fixedJson = item.price_sku
            .replace(/'/g, '"')  // 替换单引号为双引号
            .replace(/(\w+):/g, '"$1":')  // 给键名添加双引号
          priceSku = JSON.parse(fixedJson)
        } catch (e2) {
          console.error('JSON解析失败：', e2)
        }
      }
    }

    return {
      id: item.id,
      title: item.title,
      thumb: item.thumb,
      description: item.description,
      price: item.price,
      priceSku: priceSku,
      stock: item.quantity,
      // 添加一些计算属性
      hasStock: parseInt(item.quantity) > 0,
      selectedSku: null, // 用户选择的规格
      selectedPrice: item.price // 默认使用基础价格
    }
  },

  // 选择规格
  selectSku: function(e) {
    const { index, sku } = e.currentTarget.dataset
    const exchangeList = [...this.data.exchangeList]
    const item = exchangeList[index]
    
    if (!item) return
    
    item.selectedSku = sku
    item.selectedPrice = item.priceSku[sku] || item.price
    
    this.setData({
      exchangeList: exchangeList
    })
  },

  // 获取兑换记录
  getExchangeHistory: function() {
    return new Promise((resolve) => {
      const { page, pageSize } = this.data
      const member_uid = wx.getStorageSync('member_uid')
      const member_auth = wx.getStorageSync('member_auth')
      
      if (!member_uid || !member_auth) {
        resolve(false)
        return
      }

      const requestData = {
        s: 'httpapi',
        id: 8,
        appid: app.globalData.appid || '',
        appsecret: app.globalData.appsecret || '',
        page: page,
        pagesize: pageSize,
        api_auth_code: member_auth,
        api_auth_uid: member_uid
      }

      console.log('历史记录请求参数：', requestData)
      
      wx.request({
        url: app.globalData.http_api,
        method: 'GET',
        data: requestData,
        success: (res) => {
          console.log('历史记录API响应：', res)
          if (res.data && res.data.data) {
            // 处理新的数据格式，根据数据表结构解析数据
            const historyList = res.data.data.map(item => {
              return {
                id: item.id,
                uid: item.uid,
                name: item.name || '', // 兑换人
                cid: item.cid || '', // 商品ID
                title: item.title || '', // 商品标题
                sellid: item.sellid || '', // 作者ID
                price: item.price || '0.00', // 售价
                inputtime: item.inputtime ? this.formatTime(item.inputtime) : '' // 格式化时间戳
              }
            })
            this.setData({
              historyList: historyList
            })
            resolve(true)
          } else {
            this.showError(res.data ? res.data.msg : '获取历史记录失败')
            resolve(false)
          }
        },
        fail: (err) => {
          console.error('历史记录请求失败：', err)
          this.showError('网络请求失败')
          resolve(false)
        }
      })
    })
  },

  // 获取我的兑换列表
  getMyExchangeList: function() {
    return new Promise((resolve) => {
      const member_uid = wx.getStorageSync('member_uid')
      const member_auth = wx.getStorageSync('member_auth')
      
      if (!member_uid || !member_auth) {
        resolve(false)
        return
      }

      const requestData = {
        s: 'httpapi',
        id: 8,
        appid: app.globalData.appid || '',
        appsecret: app.globalData.appsecret || '',
        page: 1,
        pagesize: 100, // 获取足够多的数据，以便前端过滤
        api_auth_code: member_auth,
        api_auth_uid: member_uid
      }

      console.log('我的兑换请求参数：', requestData)
      
      wx.request({
        url: app.globalData.http_api,
        method: 'GET',
        data: requestData,
        success: (res) => {
          console.log('我的兑换API响应：', res)
          if (res.data && res.data.data) {
            // 在前端过滤当前用户的数据
            const allData = res.data.data;
            const filteredData = allData.filter(item => item.uid === member_uid);
            
            console.log('过滤后的我的兑换数据：', filteredData);
            
            // 处理数据
            const myExchangeList = filteredData.map(item => {
              return {
                id: item.id,
                uid: item.uid,
                name: item.name || '',
                cid: item.cid || '',
                title: item.title || '',
                sellid: item.sellid || '',
                price: item.price || '0.00',
                inputtime: item.inputtime ? this.formatTime(item.inputtime) : '',
                status: item.status || '0',
                statusText: this.getStatusText(item.status)
              }
            })
            
            this.setData({
              myExchangeList: myExchangeList
            })
            
            resolve(true)
          } else {
            // API未返回数据
            this.setData({
              myExchangeList: []
            })
            resolve(false)
          }
        },
        fail: (err) => {
          console.error('获取我的兑换列表失败：', err)
          this.showError('网络请求失败')
          this.setData({
            myExchangeList: []
          })
          resolve(false)
        }
      })
    })
  },
  
  // 获取状态文本
  getStatusText: function(status) {
    const statusMap = {
      '0': '处理中',
      '1': '已完成',
      '2': '已取消',
      '3': '配送中'
    }
    return statusMap[status] || '未知状态'
  },

  // 处理兑换
  handleExchange: function(e) {
    const item = e.currentTarget.dataset.item
    console.log('准备兑换的商品：', item)
    
    const member_uid = wx.getStorageSync('member_uid')
    const member_auth = wx.getStorageSync('member_auth')
    
    if (!member_uid || !member_auth) {
      this.showError('请先登录')
      return
    }

    if (!this.data.member) {
      this.showError('会员信息获取失败，请重新登录')
      return
    }

    if (!item.hasStock) {
      this.showError('商品库存不足')
      return
    }

    // 检查积分是否足够
    if (this.data.member.score < item.selectedPrice) {
      this.showError('积分不足')
      return
    }

    wx.showModal({
      title: '确认兑换',
      content: `确定要使用${item.selectedPrice}积分兑换${item.title}吗？`,
      success: (res) => {
        if (res.confirm) {
          this.doExchange(item)
        }
      }
    })
  },

  // 执行兑换
  doExchange: function(item) {
    if (this.data.loading) return
    
    this.setData({ loading: true })
    wx.showLoading({
      title: '兑换中...',
    })
    
    console.log('开始兑换，商品信息：', item)
    
    const member_uid = wx.getStorageSync('member_uid')
    const member_auth = wx.getStorageSync('member_auth')
    
    if (!member_uid || !member_auth) {
      this.showError('请先登录')
      this.setData({ loading: false })
      wx.hideLoading()
      return
    }

    if (!item || !item.id) {
      console.log('商品信息不完整，缺少ID：', item)
      this.showError('商品信息不完整')
      this.setData({ loading: false })
      wx.hideLoading()
      return
    }

    // 构建请求完整URL而不是通过data参数
    const requestUrl = app.globalData.http_api + 
      "s=jifen&c=buy&m=index&id=" + item.id + 
      "&api_auth_code=" + member_auth + 
      "&api_auth_uid=" + member_uid

    console.log('兑换请求URL：', requestUrl)
    
    wx.request({
      url: requestUrl,
      method: 'POST',
      data: {
        sku: item.selectedSku || ''
      },
      header: {
        'content-type': 'application/x-www-form-urlencoded'
      },
      success: (res) => {
        console.log('兑换API响应：', res)
        wx.hideLoading()
        
        if (res.data && res.data.code === 1) {
          // 兑换成功，刷新数据
          wx.showToast({
            title: '兑换成功',
            icon: 'success'
          })
          
          // 刷新数据
          setTimeout(() => {
            Promise.all([
              this.getMemberInfo(),
              this.getExchangeList(),
              this.getExchangeHistory(),
              this.getMyExchangeList()
            ])
          }, 1500)
        } else {
          let errorMsg = '兑换失败'
          if (res.data) {
            if (res.data.msg === "禁止提交，请检查提交地址是否有误") {
              errorMsg = '系统维护中，请稍后再试'
              // 可能需要检查接口地址或参数
              console.error('API请求地址可能错误:', requestUrl)
            } else {
              errorMsg = res.data.msg || errorMsg
            }
          }
          this.showError(errorMsg)
        }
      },
      fail: (err) => {
        console.error('兑换请求失败：', err)
        wx.hideLoading()
        this.showError('网络请求失败，请检查网络连接')
      },
      complete: () => {
        this.setData({ loading: false })
      }
    })
  },
  
  // 统一错误提示
  showError: function(msg) {
    wx.showToast({
      title: msg,
      icon: 'none',
      duration: 2000
    })
  },

  // 格式化时间戳为可读日期
  formatTime: function(timestamp) {
    if (!timestamp) return '';
    
    const date = new Date(timestamp * 1000);
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    const hour = date.getHours().toString().padStart(2, '0');
    const minute = date.getMinutes().toString().padStart(2, '0');
    
    return `${year}-${month}-${day} ${hour}:${minute}`;
  },

  // 切换标签页
  switchTab: function(e) {
    const tab = e.currentTarget.dataset.tab
    this.setData({
      activeTab: tab
    })
    
    // 如果切换到历史记录标签，且历史记录为空，则刷新数据
    if (tab === 'history' && this.data.historyList.length === 0) {
      this.getExchangeHistory()
    }

    // 如果切换到我的兑换标签页，刷新数据
    if (tab === 'my-exchange') {
      this.getMyExchangeList()
    }
  }
}) 