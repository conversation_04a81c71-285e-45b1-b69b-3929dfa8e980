<view class="container">
  <view class="recharge-card">
    <view class="card-header">
      <text class="card-title">账户充值</text>
      <text class="card-subtitle">请选择充值金额或输入自定义金额</text>
    </view>

    <!-- 快捷金额选择 -->
    <view class="amount-options">
      <view class="amount-grid">
        <view class="amount-item {{selectedAmount === 50 ? 'active' : ''}}" bindtap="selectAmount" data-amount="50">
          <text class="amount-value">50</text>
          <text class="amount-unit">元</text>
        </view>
        <view class="amount-item {{selectedAmount === 100 ? 'active' : ''}}" bindtap="selectAmount" data-amount="100">
          <text class="amount-value">100</text>
          <text class="amount-unit">元</text>
        </view>
        <view class="amount-item {{selectedAmount === 200 ? 'active' : ''}}" bindtap="selectAmount" data-amount="200">
          <text class="amount-value">200</text>
          <text class="amount-unit">元</text>
        </view>
        <view class="amount-item {{selectedAmount === 500 ? 'active' : ''}}" bindtap="selectAmount" data-amount="500">
          <text class="amount-value">500</text>
          <text class="amount-unit">元</text>
        </view>
      </view>
    </view>

    <!-- 自定义金额输入 -->
    <view class="custom-amount">
      <view class="input-label">自定义金额</view>
      <view class="input-wrap">
        <text class="currency-symbol">¥</text>
        <input 
          class="amount-input" 
          type="digit" 
          placeholder="请输入充值金额" 
          placeholder-class="input-placeholder"
          value="{{customAmount}}"
          bindinput="onCustomAmountInput"
          focus="{{!selectedAmount}}"
        />
      </view>
    </view>

    <!-- 支付方式选择 -->
    <view class="payment-method">
      <view class="method-header">
        <text class="method-title">支付方式</text>
      </view>
      <view class="method-item">
        <view class="method-info">
          <image class="method-icon" src="../../icons/wechat-pay.png" mode="aspectFit"></image>
          <text class="method-name">微信支付</text>
        </view>
        <view class="method-check">
          <image class="check-icon" src="../../icons/check.png" mode="aspectFit"></image>
        </view>
      </view>
    </view>

    <!-- 充值按钮 -->
    <form bindsubmit="formBindsubmit">
      <button class="recharge-btn" formType="submit">
        <text class="btn-text">立即充值</text>
        <text class="btn-amount" wx:if="{{selectedAmount || customAmount}}">¥{{selectedAmount || customAmount}}</text>
      </button>
    </form>

    <!-- 充值说明 -->
    <view class="recharge-tips">
      <view class="tips-item">
        <image class="tips-icon" src="../../icons/info.png" mode="aspectFit"></image>
        <text class="tips-text">充值金额最低10元，最高10000元</text>
      </view>
      <view class="tips-item">
        <image class="tips-icon" src="../../icons/info.png" mode="aspectFit"></image>
        <text class="tips-text">充值成功后，余额将立即到账</text>
      </view>
    </view>
  </view>
</view>







