var app = getApp();

/**
 * 财务管理页面
 */
Page({
  /**
   * 页面的初始数据
   */
  data: {
    isAdmin: false,          // 是否为管理员
    isLoggedIn: false,       // 是否已登录
    currentTab: 0,           // 当前标签页：0-收入，1-支出
    
    // 数据列表
    incomeData: [],          // 收入数据列表
    expenseData: [],         // 支出数据列表
    
    // 分页相关
    loading: false,          // 是否正在加载数据
    page: 1,                 // 当前页码
    limit: 10,               // 每页数据条数
    hasMore: true,           // 是否有更多数据
    
    // 统计数据
    initialBalance: 15915.78, // 期初值
    totalIncome: 0,          // 总收入
    totalExpense: 0,         // 总支出
    balance: 0,              // 当期结余(不含期初值)
    
    // 筛选相关
    categoryList: [],        // 年度栏目列表
    selectedCategory: 0,     // 当前选择的栏目ID（0表示全部）
    selectedCategoryName: '全部年度', // 当前选择的栏目名称
    showFilterPopup: false,  // 是否显示筛选弹窗
    
    // 搜索相关
    searchKeyword: '',       // 搜索关键词
    isSearching: false       // 是否处于搜索状态
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function(options) {
    this.checkAdminPermission();
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function() {
    this.checkAdminPermission();
    if (this.data.isAdmin) {
      // 初始化数据
      this.resetPageData();
      
      // 获取筛选条件
      this.getCategoryList();
      
      // 获取统计数据
      this.getFinancialStats();
    }
    
    // 设置当前选中的tabbar项
    this.setTabBarSelected();
  },
  
  /**
   * 设置TabBar选中状态
   */
  setTabBarSelected: function() {
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().setData({
        selected: 3  // 财务页面对应的索引
      });
    }
  },

  /**
   * 获取年度栏目列表
   */
  getCategoryList: function() {
    wx.showLoading({
      title: '加载筛选项...',
    });
    
    // 获取用户认证信息
    const authInfo = this.getAuthInfo();
    
    // 请求栏目数据
    wx.request({
      url: app.globalData.http_api,
      method: 'GET',
      data: {
        s: 'httpapi',
        m: 'category',
        mid: 'caiwu',
        pid: 0, // 获取顶级栏目（年度）
        ...authInfo
      },
      success: (res) => {
        if (res.data && res.data.code == 1) {
          // 添加"全部"选项
          const categoryList = [{ id: 0, name: '全部年度' }];
          
          // 处理返回的栏目数据 - 数据是对象格式，需要转换为数组
          if (res.data.data && typeof res.data.data === 'object') {
            // 将对象转换为数组
            const dataArray = Object.values(res.data.data);
            if (dataArray.length > 0) {
              categoryList.push(...dataArray);
            }
          }
          
          this.setData({ categoryList });
          
          // 如果有选中的分类，更新分类名称
          if (this.data.selectedCategory > 0) {
            const selectedItem = categoryList.find(item => parseInt(item.id) === parseInt(this.data.selectedCategory));
            if (selectedItem) {
              this.setData({ selectedCategoryName: selectedItem.name });
            }
          }
        } else {
          wx.showToast({
            title: res.data.msg || '获取筛选选项失败',
            icon: 'none'
          });
        }
      },
      fail: (err) => {
        console.error('请求栏目数据失败:', err);
      },
      complete: () => {
        wx.hideLoading();
      }
    });
  },
  
  /**
   * 切换筛选弹窗显示状态
   */
  toggleFilterPopup: function() {
    this.setData({
      showFilterPopup: !this.data.showFilterPopup
    });
  },
  
  /**
   * 阻止触摸穿透
   */
  preventTouchMove: function() {
    // 阻止触摸事件穿透
    return false;
  },

  /**
   * 选择筛选条件
   */
  selectCategory: function(e) {
    const categoryId = parseInt(e.currentTarget.dataset.id) || 0;
    const categoryName = e.currentTarget.dataset.name || '全部年度';
    
    // 更新选中的栏目
    this.setData({
      selectedCategory: categoryId,
      selectedCategoryName: categoryName,
      showFilterPopup: false // 关闭弹窗
    });
    
    // 重置数据并重新加载
    this.resetPageData();
    this.getFinancialStats();
  },

  /**
   * 重置页面数据
   */
  resetPageData: function() {
    this.setData({
      currentTab: 0,  // 默认显示收入标签
      page: 1,
      hasMore: true,
      incomeData: [],
      expenseData: [],
      loading: false
    });
  },

  /**
   * 检查管理员权限
   */
  checkAdminPermission: function() {
    try {
      // 获取当前用户信息
      const member = wx.getStorageSync('member');
      const member_auth = wx.getStorageSync('member_auth');
      const member_uid = wx.getStorageSync('member_uid');

      // 如果未登录，直接跳转到登录页面
      if (!member || !member_auth || !member_uid || !member.id) {
        wx.reLaunch({
          url: '/pages/login/login'
        });
        return;
      }

      // 判断是否是管理员，is_admin > 0 表示是管理员
      const isAdmin = member.is_admin && parseInt(member.is_admin) > 0;
      this.setData({
        isAdmin: isAdmin,
        isLoggedIn: true
      });

      // 如果不是管理员，提示无权访问并返回
      if (!isAdmin) {
        this.showNoPermissionTip();
      }
    } catch (err) {
      console.error('权限检查错误:', err);
      wx.showToast({
        title: '系统错误，请重试',
        icon: 'none'
      });
    }
  },

  /**
   * 显示无权限提示
   */
  showNoPermissionTip: function() {
    wx.showModal({
      title: '无权访问',
      content: '只有管理员才能访问财务页面',
      showCancel: false,
      success: (res) => {
        wx.switchTab({
          url: '/pages/home/<USER>'
        });
      }
    });
  },
  
  /**
   * 显示登录提示
   */
  showLoginTip: function() {
    wx.showModal({
      title: '请先登录',
      content: '您需要登录并拥有管理员权限才能访问财务页面',
      showCancel: false,
      success: (res) => {
        wx.navigateTo({
          url: '/pages/login/login'
        });
      }
    });
  },

  /**
   * 获取财务统计数据
   */
  getFinancialStats: function() {
    wx.showLoading({
      title: '加载中...',
      mask: true
    });
    
    // 获取用户认证信息
    const authInfo = this.getAuthInfo();
    
    // 构建请求参数
    const requestData = {
      s: 'caiwu',
      c: 'search',
      api_call_function: 'module_list',
      pagesize: 999, // 获取所有数据用于统计
      ...authInfo
    };
    
    // 添加栏目ID筛选条件（如果不是"全部"）
    if (this.data.selectedCategory > 0) {
      requestData.catid = this.data.selectedCategory;
    }
    
    // 清除之前可能存在的加载状态
    this.setData({ loading: true });
    
    // 请求全部数据用于统计
    wx.request({
      url: app.globalData.http_api,
      method: 'GET',
      data: requestData,
      success: (res) => {
        if (res.data && res.data.code == 1) {
          // 计算统计数据
          this.calculateStats(res.data.data || []);
          
          // 同时从统计数据中提取首页数据
          this.extractInitialData(res.data.data || []);
        } else {
          this.handleApiError(res, '加载统计数据失败');
          // 如果统计加载失败，尝试常规方式加载列表
          this.loadTabData(0);
        }
      },
      fail: (err) => {
        console.error('获取统计数据失败:', err);
        // 如果请求失败，尝试常规方式加载列表
        this.loadTabData(0);
      },
      complete: () => {
        // 确保无论如何都隐藏loading并重置状态
        wx.hideLoading();
        this.setData({ loading: false });
      }
    });
  },
  
  /**
   * 计算财务统计数据
   */
  calculateStats: function(data) {
    let totalIncome = 0;
    let totalExpense = 0;
    
    data.forEach(item => {
      // 安全解析金额，确保是有效数字
      let amount = 0;
      try {
        amount = parseFloat(item.money || 0);
        if (isNaN(amount)) amount = 0;
      } catch (e) {
        amount = 0;
      }
      
      if (item.szxm === "收入") {
        totalIncome += amount;
      } else if (item.szxm === "支出") {
        totalExpense += amount;
      }
    });
    
    const balance = totalIncome - totalExpense;
    
    // 不使用 toFixed 来避免数据类型问题
    this.setData({
      totalIncome: Math.round(totalIncome * 100) / 100,
      totalExpense: Math.round(totalExpense * 100) / 100,
      balance: Math.round(balance * 100) / 100
    });
  },

  /**
   * 从统计数据中提取初始显示数据
   */
  extractInitialData: function(allData) {
    if (!allData || allData.length === 0) {
      // 如果没有数据，使用常规方式加载
      this.loadTabData(0);
      return;
    }
    
    // 确保每个记录都有唯一ID
    allData = allData.map((item, index) => {
      if (!item.id) {
        item.id = 'temp_' + Date.now() + '_' + index;
      } else {
        // 确保ID是字符串类型
        item.id = String(item.id);
      }
      return item;
    });
    
    // 提取收入数据
    const incomeData = allData.filter(item => item.szxm === "收入");
    
    // 提取支出数据
    const expenseData = allData.filter(item => item.szxm === "支出");
    
    // 去除重复ID
    const uniqueIncomeData = this.removeDuplicateIds(incomeData);
    const uniqueExpenseData = this.removeDuplicateIds(expenseData);
    
    // 分页处理收入数据
    const initialIncomeData = uniqueIncomeData.slice(0, this.data.limit);
    const hasMoreIncome = uniqueIncomeData.length > this.data.limit;
    
    // 分页处理支出数据
    const initialExpenseData = uniqueExpenseData.slice(0, this.data.limit);
    const hasMoreExpense = uniqueExpenseData.length > this.data.limit;
    
    // 更新数据
    this.setData({
      incomeData: initialIncomeData,
      expenseData: initialExpenseData,
      hasMore: this.data.currentTab === 0 ? hasMoreIncome : hasMoreExpense,
      page: 2 // 已加载第一页
    });
  },
  
  /**
   * 移除重复ID的记录，保留第一个出现的记录
   */
  removeDuplicateIds: function(data) {
    const seen = new Set();
    return data.filter(item => {
      const id = item.id;
      if (seen.has(id)) {
        return false; // 已存在，过滤掉
      }
      seen.add(id);
      return true; // 第一次出现，保留
    });
  },

  /**
   * 加载特定标签的数据
   */
  loadTabData: function(tabIndex) {
    this.setData({
      currentTab: tabIndex,
      page: 1,
      hasMore: true,
      loading: false,
      incomeData: tabIndex === 0 ? [] : this.data.incomeData,
      expenseData: tabIndex === 1 ? [] : this.data.expenseData
    });
    
    // 加载数据
    this.loadFinancialData();
  },

  /**
   * 加载财务数据列表
   */
  loadFinancialData: function() {
    if (this.data.loading || !this.data.hasMore) return;
    
    this.setData({ loading: true });
    
    // 设置超时保护，确保加载状态不会永远停留
    const loadingTimeout = setTimeout(() => {
      if (this.data.loading) {
        this.setData({ loading: false });
      }
    }, 10000); // 10秒超时保护
    
    // 获取用户认证信息
    const authInfo = this.getAuthInfo();
    
    // 构建请求参数
    const requestData = {
      s: 'caiwu',
      c: 'search',
      api_call_function: 'module_list',
      more: 1,
      page: this.data.page,
      pagesize: this.data.limit,
      type: this.data.currentTab,
      ...authInfo
    };
    
    // 添加栏目ID筛选条件（如果不是"全部"）
    if (this.data.selectedCategory > 0) {
      requestData.catid = this.data.selectedCategory;
    }
    
    // 添加搜索关键词（如果有）
    if (this.data.isSearching && this.data.searchKeyword) {
      requestData.keyword = this.data.searchKeyword.trim();
    }
    
    // 请求财务数据
    wx.request({
      url: app.globalData.http_api,
      method: 'GET',
      data: requestData,
      success: (res) => {
        if (res.data && res.data.code == 1) {
          const newData = res.data.data || [];
          
          // 使用 nextTick 推迟到下一个渲染周期，减轻当前渲染压力
          wx.nextTick(() => {
            this.appendTabData(newData);
          });
        } else {
          this.handleApiError(res, '加载数据失败');
          this.setData({ hasMore: false });
        }
      },
      fail: (err) => {
        console.error('请求财务数据失败:', err);
        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        });
        this.setData({ hasMore: false });
      },
      complete: () => {
        // 清除超时保护
        clearTimeout(loadingTimeout);
        
        // 确保无论如何都重置loading状态
        wx.nextTick(() => {
          this.setData({ loading: false });
          wx.stopPullDownRefresh();
        });
      }
    });
  },

  /**
   * 将新数据追加到当前标签
   */
  appendTabData: function(newData) {
    // 筛选数据
    let filteredData = this.filterDataByType(newData);
    
    // 确保每个记录都有唯一ID
    filteredData = filteredData.map((item, index) => {
      // 如果没有id，设置一个临时唯一id
      if (!item.id) {
        item.id = 'temp_' + Date.now() + '_' + index;
      } else {
        // 确保ID是字符串类型
        item.id = String(item.id);
      }
      return item;
    });
    
    const hasMore = newData.length >= this.data.limit;
    
    if (this.data.currentTab == 0) {
      // 收入数据
      const currentIncomeData = this.data.incomeData || [];
      
      // 检查现有数据中已存在的ID
      const existingIds = new Set(currentIncomeData.map(item => item.id));
      
      // 过滤掉重复ID的记录
      const uniqueNewData = filteredData.filter(item => !existingIds.has(item.id));
      
      // 直接更新状态数据，不进行额外的计算
      this.setData({
        incomeData: [...currentIncomeData, ...uniqueNewData],
        hasMore: hasMore,
        page: this.data.page + 1
      });
      
      // 如果在搜索状态下加载了更多数据，显示更新后的结果数量
      if (this.data.isSearching && this.data.searchKeyword && uniqueNewData.length > 0) {
        wx.showToast({
          title: `收入记录共${currentIncomeData.length + uniqueNewData.length}条`,
          icon: 'none',
          duration: 1500
        });
      }
      
    } else {
      // 支出数据
      const currentExpenseData = this.data.expenseData || [];
      
      // 检查现有数据中已存在的ID
      const existingIds = new Set(currentExpenseData.map(item => item.id));
      
      // 过滤掉重复ID的记录
      const uniqueNewData = filteredData.filter(item => !existingIds.has(item.id));
      
      // 直接更新状态数据，不进行额外的计算
      this.setData({
        expenseData: [...currentExpenseData, ...uniqueNewData],
        hasMore: hasMore,
        page: this.data.page + 1
      });
      
      // 如果在搜索状态下加载了更多数据，显示更新后的结果数量
      if (this.data.isSearching && this.data.searchKeyword && uniqueNewData.length > 0) {
        wx.showToast({
          title: `支出记录共${currentExpenseData.length + uniqueNewData.length}条`,
          icon: 'none',
          duration: 1500
        });
      }
    }
  },

  /**
   * 根据类型筛选数据
   */
  filterDataByType: function(data) {
    const result = data.filter(item => {
      if (this.data.currentTab === 0) {
        // 收入数据
        return item.szxm === "收入";
      } else {
        // 支出数据
        return item.szxm === "支出";
      }
    });
    
    return result;
  },
  
  /**
   * 获取用户认证信息
   */
  getAuthInfo: function() {
    return {
      member_auth: wx.getStorageSync('member_auth'),
      member_uid: wx.getStorageSync('member_uid')
    };
  },
  
  /**
   * 处理API错误
   */
  handleApiError: function(res, defaultMsg) {
    wx.showToast({
      title: res.data.msg || defaultMsg,
      icon: 'none'
    });
  },

  /**
   * 切换标签页
   */
  switchTab: function(e) {
    const tabIndex = parseInt(e.currentTarget.dataset.index);
    
    // 如果已经是当前标签，不重复加载
    if (this.data.currentTab === tabIndex) return;
    
    // 如果处于搜索状态，只切换标签显示当前标签的搜索结果
    if (this.data.isSearching && this.data.searchKeyword) {
      this.setData({ currentTab: tabIndex });
      return;
    }
    
    // 如果当前标签没有数据，则加载数据
    if (
      (tabIndex === 0 && (!this.data.incomeData || this.data.incomeData.length === 0)) ||
      (tabIndex === 1 && (!this.data.expenseData || this.data.expenseData.length === 0))
    ) {
      this.loadTabData(tabIndex);
    } else {
      // 如果已有数据，只切换标签
    this.setData({
      currentTab: tabIndex,
        hasMore: tabIndex === 0 ? 
          this.data.incomeData.length >= this.data.limit : 
          this.data.expenseData.length >= this.data.limit 
      });
    }
  },

  /**
   * 下拉刷新
   */
  onPullDownRefresh: function() {
    if (!this.data.isAdmin) {
      wx.stopPullDownRefresh();
      return;
    }
    
    // 如果处于搜索状态，刷新搜索结果
    if (this.data.isSearching && this.data.searchKeyword) {
      this.searchFinancialData();
    } else {
      // 正常刷新当前标签的数据
      this.loadTabData(this.data.currentTab);
    }
    
    wx.stopPullDownRefresh();
  },

  /**
   * 上拉加载更多
   */
  onReachBottom: function() {
    if (!this.data.isAdmin || !this.data.hasMore || this.data.loading) return;

    // 设置节流，防止多次快速触发
    if (this.loadMoreTimer) {
      clearTimeout(this.loadMoreTimer);
    }
    
    this.loadMoreTimer = setTimeout(() => {
      // 正常加载更多数据
      this.loadFinancialData();
    }, 300); // 增加延迟，减少频繁触发
  },

  /**
   * 添加新的财务记录
   */
  addFinancialRecord: function() {
    wx.navigateTo({
      url: '/pages/caiwu/add'
    });
  },

  /**
   * 查看详情
   */
  viewDetail: function(e) {
    const id = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: '/pages/caiwu/detail?id=' + id
    });
  },

  /**
   * 格式化金额，保留两位小数
   */
  formatMoney: function(value) {
    try {
      const num = parseFloat(value);
      if (isNaN(num)) return '0.00';
      return num.toFixed(2);
    } catch (e) {
      return '0.00';
    }
  },

  /**
   * 搜索输入监听
   */
  onSearchInput: function(e) {
    this.setData({
      searchKeyword: e.detail.value
    });
    
    // 如果清空了搜索框且当前处于搜索状态，重置搜索并刷新数据
    if (!e.detail.value && this.data.isSearching) {
      this.resetSearch();
    }
  },
  
  /**
   * 搜索确认
   */
  onSearchConfirm: function() {
    const keyword = this.data.searchKeyword.trim();
    
    // 如果关键词为空，且当前已在搜索状态，重置搜索
    if (!keyword && this.data.isSearching) {
      this.resetSearch();
      return;
    }
    
    // 如果关键词为空，不执行搜索
    if (!keyword) return;
    
    // 设置搜索状态并重置页面
    this.setData({
      isSearching: true,
      page: 1,
      hasMore: true,
      incomeData: [],
      expenseData: []
    });
    
    // 执行搜索
    this.searchFinancialData();
  },
  
  /**
   * 清除搜索
   */
  clearSearch: function() {
    this.setData({
      searchKeyword: ''
    });
    
    // 如果当前处于搜索状态，重置搜索
    if (this.data.isSearching) {
      this.resetSearch();
    }
  },
  
  /**
   * 重置搜索状态并刷新数据
   */
  resetSearch: function() {
    this.setData({
      isSearching: false,
      searchKeyword: ''
    });
    
    // 重置页面数据
    this.resetPageData();
    
    // 重新获取数据
    this.getFinancialStats();
  },
  
  /**
   * 执行搜索
   */
  searchFinancialData: function() {
    const keyword = this.data.searchKeyword.trim();
    if (!keyword) return;
    
    wx.showLoading({
      title: '搜索中...',
      mask: true
    });
    
    // 设置加载状态
    this.setData({ loading: true });
    
    // 设置超时保护
    const searchTimeout = setTimeout(() => {
      if (this.data.loading) {
        this.setData({ loading: false });
        wx.hideLoading();
      }
    }, 15000); // 15秒超时保护
    
    // 重置页码
    this.setData({ page: 1 });
    
    // 获取用户认证信息
    const authInfo = this.getAuthInfo();
    
    // 使用混合搜索策略：先从API获取数据，然后在本地再次筛选，确保结果准确性
    wx.request({
      url: app.globalData.http_api,
      method: 'GET',
      data: {
        s: 'caiwu',
        c: 'search',
        api_call_function: 'module_list',
        pagesize: 500, // 获取足够多的数据供本地筛选
        keyword: keyword,
        ...authInfo,
        ...(this.data.selectedCategory > 0 ? { catid: this.data.selectedCategory } : {})
      },
      success: (res) => {
        if (res.data && res.data.code == 1) {
          const allData = res.data.data || [];
          
          // 在本地再次进行关键词筛选，确保精确度
          this.performLocalSearchFiltering(allData, keyword);
        } else {
          this.handleApiError(res, '搜索失败');
          this.setData({ hasMore: false });
        }
      },
      fail: (err) => {
        console.error('搜索请求失败:', err);
        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        });
      },
      complete: () => {
        // 清除超时保护
        clearTimeout(searchTimeout);
        
        // 确保无论如何都重置loading状态
        this.setData({ loading: false });
        wx.hideLoading();
      }
    });
  },
  
  /**
   * 执行本地搜索过滤
   */
  performLocalSearchFiltering: function(allData, keyword) {
    if (!allData || allData.length === 0) {
      this.setData({
        incomeData: [],
        expenseData: [],
        hasMore: false
      });
      wx.showToast({
        title: '未找到相关记录',
        icon: 'none'
      });
      return;
    }
    
    // 确保每个记录都有唯一ID
    allData = allData.map((item, index) => {
      if (!item.id) {
        item.id = 'temp_' + Date.now() + '_' + index;
      } else {
        // 确保ID是字符串类型
        item.id = String(item.id);
      }
      return item;
    });
    
    // 忽略大小写的搜索
    const lowerKeyword = keyword.toLowerCase();
    
    // 多字段精确筛选
    const filteredData = allData.filter(item => {
      // 增强搜索字段和精度
      return (
        // 标题包含关键词
        (item.title && item.title.toLowerCase().indexOf(lowerKeyword) !== -1) ||
        // 栏目名称包含关键词
        (item.catname && item.catname.toLowerCase().indexOf(lowerKeyword) !== -1) ||
        // 收入类别包含关键词
        (item.shouruleibie && item.shouruleibie.toLowerCase().indexOf(lowerKeyword) !== -1) ||
        // 支出类别包含关键词
        (item.zhichuleibie && item.zhichuleibie.toLowerCase().indexOf(lowerKeyword) !== -1) ||
        // 收款方式包含关键词
        (item.skfs && item.skfs.toLowerCase().indexOf(lowerKeyword) !== -1) ||
        // 付款方式包含关键词
        (item.fkfs && item.fkfs.toLowerCase().indexOf(lowerKeyword) !== -1) ||
        // 金额包含关键词
        (item.money && item.money.toString().indexOf(keyword) !== -1) ||
        // 内容包含关键词
        (item.content && item.content.toLowerCase().indexOf(lowerKeyword) !== -1) ||
        // 增加额外字段的搜索
        (item.inputuser && item.inputuser.toLowerCase().indexOf(lowerKeyword) !== -1) ||
        // ID精确匹配
        (item.id && item.id.toString() === keyword) ||
        // 时间包含关键词
        (item.inputtime && item.inputtime.indexOf(keyword) !== -1)
      );
    });
    
    if (filteredData.length === 0) {
      this.setData({
        incomeData: [],
        expenseData: [],
        hasMore: false
      });
      wx.showToast({
        title: '未找到相关记录',
        icon: 'none'
      });
      return;
    }
    
    // 分离收入和支出数据
    let incomeData = filteredData.filter(item => item.szxm === "收入");
    let expenseData = filteredData.filter(item => item.szxm === "支出");
    
    // 去除重复ID
    incomeData = this.removeDuplicateIds(incomeData);
    expenseData = this.removeDuplicateIds(expenseData);
    
    // 提示搜索结果数量
    wx.showToast({
      title: `共找到${incomeData.length + expenseData.length}条记录`,
      icon: 'none',
      duration: 2000
    });
    
    // 保存搜索结果
    this.setData({
      incomeData: incomeData,
      expenseData: expenseData,
      // 以下代码是为了支持加载更多功能
      hasMore: false, // 本地搜索已获取全部数据，不需要加载更多
      page: 1
    });
  },

  /**
   * 处理搜索结果
   */
  processSearchResult: function(data, keyword) {
    // 复用本地搜索处理逻辑
    this.performLocalSearchFiltering(data, keyword);
  },

  /**
   * 页面滚动事件处理
   */
  onPageScroll: function(e) {
    // 提前触发加载，不等到完全到底再加载
    // 只有当距离页面底部一定距离时，且符合其他条件时，才提前加载下一页
    if (!this.data.isAdmin || !this.data.hasMore || this.data.loading) return;
    
    // 获取窗口信息
    const windowInfo = wx.getWindowInfo();
    const windowHeight = windowInfo.windowHeight;
    
    // 计算滚动到底部的距离
    const scrollToBottom = e.scrollHeight - (e.scrollTop + windowHeight);
    
    // 提高预加载距离阈值，更早开始加载下一页数据
    if (scrollToBottom < 500) {
      // 使用节流防止频繁触发，延长节流时间
      if (this.scrollLoadTimer) {
        clearTimeout(this.scrollLoadTimer);
      }
      
      this.scrollLoadTimer = setTimeout(() => {
        // 仅当确实还能加载更多数据时才触发加载
        if (this.data.hasMore && !this.data.loading) {
          this.loadFinancialData();
        }
      }, 300); // 增加延迟，减少触发频率
    }
  },
}) 