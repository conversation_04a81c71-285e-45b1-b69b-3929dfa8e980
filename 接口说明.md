# 工单小程序API接口说明

本文档详细说明了工单小程序各页面的API接口，包括接口URL、参数、请求方式及要点说明，方便在其它项目中复用。

## 目录

### I. 接口说明
- [1. 基础配置](#1-基础配置)
- [2. 登录注册相关接口](#2-登录注册相关接口)
- [3. 工单管理相关接口](#3-工单管理相关接口)
  - [3.1 工单列表](#31-工单列表)
  - [3.2 工单详情](#32-工单详情)
  - [3.3 发布工单](#33-发布工单)
  - [3.4 完成工单](#34-完成工单)
  - [3.5 删除工单](#35-删除工单)
  - [3.6 工单评论列表](#36-工单评论列表)
  - [3.7 提交工单评论](#37-提交工单评论)
  - [3.8 工单点赞](#38-工单点赞)
  - [3.9 查询工单收藏状态](#39-查询工单收藏状态)
  - [3.10 收藏/取消收藏工单](#310-收藏取消收藏工单)
- [4. 首页和统计相关接口](#4-首页和统计相关接口)
  - [4.1 工单统计数据](#41-工单统计数据)
  - [4.2 获取最新工单](#42-获取最新工单)
  - [4.3 获取首页轮播图](#43-获取首页轮播图)
- [5. 会员中心相关接口](#5-会员中心相关接口)
  - [5.1 获取用户信息](#51-获取用户信息)
  - [5.2 我的评论列表](#52-我的评论列表)
  - [5.3 我的收藏列表](#53-我的收藏列表)
  - [5.4 我的工单列表](#54-我的工单列表)
  - [5.5 支付记录](#55-支付记录)
  - [5.6 支付记录详情](#56-支付记录详情)
  - [5.7 会员消息通知列表](#57-会员消息通知列表)
  - [5.8 标记消息已读](#58-标记消息已读)
- [6. 村民管理相关接口](#6-村民管理相关接口)
  - [6.1 村民列表](#61-村民列表)
  - [6.2 村民详情](#62-村民详情)
  - [6.3 获取村组数据](#63-获取村组数据)
  - [6.4 编辑村民信息](#64-编辑村民信息)
  - [6.5 村民照片上传](#65-村民照片上传)
- [7. 公告通知相关接口](#7-公告通知相关接口)
  - [7.1 公告列表](#71-公告列表)
- [8. 文件上传相关接口](#8-文件上传相关接口)
  - [8.1 图片上传](#81-图片上传)
  - [8.2 编辑器图片上传](#82-编辑器图片上传)
- [9. 积分兑换相关接口](#9-积分兑换相关接口)
  - [9.1 积分商品列表](#91-积分商品列表)
  - [9.2 我的兑换记录](#92-我的兑换记录)
  - [9.3 积分兑换](#93-积分兑换)
- [10. 财务管理相关接口](#10-财务管理相关接口)
  - [10.1 财务记录列表](#101-财务记录列表)
  - [10.2 财务统计数据](#102-财务统计数据)
  - [10.3 添加财务记录](#103-添加财务记录)
  - [10.4 财务记录详情](#104-财务记录详情)
  - [10.5 获取年度栏目](#105-获取年度栏目)

### II. 功能实现细节
- [11. 接口实现细节](#11-接口实现细节)
  - [11.1 认证机制实现](#111-认证机制实现细节)
  - [11.2 工单发布实现](#112-工单发布关键实现)
  - [11.3 工单列表筛选](#113-工单列表筛选实现细节)
  - [11.4 权限控制](#114-权限控制实现细节)
  - [11.5 工单完成](#115-工单完成接口的完整实现)
  - [11.6 评论图片上传](#116-工单评论上传图片实现)
  - [11.7 并行数据请求](#117-统计数据并行请求实现)
  - [11.8 二级联动](#118-村组数据二级联动实现)
  - [11.9 登录验证](#119-登录状态验证实现)
  - [11.10 上拉加载](#1110-上拉加载更多实现)
  - [11.11 下拉刷新](#1111-下拉刷新实现)
  - [11.12 列表UI实现](#1112-wxml中的列表和加载状态实现)
  - [11.13 日期格式化](#1113-日期格式化实现)
  - [11.14 状态样式](#1114-工单状态样式处理)
  - [11.15 条件渲染](#1115-wxml中的状态条件渲染)
  - [11.16 数据过滤](#1116-数据过滤与处理)
  - [11.17 权限验证](#1117-管理员权限验证)
  - [11.18 错误重试](#1118-数据加载错误重试机制)
  - [11.19 搜索功能](#1119-模糊搜索实现)
  - [11.20 删除功能](#1120-超级管理员删除工单实现)
- [12. 微信小程序特有接口](#12-微信小程序特有接口)
  - [12.1 后台数据预拉取](#121-后台数据预拉取)
    - [12.1.1 设置后台数据获取Token](#1211-设置后台数据获取token)
    - [12.1.2 获取后台预拉取数据](#1212-获取后台预拉取数据)
  - [12.2 微信登录](#122-微信登录)
  - [12.3 获取用户信息](#123-获取用户信息)
  - [12.4 使用文件系统](#124-使用文件系统)

## 1. 基础配置

API接口的基础URL格式如下：

```javascript
// 基础API地址
http_api: "https://p.hnzbz.net/index.php?v=1&appid=2&appsecret=PHPCMFAE9FFC56BDD07&"

// 应用ID和密钥
appid: '2'
appsecret: 'PHPCMFAE9FFC56BDD07'

// 微信小程序配置
wx_appid: "wxfd5ec730bb6cd9c1" 
wx_secret: "f348eac25dc97721f454012d3ff1df2b"
```

**认证机制**：大多数API需要传递用户认证信息，认证参数通常为：

```javascript
// 认证字符串格式
var memberAuthString = "&api_auth_code=" + wx.getStorageSync('member_auth') + "&api_auth_uid=" + wx.getStorageSync('member_uid');
```

## 2. 登录注册相关接口

### 2.1 用户登录

**请求URL**: 
```
[基础URL]s=member&c=login
```

**请求方式**: POST

**Content-Type**: application/x-www-form-urlencoded

**请求参数**:
```javascript
{
  is_ajax: 1,
  "data[username]": 用户名,
  "data[password]": 密码
}
```

**响应参数**:
```javascript
{
  code: 1, // 1成功，0失败
  msg: "登录成功",
  data: {
    member: {
      id: 用户ID,
      username: 用户名,
      // 其他用户信息...
    },
    auth: 认证Token
  }
}
```

**实现要点**:
- 登录成功后需要保存以下本地缓存: 
  - 'member_uid': 用户ID
  - 'member_auth': 认证Token
  - 'member': 用户信息对象

### 2.2 用户注册

**请求URL**: 
```
[基础URL]s=member&c=register
```

**请求方式**: POST

**Content-Type**: application/x-www-form-urlencoded

**请求参数**:
```javascript
{
  'is_ajax': 1,
  'is_protocol': 1,
  'data[username]': 用户名,
  'data[name]': 真实姓名,
  'data[password]': 密码,
  'data[password2]': 确认密码,
  'data[email]': 电子邮箱,
  'data[phone]': 手机号码,
  'data[sscz]': 所属村组ID
}
```

**响应参数**:
```javascript
{
  code: 1, // 1成功，0失败
  msg: "注册成功",
  data: {}
}
```

**实现要点**:
- 注册前应先获取村组数据，让用户选择所属村组
- 需要进行表单验证，包括用户名、密码、手机号等必填字段
- 注册成功后跳转到登录页面

### 2.3 微信登录

**请求URL**: 
```
[基础URL]s=weixin&c=member&m=xcx
```

**请求方式**: POST

**Content-Type**: application/x-www-form-urlencoded

**请求参数**:
```javascript
{
  json: 微信用户资料(rawData),
  js_code: 微信登录code
}
```

**响应参数**:
- 如果已绑定账号:
```javascript
{
  code: 1,
  msg: "login",
  data: {
    member: {
      id: 用户ID,
      // 其他用户信息...
    },
    auth: 认证Token
  }
}
```
- 如果未绑定账号:
```javascript
{
  code: 1,
  msg: "需要绑定已有账号或注册新账号",
  data: {
    id: 微信授权ID,
    // 其他授权信息...
  }
}
```

**实现要点**:
- 需要先调用wx.login获取code
- 根据返回结果判断是直接登录还是需要进一步绑定账号/注册

### 2.4 微信账号绑定

**请求URL**: 
```
[基础URL]s=weixin&c=member&m=xcx_bang
```

**请求方式**: POST

**Content-Type**: application/x-www-form-urlencoded

**请求参数**:
```javascript
{
  is_ajax: 1,
  "data[username]": 用户名,
  oid: 微信授权ID,
  "data[password]": 密码
}
```

**响应参数**:
```javascript
{
  code: 1, // 1成功，0失败
  msg: "绑定成功",
  data: {
    member: {
      id: 用户ID,
      // 其他用户信息...
    },
    auth: 认证Token
  }
}
```

**实现要点**:
- 需要从本地缓存获取之前保存的oauth信息
- 绑定成功后保存用户信息到本地缓存

## 3. 工单管理相关接口

### 3.1 工单列表

**请求URL**: 
```
[基础URL]s=workorder&c=search&api_call_function=module_list&pagesize=[每页数量]&page=[页码]
```

**请求方式**: GET

**可选参数**:
- jindu: 工单进度(1-4)
- keyword: 搜索关键词

**响应参数**:
```javascript
{
  code: 1,
  msg: "操作成功",
  data: [
    {
      id: "工单ID",
      title: "工单标题",
      description: "工单描述",
      jindu: "工单状态", // 待受理、处理中、已回复、已完毕等
      author: "发布者",
      inputtime: "发布时间",
      leixing: "问题类型",
      xinxileixing: "信息类型",
      fabuleixing: "发布类型",
      gongkai: "是否公开",
      userid: "用户ID"
      // 其他字段...
    },
    // 更多工单...
  ]
}
```

**实现要点**:
- 可以通过jindu参数筛选不同状态的工单
- 拉取工单后需要处理数据，添加状态样式类
- 处理公开/不公开权限，非公开工单只对创建者和管理员可见

### 3.2 工单详情

**请求URL**: 
```
[基础URL]s=workorder&c=show&api_call_function=module_show&id=[工单ID]
```

**请求方式**: GET

**认证参数**:
- api_auth_code: 用户认证Token
- api_auth_uid: 用户ID

**响应参数**:
```javascript
{
  code: 1,
  msg: "操作成功",
  data: {
    id: "工单ID",
    title: "工单标题",
    content: "工单内容HTML",
    jindu: "工单状态",
    author: "发布者",
    inputtime: "发布时间",
    leixing: "问题类型",
    xinxileixing: "信息类型",
    fabuleixing: "发布类型",
    gongkai: "是否公开",
    uid: "创建者ID",
    // 其他字段...
  }
}
```

**实现要点**:
- 工单详情中的content字段是HTML格式，需要使用WxParse解析
- 需要处理工单的查看权限，非公开工单只对创建者和管理员可见
- 根据不同用户角色(管理员/普通用户)显示不同的操作按钮

### 3.3 发布工单

**请求URL**: 
```
[基础URL]s=member&app=workorder&c=home&m=add&api_auth_code=[认证Token]&api_auth_uid=[用户ID]
```

**请求方式**: POST

**Content-Type**: application/json

**请求参数**:
```javascript
{
  title: "工单标题",
  content: "工单内容",
  gongkai: "公开设置", // 保密/信息公开
  leixing: "问题类型", // 当fabuleixing为1时
  xinxileixing: "信息类型", // 当fabuleixing为2时
  fabuleixing: "发布类型", // 1:反应问题 2:发布信息
  author: "发布者姓名",
  tupianxinxi: "图片信息JSON字符串", // 已上传图片的ID列表
  jindu: "1", // 默认为待受理
  sscz: "村组ID" // 所属村组
}
```

**响应参数**:
```javascript
{
  code: 1, // 1成功，0失败
  msg: "发表成功",
  data: {
    id: "新创建的工单ID"
  }
}
```

**实现要点**:
- 发布前需要先上传图片，获取图片ID
- 表单验证：根据不同的发布类型(fabuleixing)验证不同的必填字段
- 发布成功后设置全局变量needRefreshWorkorderList=true，用于刷新工单列表
- 不同村组的工单需要正确设置sscz字段

### 3.4 完成工单

**请求URL**: 
```
[基础URL]s=workorder&c=api&m=myda&id=[工单ID]&api_auth_code=[认证Token]&api_auth_uid=[用户ID]
```

**请求方式**: GET

**响应参数**:
```javascript
{
  code: 1, // 1成功，0失败
  msg: "操作成功",
  data: {}
}
```

**实现要点**:
- 仅工单创建者或管理员可以完成工单
- 完成后需要重新加载工单详情，更新状态

### 3.5 删除工单

**请求URL**: 
```
[基础URL]s=httpapi&m=delete&appid=[应用ID]&appsecret=[应用密钥]&api_auth_code=[认证Token]&api_auth_uid=[用户ID]
```

**请求方式**: POST

**Content-Type**: application/x-www-form-urlencoded

**请求参数**:
```
id=[工单ID]&table=1_workorder&siteid=1&module=workorder
```

**响应参数**:
```javascript
{
  code: 1, // 1成功，0失败
  msg: "删除成功",
  data: {}
}
```

**实现要点**:
- 仅超级管理员可以删除工单
- 需要二次确认(showModal)
- 删除成功后需要刷新工单列表

### 3.6 工单评论列表

**请求URL**: 
```
[基础URL]s=workorder&c=comment&page=[页码]&api_call_function=module_comment_list
```

**请求方式**: GET

**认证参数**:
- api_auth_code: 用户认证Token
- api_auth_uid: 用户ID

**可选参数**:
- id: 工单ID

**响应参数**:
```javascript
{
  code: 1,
  msg: "操作成功",
  data: [
    {
      id: "评论ID",
      content: "评论内容",
      author: "评论者名称",
      picurl: "评论者头像",
      inputtime: "评论时间",
      // 其他字段...
    },
    // 更多评论...
  ]
}
```

### 3.7 提交工单评论

**请求URL**: 
```
[基础URL]s=workorder&m=post&c=comment&api_auth_code=[认证Token]&api_auth_uid=[用户ID]&id=[工单ID]
```

**请求方式**: POST

**Content-Type**: application/json

**请求参数**:
```javascript
{
  id: "工单ID",
  content: "评论内容",
  module: "workorder",
  mid: "workorder",
  cid: "工单ID",
  uid: "评论者用户ID",
  cuid: "工单创建者ID",
  author: "评论者用户名",
  pinglunzhe: "评论者用户ID",
  status: 1
}
```

**响应参数**:
```javascript
{
  code: 1, // 1成功，0失败
  msg: "评论成功",
  data: {
    id: "新创建的评论ID"
  }
}
```

**实现要点**:
- 评论可以包含图片，需要先上传图片再提交评论
- 提交成功后需要刷新评论列表

### 3.8 工单点赞

**请求URL**: 
```
[基础URL]s=zan&mid=workorder&id=[工单ID]
```

**请求方式**: GET

**响应参数**:
```javascript
{
  code: 1,
  msg: "操作成功",
  data: {
    b: "点赞数量" // 当前工单的点赞总数
  }
}
```

**实现要点**:
- 用户可以多次点击，但只记为一次点赞
- 点赞状态需要在本地存储中记录，避免重复请求
- 点赞图标需要根据点赞状态进行变化

### 3.9 查询工单收藏状态

**请求URL**: 
```
[基础URL]s=api&app=workorder&c=module&m=is_favorite&api_auth_code=[认证Token]&api_auth_uid=[用户ID]
```

**请求方式**: GET

**请求参数**:
```javascript
{
  id: "工单ID"
}
```

**响应参数**:
```javascript
{
  code: 1, // 1表示已收藏，0表示未收藏
  msg: "操作成功",
  data: {}
}
```

**实现要点**:
- 用于检查当前用户是否已收藏该工单
- 收藏图标需要根据收藏状态进行变化

### 3.10 收藏/取消收藏工单

**请求URL**: 
```
[基础URL]s=api&app=workorder&c=module&m=favorite&api_auth_code=[认证Token]&api_auth_uid=[用户ID]
```

**请求方式**: POST

**请求参数**:
```javascript
{
  id: "工单ID",
  title: "工单标题",
  url: "工单链接"
}
```

**响应参数**:
```javascript
{
  code: 1, // 1成功，0失败
  msg: "收藏成功" // 或 "取消收藏成功"
  data: {}
}
```

**实现要点**:
- 接口同时用于收藏和取消收藏（toggle行为）
- 收藏成功后需要更新收藏图标状态
- 用户需要登录才能使用此功能

## 4. 首页和统计相关接口

### 4.1 工单统计数据

**获取总工单数**:
```
[基础URL]s=httpapi&id=4
```

**获取待处理工单数**:
```
[基础URL]s=httpapi&id=5
```

**获取处理中工单数**:
```
[基础URL]s=httpapi&id=6
```

**获取已完成工单数**:
```
[基础URL]s=httpapi&id=7
```

**请求方式**: GET

**响应参数**:
```javascript
{
  code: 1,
  msg: "操作成功",
  data: {
    total: 工单数量
  }
}
```

**实现要点**:
- 这些接口可以同时请求，用于显示首页的工单统计数据
- 无需登录认证即可访问

### 4.2 获取最新工单

**请求URL**: 
```
[基础URL]s=workorder&c=search&pagesize=5&page=[页码]&api_call_function=module_list
```

**请求方式**: GET

**响应格式**: 同[工单列表](#31-工单列表)

**实现要点**:
- 用于首页显示最新工单动态
- 可设置pagesize控制返回数量
- 处理工单数据，添加状态样式类

### 4.3 获取首页轮播图

**请求URL**: 
```
[基础URL]s=httpapi&id=1
```

**请求方式**: GET

**响应参数**:
```javascript
{
  code: 1,
  msg: "操作成功",
  data: [
    "轮播图1的URL",
    "轮播图2的URL",
    // 更多轮播图...
  ]
}
```

**实现要点**:
- 轮播图URL列表用于首页展示
- 需要过滤无效的空URL
- 无需登录认证即可访问

## 5. 会员中心相关接口

### 5.1 获取用户信息

**请求URL**: 
```
[基础URL]s=member&c=api&m=userinfo&uid=[用户ID]&api_auth_code=[认证Token]&api_auth_uid=[用户ID]
```

**请求方式**: GET

**响应参数**:
```javascript
{
  code: 1,
  msg: "操作成功",
  data: {
    id: "用户ID",
    username: "用户名",
    name: "真实姓名",
    email: "电子邮箱",
    phone: "手机号",
    sscz: "所属村组ID",
    is_admin: "管理员权限等级",
    // 其他用户信息...
  }
}
```

**实现要点**:
- 登录后应保存用户信息到本地缓存
- 需要定期更新用户信息，确保数据最新

### 5.2 我的评论列表

**请求URL**: 
```
[基础URL]s=member&app=comment&c=content&m=index&field=id&module=workorder&api_call_function=member_content_comment&api_auth_uid=[用户ID]&api_auth_code=[认证Token]
```

**请求方式**: GET

**响应参数**:
```javascript
{
  code: 1,
  msg: "操作成功",
  data: [
    {
      id: "评论ID",
      content: "评论内容",
      title: "评论的工单标题",
      status: "状态",
      inputtime: "评论时间",
      // 其他字段...
    },
    // 更多评论...
  ]
}
```

**实现要点**:
- 需要过滤有效评论，确保显示的评论具有完整数据
- 评论内容可能包含HTML标签，需要使用WxParse解析

### 5.3 我的收藏列表

**请求URL**: 
```
[基础URL]s=member&app=favorite&c=home&m=index&field=id&module=workorder&api_call_function=member_content_comment&api_auth_uid=[用户ID]&api_auth_code=[认证Token]
```

**请求方式**: GET

**响应参数**:
```javascript
{
  code: 1,
  msg: "操作成功",
  data: [
    {
      id: "工单ID",
      title: "工单标题",
      url: "工单链接",
      description: "工单描述",
      // 其他字段...
    },
    // 更多收藏...
  ]
}
```

### 5.4 我的工单列表

**请求URL**: 
```
[基础URL]s=member&app=workorder&c=content&m=index&field=id&api_auth_uid=[用户ID]&api_auth_code=[认证Token]
```

**请求方式**: GET

**响应参数**:
```javascript
{
  code: 1,
  msg: "操作成功",
  data: [
    {
      id: "工单ID",
      title: "工单标题",
      url: "工单链接",
      description: "工单描述",
      jindu: "工单状态",
      // 其他字段...
    },
    // 更多工单...
  ]
}
```

### 5.5 支付记录

**请求URL**: 
```
[基础URL]s=member&app=pay&c=paylog&m=index&api_auth_uid=[用户ID]&api_auth_code=[认证Token]
```

**请求方式**: GET

**响应参数**:
```javascript
{
  code: 1,
  msg: "操作成功",
  data: [
    {
      id: "支付记录ID",
      title: "支付标题",
      money: "支付金额",
      inputtime: "支付时间",
      status: "支付状态",
      // 其他字段...
    },
    // 更多记录...
  ]
}
```

### 5.6 支付记录详情

**请求URL**: 
```
[基础URL]s=member&app=pay&c=paylog&m=show&api_auth_uid=[用户ID]&api_auth_code=[认证Token]&id=[支付记录ID]
```

**请求方式**: GET

**响应参数**:
```javascript
{
  code: 1,
  msg: "操作成功",
  data: {
    id: "支付记录ID",
    title: "支付标题",
    money: "支付金额",
    inputtime: "支付时间",
    status: "支付状态",
    // 详细信息...
  }
}
```

### 5.7 会员消息通知列表

**请求URL**: 
```
[基础URL]s=member&app=notice&c=home&m=index&tid=[通知类型ID]&api_auth_uid=[用户ID]&api_auth_code=[认证Token]&api_call_function=member_content_comment
```

**请求方式**: GET

**请求参数**:
- page: 页码
- pagesize: 每页数据量
- tid: 通知类型，默认0表示全部

**响应参数**:
```javascript
{
  code: 1,
  msg: "操作成功",
  data: [
    {
      id: "通知ID",
      title: "通知标题",
      content: "通知内容",
      inputtime: "通知时间",
      is_read: "已读状态", // 0-未读，1-已读
      // 其他字段...
    },
    // 更多通知...
  ]
}
```

**实现要点**:
- 需要用户登录认证才能访问
- 可根据类型筛选不同消息
- 需要处理已读/未读状态的显示

### 5.8 标记消息已读

**请求URL**: 
```
[基础URL]s=member&app=notice&c=home&m=read&api_auth_uid=[用户ID]&api_auth_code=[认证Token]
```

**请求方式**: POST

**请求参数**:
```javascript
{
  id: "通知ID"
}
```

**响应参数**:
```javascript
{
  code: 1,
  msg: "操作成功",
  data: {}
}
```

**实现要点**:
- 标记已读后需要更新本地消息状态
- 查看消息详情前应先标记已读

## 6. 村民管理相关接口

### 6.1 村民列表

**请求URL**: 
```
[基础URL]s=cmda&c=search&api_call_function=module_list&more=1&page=[页码]&pagesize=[每页数量]&api_auth_uid=[用户ID]&api_auth_code=[认证Token]
```

**请求方式**: GET

**可选参数**:
- keyword: 搜索关键词

**响应参数**:
```javascript
{
  code: 1,
  msg: "操作成功",
  data: [
    {
      id: "村民ID",
      name: "村民姓名",
      // 其他村民信息...
    },
    // 更多村民...
  ]
}
```

### 6.2 村民详情

**请求URL**: 
```
[基础URL]s=cmda&c=show&id=[村民ID]&api_call_function=module_show&api_auth_uid=[用户ID]&api_auth_code=[认证Token]
```

**请求方式**: GET

**响应参数**:
```javascript
{
  code: 1,
  msg: "操作成功",
  data: {
    id: "村民ID",
    name: "村民姓名",
    // 详细信息...
  }
}
```

### 6.3 获取村组数据

**请求URL**: 
```
[基础URL]s=api&c=api&m=linkage_ld&mid=member&file=&code=sscz&parent_id=0
```

**请求方式**: GET

**响应参数**:
```javascript
{
  code: 1,
  msg: "操作成功",
  data: [
    {
      region_id: "村组ID",
      region_name: "村组名称",
      // 其他字段...
    },
    // 更多村组...
  ]
}
```

**实现要点**:
- 可以通过parent_id参数获取子级村组数据
- 用于注册、发布工单时选择所属村组

### 6.4 编辑村民信息

**请求URL**: 
```
[基础URL]s=member&app=cmda&c=home&m=edit&id=[村民ID]&api_auth_uid=[用户ID]&api_auth_code=[认证Token]
```

**请求方式**: POST

**Content-Type**: application/x-www-form-urlencoded

**请求参数**:
```javascript
{
  is_ajax: 1,
  id: "村民ID",
  catid: "栏目ID", // 通常为2
  model: "cmda",
  module: "cmda",
  action: "edit",
  // 基本信息
  "data[title]": "村民姓名",
  "data[xingbie]": "性别", // 男/女
  "data[sfzhm]": "身份证号码",
  "data[shoujihaoma]": "手机号码",
  "data[hujidizhi]": "户籍地址",
  "data[isXiaohu]": "是否销户", // true/false
  // 户籍属性（可多选）
  "data[hujishuxing][]": "户籍属性1", // 如"党员"、"低保户"等
  "data[hujishuxing][]": "户籍属性2",
  // 照片数据（多张照片以数组形式提交ID）
  "data[grzp][]": "身份证件照片ID1",
  "data[grzp][]": "身份证件照片ID2",
  "data[fwzp][]": "房屋照片ID",
  "data[qtzjzp][]": "其他证件照片ID",
  "data[gczp][]": "改厕照片ID",
  "data[thumb]": "缩略图ID",
  // 其他自定义字段...
}
```

**响应参数**:
```javascript
{
  code: 1, // 1成功，0失败
  msg: "修改成功",
  data: {
    id: "村民ID"
  }
}
```

**实现要点**:
- 需要管理员权限才能编辑村民信息
- 照片需要先上传获取ID，再提交表单
- 支持多种户籍属性多选设置
- 表单数据使用x-www-form-urlencoded格式，特殊字段如数组要使用data[field][]格式

### 6.5 村民照片上传

**请求URL**: 
```
[基础URL]s=api&c=file&m=upload&api_auth_uid=[用户ID]&api_auth_code=[认证Token]&fid=[字段ID]
```

**请求方式**: POST

**Content-Type**: multipart/form-data

**字段ID对照**:
- 身份证件照片: fid=471
- 户口薄照片: fid=482
- 房屋照片: fid=483
- 改厕照片: fid=484
- 身份证件照(缩略图): fid=485

**请求参数**:
```
file_data: 文件二进制数据
type: 照片类型 // 如"grzp"、"fwzp"、"qtzjzp"、"gczp"、"thumb"
```

**响应参数**:
```javascript
{
  code: 1,
  msg: "上传成功",
  data: {
    id: "文件ID",
    file: "文件路径",
    // 其他信息...
  }
}
```

**实现要点**:
- 照片上传成功后返回的id需要保存，用于提交村民信息表单
- 支持多种类型照片的上传，通过fid区分不同类型
- 需要处理照片修改逻辑，保留未修改的原始照片ID

## 7. 公告通知相关接口

### 7.1 公告列表

**请求URL**: 
```
[基础URL]s=news&c=search&api_call_function=module_list&pagesize=[每页数量]
```

**请求方式**: GET

**响应参数**:
```javascript
{
  code: 1,
  msg: "操作成功",
  data: [
    {
      id: "公告ID",
      title: "公告标题",
      description: "公告描述",
      author: "发布者",
      updatetime: "更新时间",
      // 其他字段...
    },
    // 更多公告...
  ]
}
```

**实现要点**:
- 用于首页显示公告通知
- 无需登录认证即可访问

## 8. 文件上传相关接口

### 8.1 图片上传

**请求URL**: 
```
[基础URL]s=api&c=file&m=upload&fid=211
```

**请求方式**: POST

**Content-Type**: multipart/form-data

**请求参数**:
```
file: 文件二进制数据
```

**响应参数**:
```javascript
{
  code: 1,
  msg: "上传成功",
  data: {
    id: "文件ID",
    url: "文件URL",
    // 其他字段...
  }
}
```

**实现要点**:
- 文件大小、类型限制需要前端控制
- 上传成功后需要保存文件ID，用于关联到工单或评论

### 8.2 编辑器图片上传

**请求URL**: 
```
[基础URL]s=api&c=file&m=ueditor&action=uploadimage&encode=utf-8
```

**请求方式**: POST

**Content-Type**: multipart/form-data

**请求参数**:
```
file: 文件二进制数据
```

**响应参数**:
```javascript
{
  state: "SUCCESS",
  url: "文件URL",
  title: "文件名",
  original: "原始文件名",
  // 其他信息...
}
```

**实现要点**:
- 主要用于评论中的图片上传
- 上传成功后需要将图片URL插入到评论内容中

## 9. 积分兑换相关接口

### 9.1 积分商品列表

**请求URL**: 
```
[基础URL]s=jifen&c=search&api_call_function=module_list&more=1&page=[页码]&pagesize=[每页数量]
```

**请求方式**: GET

**响应参数**:
```javascript
{
  code: 1,
  msg: "操作成功",
  data: [
    {
      id: "商品ID",
      title: "商品标题",
      jifen: "所需积分",
      thumb: "商品缩略图",
      // 其他字段...
    },
    // 更多商品...
  ]
}
```

### 9.2 我的兑换记录

**请求URL**: 
```
[基础URL]s=httpapi&id=8&appid=[应用ID]&appsecret=[应用密钥]&page=1&pagesize=100&api_auth_code=[认证Token]&api_auth_uid=[用户ID]
```

**请求方式**: GET

**响应参数**:
```javascript
{
  code: 1,
  msg: "操作成功",
  data: [
    {
      id: "兑换记录ID",
      title: "商品名称",
      inputtime: "兑换时间",
      // 其他字段...
    },
    // 更多记录...
  ]
}
```

### 9.3 积分兑换

**请求URL**: 
```
[基础URL]s=jifen&c=buy&m=index&id=[商品ID]&api_auth_code=[认证Token]&api_auth_uid=[用户ID]
```

**请求方式**: GET

**响应参数**:
```javascript
{
  code: 1, // 1成功，0失败
  msg: "兑换成功",
  data: {}
}
```

**实现要点**:
- 兑换前需要检查用户积分是否足够
- 兑换成功后需要更新积分余额和兑换记录 

## 10. 财务管理相关接口

### 10.1 财务记录列表

**请求URL**: 
```
[基础URL]s=caiwu&c=search&api_call_function=module_list&more=1&page=[页码]&pagesize=[每页数量]&api_auth_uid=[用户ID]&api_auth_code=[认证Token]
```

**请求方式**: GET

**可选参数**:
- catid: 年度栏目ID
- szxm: 收支类型（1-收入，2-支出）
- keyword: 搜索关键词

**响应参数**:
```javascript
{
  code: 1,
  msg: "操作成功",
  data: [
    {
      id: "财务记录ID",
      title: "项目名称",
      amount: "金额",
      szxm: "收支类型", // 1-收入，2-支出
      inputtime: "记录时间",
      catid: "年度分类ID",
      author: "经手人",
      // 其他字段...
    },
    // 更多记录...
  ]
}
```

**实现要点**:
- 仅管理员可以访问此接口
- 支持按年度、收支类型筛选

### 10.2 财务统计数据

**请求URL**: 
```
[基础URL]s=httpapi&m=caiwutonj&appid=[应用ID]&appsecret=[应用密钥]&api_auth_code=[认证Token]&api_auth_uid=[用户ID]
```

**请求方式**: GET

**可选参数**:
- catid: 年度栏目ID

**响应参数**:
```javascript
{
  code: 1,
  msg: "操作成功",
  data: {
    income: "总收入",
    expense: "总支出",
    balance: "结余",
    // 其他统计数据...
  }
}
```

**实现要点**:
- 仅管理员可以访问此接口
- 可以按年度获取财务统计

### 10.3 添加财务记录

**请求URL**: 
```
[基础URL]s=member&app=caiwu&c=home&m=add&api_auth_code=[认证Token]&api_auth_uid=[用户ID]
```

**请求方式**: POST

**Content-Type**: application/json

**请求参数**:
```javascript
{
  catid: "年度栏目ID",
  title: "项目名称",
  amount: "金额",
  date: "记账日期",
  author: "经手人姓名",
  szxm: "收支类型", // 1-收入，2-支出
  shouruleibie: "收入类别", // 1-转移收入，2-经营收入，3-其它收入，4-上级补助，5-代收代付
  skfs: "收款方式", // 1-转公账，2-存现金
  zhichuleibie: "支出类别", // 1-办公支出，2-项目支出，3-代收代付，4-专项支出
  fkfs: "付款方式", // 1-转账，2-现金，4-预备金，5-代收代付
  description: "描述/备注",
  pingzhengxinxi: "凭证图片JSON字符串" // 已上传凭证的ID列表
}
```

**响应参数**:
```javascript
{
  code: 1, // 1成功，0失败
  msg: "添加成功",
  data: {
    id: "新创建的财务记录ID"
  }
}
```

**实现要点**:
- 仅管理员可以添加财务记录
- 发布前需要先上传凭证图片，获取图片ID
- 表单验证：根据不同的收支类型验证不同的必填字段

### 10.4 财务记录详情

**请求URL**: 
```
[基础URL]s=caiwu&c=show&id=[财务记录ID]&api_call_function=module_show&api_auth_uid=[用户ID]&api_auth_code=[认证Token]
```

**请求方式**: GET

**响应参数**:
```javascript
{
  code: 1,
  msg: "操作成功",
  data: {
    id: "财务记录ID",
    title: "项目名称",
    amount: "金额",
    szxm: "收支类型",
    shouruleibie: "收入类别",
    zhichuleibie: "支出类别",
    skfs: "收款方式",
    fkfs: "付款方式",
    author: "经手人",
    inputtime: "记录时间",
    description: "描述/备注",
    pingzhengxinxi: "凭证图片JSON字符串",
    // 其他字段...
  }
}
```

**实现要点**:
- 仅管理员可以查看财务记录详情
- 凭证图片需要解析JSON字符串获取图片ID，然后获取图片URL

### 10.5 获取年度栏目

**请求URL**: 
```
[基础URL]s=httpapi&m=category&mid=caiwu&pid=0&api_auth_code=[认证Token]&api_auth_uid=[用户ID]
```

**请求方式**: GET

**响应参数**:
```javascript
{
  code: 1,
  msg: "操作成功",
  data: {
    "1": {
      id: "栏目ID",
      name: "栏目名称(年度)",
      // 其他字段...
    },
    // 更多年度栏目...
  }
}
```

**实现要点**:
- 用于财务记录按年度分类和筛选
- 返回结果是对象格式，需要转换为数组使用

## 11. 接口实现细节

本章节提供关键接口功能的具体实现代码，帮助您更清晰地理解接口使用方式。

### 11.1 认证机制实现细节

认证机制是通过 `member_auth` 和 `member_uid` 两个参数实现的：

```javascript
// 认证信息存储在本地缓存中
wx.setStorageSync('member_uid', res.data.data.member.id);
wx.setStorageSync('member_auth', res.data.data.auth);
wx.setStorageSync('member', res.data.data.member);

// 构建认证请求参数
var memberAuthString = "&api_auth_code=" + wx.getStorageSync('member_auth') + "&api_auth_uid=" + wx.getStorageSync('member_uid');
```

### 11.2 工单发布关键实现

工单发布需要注意：

1. **图片上传处理**：
   ```javascript
   // 先上传图片获取ID
   wx.uploadFile({
     url: uploadUrl,
     filePath: imagePath,
     name: 'file',
     success: (res) => {
       const data = JSON.parse(res.data);
       if (data.code === 1) {
         // 保存上传成功的图片ID
         uploadedImages.push(data.data.id);
       }
     }
   });
   
   // 组织图片ID为JSON字符串
   formData.tupianxinxi = JSON.stringify(uploadedImages);
   ```

2. **村组数据处理**：
   ```javascript
   // 根据区域类型设置村组ID
   if (this.data.areaType === '2' && this.data.selectedVillageId) {
     // 其它村组模式
     villageId = this.data.selectedVillageId;
   } else if (this.data.areaType === '1') {
     // 本村组模式
     const member = wx.getStorageSync('member');
     villageId = member.sscz;
   }
   ```

### 11.3 工单列表筛选实现细节

工单列表的筛选功能实现：

```javascript
// 构建API请求URL，添加筛选参数
let url = `${app.globalData.http_api}&s=workorder&c=search&pagesize=${this.data.limit}&page=${this.data.page}`;

// 根据标签添加状态筛选
if (this.data.currentTab > 0) {
    url += `&jindu=${this.data.currentTab}`;
}

// 添加搜索关键词
if (this.data.isSearching && this.data.searchKeyword) {
    url += `&keyword=${encodeURIComponent(this.data.searchKeyword)}`;
}

url += '&api_call_function=module_list';
```

### 11.4 权限控制实现细节

非公开工单的权限控制逻辑：

```javascript
// 判断是否可以查看
const canView = (
  // 公开工单可直接查看
  item.gongkai != '保密' || 
  // 或者已登录且是管理员
  (isLoggedIn && isAdmin) || 
  // 或者是工单创建者
  (isLoggedIn && item.userid == currentUserId)
);

// 只有满足条件才显示工单
if (canView) {
  // 显示工单
}
```

### 11.5 工单完成接口的完整实现

```javascript
// 完成工单实现
completeOrder: function() {
  if (this.data.isLoading) return;
  
  wx.showModal({
    title: '提示',
    content: '确定后不能回复了，要确定吗？',
    success: (res) => {
      if (res.confirm) {
        wx.showLoading({
          title: '处理中...',
          mask: true
        });
        
        wx.request({
          url: app.globalData.http_api + "s=workorder&c=api&m=myda&id=" + this.data.id + 
               "&api_auth_code=" + wx.getStorageSync('member_auth') + 
               "&api_auth_uid=" + wx.getStorageSync('member_uid'),
          header: { 'content-type': 'application/json' },
          dataType: 'json',
          method: 'GET',
          success: (res) => {
            wx.hideLoading();
            
            if (res.data.code == 1) {
              wx.showToast({
                icon: 'success',
                title: '工单已完成',
                duration: 2000
              });
              
              // 刷新页面数据
              this.loadContentWithPermissionCheck();
            } else {
              this.handleError(new Error(res.data.msg || '操作失败'));
            }
          },
          fail: (error) => {
            wx.hideLoading();
            this.handleError(error);
          }
        });
      }
    }
  });
}
```

### 11.6 工单评论上传图片实现

评论支持图片上传的关键实现：

```javascript
// 选择图片
chooseImage: function() {
  wx.chooseImage({
    count: 3,
    sizeType: ['compressed'],
    sourceType: ['album', 'camera'],
    success: (res) => {
      this.setData({
        tempImagePaths: res.tempFilePaths
      });
    }
  });
},

// 上传图片
uploadImage: function(filePath) {
  return new Promise((resolve, reject) => {
    wx.uploadFile({
      url: upload_url,
      filePath: filePath,
      name: 'upfile',
      success: (res) => {
        const result = JSON.parse(res.data);
        if (result.state === 'SUCCESS') {
          resolve(result.url);
        } else {
          reject(new Error('上传失败'));
        }
      },
      fail: reject
    });
  });
},

// 提交评论
submitComment: function() {
  // 先上传图片
  Promise.all(this.data.tempImagePaths.map(path => this.uploadImage(path)))
    .then(uploadedUrls => {
      // 构建评论内容，包含图片
      let content = this.data.commentText;
      if (uploadedUrls.length > 0) {
        uploadedUrls.forEach(url => {
          content += `<img src="${url}" style="max-width:100%;" />`;
        });
      }
      
      // 发送评论请求
      // ...评论提交逻辑
    });
}
```

### 11.7 统计数据并行请求实现

首页同时加载多个统计接口的实现：

```javascript
// 获取总工单数
wx.request({
  url: http_url + 's=httpapi&id=4',
  method: 'GET',
  success: function(res) {
    if (res.data.code == 1) {
      self.setData({
        totalTickets: res.data.data.total || 0
      });
    }
  }
});

// 获取待处理工单数
wx.request({
  url: http_url + 's=httpapi&id=5',
  method: 'GET',
  success: function(res) {
    if (res.data.code == 1) {
      self.setData({
        pendingTickets: res.data.data.total || 0
      });
    }
  }
});

// 同时发起其他请求...
```

### 11.8 村组数据二级联动实现

村组数据的二级联动实现：

```javascript
// 获取一级村组数据
fetchVillageGroups: function() {
  wx.request({
    url: app.globalData.http_api + 's=api&c=api&m=linkage_ld&mid=member&file=&code=sscz&parent_id=0',
    method: 'GET',
    success: (res) => {
      if (res.data && res.data.data && Array.isArray(res.data.data)) {
        this.setData({ 
          villageLevel1: res.data.data,
          'villageGroupColumns[0]': res.data.data.map(item => item.region_name)
        });
      }
    }
  });
},

// 根据一级ID获取二级村组数据
fetchVillageLevel2: function(parentId) {
  wx.request({
    url: app.globalData.http_api + 's=api&c=api&m=linkage_ld&mid=member&file=&code=sscz&parent_id=' + parentId,
    method: 'GET',
    success: (res) => {
      if (res.data && res.data.data && Array.isArray(res.data.data)) {
        this.setData({
          villageLevel2: res.data.data,
          'villageGroupColumns[1]': res.data.data.map(item => item.region_name)
        });
      }
    }
  });
}
```

### 11.9 登录状态验证实现

登录状态验证是大多数页面的关键前置操作：

```javascript
// 登录状态检查函数
checkLoginStatus: function() {
    const memberAuth = wx.getStorageSync('member_auth');
    const memberUid = wx.getStorageSync('member_uid');
    
    const isLoggedIn = !!(memberAuth && memberUid);
    
    this.setData({
        isLoggedIn: isLoggedIn
    });
    
    return isLoggedIn;
},

// 在页面加载时验证
onLoad: function() {
    // 检查登录状态并处理
    const isLoggedIn = this.checkLoginStatus();
    
    if (!isLoggedIn) {
        // 如果未登录，显示登录提示并退出后续初始化
        this.showLoginModal();
        return false;
    }
    
    // 已登录，执行页面初始化
    this.initPageData();
    return true;
},

// 显示登录提示
showLoginModal: function() {
    wx.showModal({
        title: '提示',
        content: '请先登录',
        showCancel: true,
        confirmText: '去登录',
        success: (res) => {
            if (res.confirm) {
                wx.navigateTo({
                    url: '/pages/login/login'
                });
            } else {
                wx.navigateBack();
            }
        }
    });
}
```

### 11.10 上拉加载更多实现

上拉加载更多通常实现在列表页面，通过监听页面触底事件实现：

```javascript
// 页面配置
{
    "enablePullDownRefresh": true,
    "onReachBottomDistance": 50
}

// 上拉加载更多的实现
onReachBottom: function() {
    if (this.data.hasMore && !this.data.loading) {
        this.loadData();
    }
},

// 数据加载函数
loadData: function(isSearch = false, retryCount = 0) {
    if (this.data.loading) return;
    
    // 设置加载状态
    this.setData({
        loading: true,
        hidden: false
    });
    
    try {
        // 构建API请求URL
        let url = `${app.globalData.http_api}&s=workorder&c=search&pagesize=${this.data.limit}&page=${this.data.page}`;
        
        // 根据标签添加状态筛选
        if (this.data.currentTab > 0) {
            url += `&jindu=${this.data.currentTab}`;
        }
        
        // 添加搜索关键词
        if (isSearch && this.data.searchKeyword) {
            url += `&keyword=${encodeURIComponent(this.data.searchKeyword)}`;
        }
        
        url += '&api_call_function=module_list';
        
        // 发送请求
        wx.request({
            url: url,
            method: 'GET',
            success: (res) => {
                if (res.data.code === 1) {
                    const newData = res.data.data || [];
                    const hasMore = newData.length === this.data.limit;
                    
                    // 追加新数据或替换全部数据
                    this.setData({
                        listData: this.data.page === 1 ? newData : [...this.data.listData, ...newData],
                        hasMore: hasMore,
                        hidden: !hasMore,
                        page: this.data.page + 1
                    });

                    if (!hasMore) {
                        // 如果没有更多数据，延迟隐藏底部提示
                        setTimeout(() => {
                            this.setData({ hidden: true });
                        }, 1500);
                    }
                } else {
                    throw new Error(res.data.msg || '加载失败');
                }
            },
            fail: (err) => {
                console.error('请求失败:', err);
                // 重试机制
                if (retryCount < 3) {
                    setTimeout(() => {
                        this.loadData(isSearch, retryCount + 1);
                    }, 1000);
                } else {
                    wx.showToast({
                        title: '网络错误，请重试',
                        icon: 'none'
                    });
                }
            },
            complete: () => {
                this.setData({ loading: false });
            }
        });
    } catch (err) {
        console.error('加载数据错误:', err);
        this.setData({ loading: false });
        wx.showToast({
            title: err.message || '加载失败',
            icon: 'none'
        });
    }
}
```

### 11.11 下拉刷新实现

下拉刷新是更新列表内容的标准方式：

```javascript
// 下拉刷新事件处理
onPullDownRefresh: function() {
    try {
        // 重置分页参数
        this.setData({
            page: 1,
            listData: [],
            hasMore: true,
            hidden: true,
            refreshing: true
        });
        
        // 重新加载第一页数据
        let url = `${app.globalData.http_api}&s=workorder&c=search&pagesize=${this.data.limit}&page=1`;
        
        if (this.data.currentTab > 0) {
            url += `&jindu=${this.data.currentTab}`;
        }
        
        if (this.data.isSearching && this.data.searchKeyword) {
            url += `&keyword=${encodeURIComponent(this.data.searchKeyword)}`;
        }
        
        url += '&api_call_function=module_list';

        wx.request({
            url: url,
            method: 'GET',
            success: (res) => {
                if (res.data.code === 1) {
                    const newData = res.data.data || [];
                    const hasMore = newData.length === this.data.limit;
                    
                    this.setData({
                        listData: newData,
                        hasMore: hasMore,
                        hidden: !hasMore,
                        page: 2
                    });
                } else {
                    throw new Error(res.data.msg || '刷新失败');
                }
            },
            fail: (err) => {
                console.error('刷新失败:', err);
                wx.showToast({
                    title: '刷新失败，请重试',
                    icon: 'none'
                });
            },
            complete: () => {
                this.setData({ 
                    loading: false,
                    refreshing: false
                });
                wx.stopPullDownRefresh();
            }
        });
    } catch (err) {
        console.error('下拉刷新错误:', err);
        this.setData({ 
            loading: false,
            refreshing: false
        });
        wx.stopPullDownRefresh();
        wx.showToast({
            title: err.message || '刷新失败',
            icon: 'none'
        });
    }
}

// 高级刷新实现 - 使用Promise.all并发刷新多个数据源
refreshAllData: function() {
    var self = this;
    
    // 显示刷新加载提示
    wx.showLoading({
        title: '刷新中...',
        mask: true
    });
    
    // 重新加载所有数据
    Promise.all([
        // 重新加载基础数据
        new Promise((resolve) => {
            wx.request({
                url: http_url,
                method: 'GET',
                success: function(res) {
                    if (res.data && res.data.code == 1) {
                        self.setData({
                            fabuData: Array.isArray(res.data.data) ? res.data.data : []
                        });
                    }
                    resolve();
                },
                fail: function() {
                    resolve();
                }
            });
        }),
        // 重新加载统计数据
        new Promise((resolve) => {
            self.loadStatistics();
            resolve();
        }),
        // 重新加载列表数据
        new Promise((resolve) => {
            self.loadListData(1, false);
            resolve();
        })
    ]).then(() => {
        // 停止下拉刷新动画
        wx.stopPullDownRefresh();
        wx.hideLoading();
        // 显示刷新成功提示
        wx.showToast({
            title: '刷新成功',
            icon: 'success',
            duration: 1500
        });
    }).catch(error => {
        console.error('刷新数据出错：', error);
        wx.stopPullDownRefresh();
        wx.hideLoading();
        wx.showToast({
            title: '刷新失败',
            icon: 'error',
            duration: 1500
        });
    });
}
```

### 11.12 WXML中的列表和加载状态实现

在WXML模板中实现列表和加载状态UI：

```html
<scroll-view 
    scroll-y 
    class="list-scroll" 
    bindscrolltolower="loadMore"
    refresher-enabled="{{true}}"
    refresher-triggered="{{refreshing}}"
    bindrefresherrefresh="onPullDownRefresh">
    
    <!-- 列表内容 -->
    <view class="list-container">
        <block wx:for="{{listData}}" wx:key="id">
            <view class="list-item" bindtap="viewDetail" data-id="{{item.id}}">
                <view class="item-title">{{item.title}}</view>
                <view class="item-desc">{{item.description}}</view>
                <view class="item-meta">
                    <text class="author">{{item.author}}</text>
                    <text class="time">{{item.inputtime}}</text>
                </view>
                <view class="item-status {{item.statusClass}}">{{item.jindu}}</view>
            </view>
        </block>
    </view>

    <!-- 加载状态 -->
    <view class="loading-container" wx:if="{{loading && !hidden}}">
        <view class="loading">
            <view class="loading-spinner"></view>
            <text>加载中...</text>
        </view>
    </view>

    <!-- 无更多数据 -->
    <view class="no-more" wx:if="{{!hasMore && listData.length > 0 && !loading}}">
        <view class="divider">
            <view class="divider-line"></view>
            <text>没有更多内容了</text>
            <view class="divider-line"></view>
        </view>
    </view>

    <!-- 空状态 -->
    <view class="empty-state" wx:if="{{listData.length === 0 && !loading}}">
        <image class="empty-icon" src="../../images/empty.png" mode="aspectFit"/>
        <text class="empty-text">暂无数据</text>
    </view>
</scroll-view>
```

### 11.13 日期格式化实现

日期格式化是列表展示中的常见需求，按照不同时间跨度显示不同格式：

```javascript
// 格式化时间戳
formatTime: function(timestamp) {
  if (!timestamp) return '';
  
  const ts = parseInt(timestamp);
  if (isNaN(ts)) return timestamp;
  
  const date = new Date(ts * 1000);
  const now = new Date();
  const diff = Math.floor((now - date) / 1000);
  
  // 今天内
  if (diff < 86400 && date.getDate() === now.getDate()) {
    const hours = date.getHours().toString().padStart(2, '0');
    const minutes = date.getMinutes().toString().padStart(2, '0');
    return `今天 ${hours}:${minutes}`;
  }
  
  // 昨天
  const yesterday = new Date();
  yesterday.setDate(yesterday.getDate() - 1);
  if (date.getDate() === yesterday.getDate() && 
      date.getMonth() === yesterday.getMonth() && 
      date.getFullYear() === yesterday.getFullYear()) {
    const hours = date.getHours().toString().padStart(2, '0');
    const minutes = date.getMinutes().toString().padStart(2, '0');
    return `昨天 ${hours}:${minutes}`;
  }
  
  // 一周内
  if (diff < 604800) {
    const days = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
    const day = days[date.getDay()];
    const hours = date.getHours().toString().padStart(2, '0');
    const minutes = date.getMinutes().toString().padStart(2, '0');
    return `${day} ${hours}:${minutes}`;
  }
  
  // 更早
  const year = date.getFullYear();
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const day = date.getDate().toString().padStart(2, '0');
  
  return `${year}-${month}-${day}`;
}
```

### 11.14 工单状态样式处理

为不同状态的工单添加不同的样式类：

```javascript
/**
 * 获取工单状态对应的样式类
 * @param {string} jindu - 工单进度状态
 * @return {string} 对应的CSS类名
 */
getStatusClass: function(jindu) {
  var statusMap = {
    '待受理': 'status-pending',
    '待回复': 'status-waiting',
    '已回复': 'status-replied',
    '处理中': 'status-processing',
    '已完毕': 'status-completed'
  };
  return statusMap[jindu] || 'status-default';
}
```

### 11.15 WXML中的状态条件渲染

在模板中根据工单状态使用条件渲染：

```html
<text class="order-status {{item.jindu == '1' ? 'status-pending' : 
                            item.jindu == '2' ? 'status-processing' : 
                            item.jindu == '3' ? 'status-completed' : 
                            item.jindu == '4' ? 'status-replied' : 
                            'status-waiting'}}">
  <text class="status-label">进度：</text>
  {{item.jindu == '1' ? '待受理' : 
    item.jindu == '2' ? '处理中' : 
    item.jindu == '3' ? '已完毕' : 
    item.jindu == '4' ? '已回复' : '待回复'}}
</text>
```

### 11.16 数据过滤与处理

处理API返回的数据，确保数据完整性和有效性：

```javascript
// 对接口返回数据进行过滤和格式化
var newData = res.data.data.filter(function(item) {
  return item && 
         item.id && 
         item.content && 
         item.content.trim() !== '' && 
         item.status !== '0' && // 假设status为0表示已删除
         item.title && // 确保有标题
         item.title.trim() !== '';
});

// 格式化时间
newData.forEach(item => {
  if (item.inputtime) {
    item.inputtime = this.formatTime(item.inputtime);
  }
});

// 处理HTML内容
for(var i = 0; i < newData.length; i++) {
  WxParse.wxParse('content', 'html', newData[i].content, this, i);
}
```

### 11.17 管理员权限验证

验证用户是否具有管理员权限：

```javascript
// 获取当前用户信息
const member = wx.getStorageSync('member');

// 检查是否是管理员
const isAdmin = member && member.is_admin && parseInt(member.is_admin) > 0;

// 根据权限展示不同的操作按钮或功能
if (isAdmin) {
  // 显示管理员特有功能
  this.setData({
    showAdminFeatures: true
  });
} else {
  // 普通用户功能
  this.setData({
    showAdminFeatures: false
  });
}
```

### 11.18 数据加载错误重试机制

网络请求失败时的重试机制：

```javascript
loadData: function(isSearch = false, retryCount = 0) {
  // 最多重试3次
  if (retryCount >= 3) {
    wx.showToast({
      title: '网络异常，请稍后再试',
      icon: 'none'
    });
    this.setData({ loading: false });
    return;
  }
  
  wx.request({
    // 请求配置...
    fail: (err) => {
      console.error('请求失败:', err);
      // 延迟重试，避免立即重试导致网络拥塞
      setTimeout(() => {
        this.loadData(isSearch, retryCount + 1);
      }, 1000 * (retryCount + 1)); // 递增重试间隔
    }
  });
}
```

### 11.19 模糊搜索实现

工单搜索功能的实现：

```javascript
// 监听搜索框输入
onSearchInput: function(e) {
  this.setData({
    searchKeyword: e.detail.value
  });
},

// 执行搜索
startSearch: function() {
  if (!this.data.searchKeyword.trim()) {
    wx.showToast({
      title: '请输入搜索关键词',
      icon: 'none'
    });
    return;
  }
  
  this.setData({
    isSearching: true,
    page: 1,
    listData: []
  });
  
  this.loadData(true);
},

// 清除搜索
clearSearch: function() {
  this.setData({
    searchKeyword: '',
    isSearching: false,
    page: 1,
    listData: []
  });
  this.loadData();
}
```

### 11.20 超级管理员删除工单实现

管理员删除工单功能的完整实现：

```javascript
// 删除工单
onDelete: function() {
  const id = this.data.selectedId;
  
  if (!id) {
    wx.showToast({
      title: '参数错误',
      icon: 'none'
    });
    return;
  }
  
  // 再次验证当前用户的超级管理员权限
  const member = wx.getStorageSync('member');
  const isAdmin = member.is_admin && parseInt(member.is_admin) > 0;
  
  if (!isAdmin) {
    wx.showToast({
      title: '没有删除权限',
      icon: 'none',
      duration: 2000
    });
    return;
  }
  
  const that = this;
  const member_auth = wx.getStorageSync('member_auth');
  const member_uid = wx.getStorageSync('member_uid');
  
  // 首先获取原始http_api地址
  let baseUrl = app.globalData.http_api;
  
  // 检查http_api是否已经包含查询参数
  if (baseUrl.includes('?')) {
    // 如果已经包含?但没有以&结尾，添加&
    if (!baseUrl.endsWith('&')) {
      baseUrl += '&';
    }
  } else {
    // 如果不包含?，添加?
    baseUrl += '?';
  }
  
  // 构建删除API URL
  const requestUrl = `${baseUrl}s=httpapi&m=delete&appid=${app.globalData.appid}&appsecret=${app.globalData.appsecret}&api_auth_code=${member_auth}&api_auth_uid=${member_uid}`;
  
  // 二次确认
  wx.showModal({
    title: '确认删除',
    content: '确定要删除这条工单吗？此操作不可恢复。',
    confirmColor: '#e64340',
    success: function(res) {
      if (res.confirm) {
        // 构建POST数据
        const postData = `id=${id}&table=1_workorder&siteid=1&module=workorder`;
        
        wx.request({
          url: requestUrl,
          method: 'POST',
          data: postData,
          header: {
            'Content-Type': 'application/x-www-form-urlencoded'
          },
          success: function(res) {
            if (res.data.code == 1) {
              wx.showToast({
                title: '删除成功',
                icon: 'success'
              });
              
              // 刷新列表数据
              that.setData({
                page: 1,
                listData: [],
                showActionMenu: false,
                selectedId: null
              });
              that.loadData();
            } else {
              wx.showToast({
                title: res.data.msg || '删除失败',
                icon: 'none'
              });
            }
          },
          fail: function(err) {
            console.error('请求失败:', err);
            wx.showToast({
              title: '网络错误，请重试',
              icon: 'none'
            });
          }
        });
      }
    }
  });
} 
```

## 12. 微信小程序特有接口

本章节介绍微信小程序特有的接口功能，这些接口依赖于微信小程序平台，不适用于其他平台。

### 12.1 后台数据预拉取

微信小程序提供了后台数据预拉取功能，可以在小程序冷启动时提前获取数据，提升用户体验。

#### 12.1.1 设置后台数据获取Token

**接口**: `wx.setBackgroundFetchToken`

**使用方式**:
```javascript
wx.setBackgroundFetchToken({
  token: 'workorder_background_fetch',
  success: () => {
    console.log('设置后台数据获取token成功');
  },
  fail: (err) => {
    console.error('设置后台数据获取token失败:', err);
  }
});
```

**实现要点**:
- 通常在小程序初始化时调用（如app.js的onLaunch中）
- token应该是一个有意义的字符串，用于标识数据用途

#### 12.1.2 获取后台预拉取数据

**接口**: `wx.getBackgroundFetchData`

**使用方式**:
```javascript
wx.getBackgroundFetchData({
  fetchType: 'pre', // 'pre'表示预拉取数据
  success: (res) => {
    console.log('获取后台数据成功:', res);
    if (res.fetchedData) {
      // 处理预拉取的数据
    }
  },
  fail: (err) => {
    console.error('获取后台数据失败:', err);
  }
});
```

**实现要点**:
- 需要在app.js中做好容错处理，避免数据获取失败影响应用正常启动
- 预拉取的数据应该是轻量级的，通常是首屏需要的关键数据
- 需要与后端配合实现数据预拉取逻辑

### 12.2 微信登录

**接口**: `wx.login`

**使用方式**:
```javascript
wx.login({
  success: (res) => {
    if (res.code) {
      // 发送code到后端换取openId、sessionKey、unionId
      wx.request({
        url: app.globalData.http_api + "s=weixin&c=member&m=xcx",
        data: {
          json: userInfoRawData,
          js_code: res.code
        },
        // ...其他请求配置
      });
    } else {
      console.error('登录失败', res.errMsg);
    }
  }
});
```

**实现要点**:
- 登录流程参见[微信登录接口](#23-微信登录)
- 微信登录获取的code有效期较短，需要及时使用
- 应该妥善保存服务端返回的用户认证信息

### 12.3 获取用户信息

**接口**: `wx.getUserProfile`

**使用方式**:
```javascript
wx.getUserProfile({
  desc: '用于完善会员资料',
  success: (userInfo) => {
    // 获取用户信息成功
    console.log(userInfo);
    // 进行后续登录操作
  },
  fail: (res) => {
    // 用户拒绝授权
    console.error('获取用户信息失败', res);
  }
});
```

**实现要点**:
- 需要在用户主动触发的事件中调用此接口（如点击按钮）
- desc参数必须说明获取用户信息的用途
- 用户可能拒绝授权，需要做好容错处理

### 12.4 使用文件系统

**接口**: `wx.getFileSystemManager`

**使用方式**:
```javascript
const fs = wx.getFileSystemManager();
// 创建目录
fs.mkdir({
  dirPath: `${wx.env.USER_DATA_PATH}/temp`,
  recursive: true,
  success: () => {
    console.log('创建临时目录成功');
  },
  fail: (err) => {
    console.error('创建临时目录失败', err);
  }
});
```

**实现要点**:
- 用于在本地文件系统中创建、读写文件
- 适用于需要缓存大量数据的场景
- 注意文件存储空间限制和清理机制