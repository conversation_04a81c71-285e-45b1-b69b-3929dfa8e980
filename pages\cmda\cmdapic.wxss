/* 页面容器 */
.container {
  min-height: 100vh;
  background: #f8f9fa;
  position: relative;
}

/* 导航栏 */
.navbar {
  position: sticky;
  top: 0;
  z-index: 1000;
  background: white;
  border-bottom: 1rpx solid #e9ecef;
}

.navbar-content {
  display: flex;
  align-items: center;
  height: 88rpx;
  padding: 0 30rpx;
}

.nav-left {
  width: 80rpx;
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.back-icon {
  font-size: 36rpx;
  color: #495057;
  font-weight: 500;
}

.nav-center {
  flex: 1;
  display: flex;
  justify-content: center;
}

.nav-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #212529;
}

.nav-right {
  width: 80rpx;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.save-indicator {
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  background: #f8f9fa;
}

.save-indicator.active {
  background: #fff3cd;
}

.save-text {
  font-size: 22rpx;
  color: #6c757d;
}

.save-indicator.active .save-text {
  color: #856404;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 60vh;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #e9ecef;
  border-top: 4rpx solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 30rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #6c757d;
}

/* 主要内容 */
.main-content {
  padding: 30rpx;
  padding-bottom: 120rpx;
}

/* 顶部保存按钮（已移除，不再使用） */

/* 用户信息 */
.user-header {
  background: white;
  border-radius: 24rpx;
  padding: 40rpx;
  margin-bottom: 30rpx;
  display: flex;
  align-items: center;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.user-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  margin-right: 30rpx;
  overflow: hidden;
  background: #e9ecef;
}

.avatar-img {
  width: 100%;
  height: 100%;
}

.user-info {
  flex: 1;
}

.user-name {
  font-size: 36rpx;
  font-weight: 600;
  color: #212529;
  margin-bottom: 10rpx;
  display: block;
}

.user-id {
  font-size: 24rpx;
  color: #6c757d;
  margin-bottom: 15rpx;
  display: block;
}

.photo-count {
  font-size: 26rpx;
  color: #495057;
  display: block;
}

/* 图片分类区域 */
.photo-sections {
  display: flex;
  flex-direction: column;
  gap: 30rpx;
}

.photo-section {
  background: white;
  border-radius: 24rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
  padding-bottom: 20rpx;
  border-bottom: 2rpx solid #f8f9fa;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 15rpx;
}

.title-icon {
  font-size: 32rpx;
}

.title-text {
  font-size: 32rpx;
  font-weight: 600;
  color: #212529;
}

.section-count {
  font-size: 24rpx;
  color: #6c757d;
  background: #f8f9fa;
  padding: 8rpx 16rpx;
  border-radius: 16rpx;
}

/* 个人照片特殊样式 */
.avatar-section {
  display: flex;
  justify-content: center;
}

.avatar-preview {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 30rpx;
}

.avatar-large {
  width: 200rpx;
  height: 200rpx;
  border-radius: 100rpx;
  border: 4rpx solid #e9ecef;
}

.avatar-actions {
  display: flex;
  gap: 20rpx;
}

.action-btn {
  padding: 16rpx 32rpx;
  border-radius: 24rpx;
  font-size: 26rpx;
  font-weight: 500;
  border: none;
}

.action-btn.primary {
  background: linear-gradient(135deg, #007bff, #0056b3);
  color: white;
}

.action-btn.secondary {
  background: #f8f9fa;
  color: #6c757d;
  border: 2rpx solid #e9ecef;
}

.avatar-upload {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx 40rpx;
  border: 2rpx dashed #dee2e6;
  border-radius: 16rpx;
  background: #f8f9fa;
}

.upload-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
  opacity: 0.6;
}

.upload-text {
  font-size: 28rpx;
  font-weight: 500;
  color: #495057;
  margin-bottom: 10rpx;
}

.upload-desc {
  font-size: 24rpx;
  color: #6c757d;
}

/* 图片网格 */
.photo-grid {
  display: flex;
  flex-wrap: wrap;
  /* 兼容性：不使用 gap，改为内边距形成间距 */
  margin: 0 -10rpx;
}

.photo-item {
  width: 33.333%;
  padding: 0 10rpx;
  box-sizing: border-box;
  height: 160rpx;
  position: relative;
  border-radius: 12rpx;
  overflow: hidden;
  margin-bottom: 15rpx;
}

.photo-img {
  width: 100%;
  height: 100%;
}

.delete-btn {
  position: absolute;
  top: 8rpx;
  right: 8rpx;
  width: 40rpx;
  height: 40rpx;
  background: rgba(0, 0, 0, 0.6);
  color: white;
  border-radius: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  font-weight: bold;
}

.upload-item {
  width: 33.333%;
  padding: 0 10rpx;
  box-sizing: border-box;
  height: 160rpx;
  border: 2rpx dashed #dee2e6;
  border-radius: 12rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: #f8f9fa;
  margin-bottom: 15rpx;
}

.upload-plus {
  font-size: 48rpx;
  color: #6c757d;
  margin-bottom: 8rpx;
}

.upload-label {
  font-size: 22rpx;
  color: #6c757d;
}

/* 底部操作栏 */
.bottom-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 20rpx 30rpx;
  padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
  border-top: 1rpx solid #e9ecef;
  display: flex;
  gap: 20rpx;
  z-index: 1000;
}

.bottom-btn {
  flex: 1;
  height: 80rpx;
  border-radius: 40rpx;
  font-size: 28rpx;
  font-weight: 500;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

.bottom-btn.primary {
  background: linear-gradient(135deg, #007bff, #0056b3);
  color: white;
}

.bottom-btn.secondary {
  background: #f8f9fa;
  color: #6c757d;
  border: 2rpx solid #e9ecef;
}

.bottom-btn:active {
  transform: scale(0.98);
}

/* 响应式调整 */
@media (max-width: 750rpx) {
  .main-content {
    padding: 20rpx;
  }

  .user-header,
  .photo-section {
    padding: 25rpx;
    margin-bottom: 20rpx;
  }

  .photo-grid {
    gap: 10rpx;
  }

  .photo-item,
  .upload-item {
    width: calc(33.333% - 7rpx);
    height: 140rpx;
  }
}

/* 底部操作栏 */
.bottom-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 20rpx 30rpx;
  padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
  border-top: 1rpx solid #e9ecef;
  display: flex;
  gap: 20rpx;
  z-index: 1000;
}

.bottom-btn {
  flex: 1;
  height: 80rpx;
  border-radius: 40rpx;
  font-size: 28rpx;
  font-weight: 500;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

.bottom-btn.primary {
  background: linear-gradient(135deg, #007bff, #0056b3);
  color: white;
}

.bottom-btn.secondary {
  background: #f8f9fa;
  color: #6c757d;
  border: 2rpx solid #e9ecef;
}

.bottom-btn:active {
  transform: scale(0.98);
}

/* 响应式调整 */
@media (max-width: 750rpx) {
  .main-content {
    padding: 20rpx;
  }

  .user-header,
  .photo-section {
    padding: 25rpx;
    margin-bottom: 20rpx;
  }

  .photo-grid {
    gap: 10rpx;
  }

  .photo-item,
  .upload-item {
    width: calc(33.333% - 7rpx);
    height: 140rpx;
  }
}

/* 底部操作栏 */
.bottom-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 20rpx 30rpx;
  padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
  border-top: 1rpx solid #e9ecef;
  display: flex;
  gap: 20rpx;
  z-index: 1000;
}

.bottom-btn {
  flex: 1;
  height: 80rpx;
  border-radius: 40rpx;
  font-size: 28rpx;
  font-weight: 500;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

.bottom-btn.primary {
  background: linear-gradient(135deg, #007bff, #0056b3);
  color: white;
}

.bottom-btn.secondary {
  background: #f8f9fa;
  color: #6c757d;
  border: 2rpx solid #e9ecef;
}

.bottom-btn:active {
  transform: scale(0.98);
}

/* 响应式调整 */
@media (max-width: 750rpx) {
  .main-content {
    padding: 20rpx;
  }
  
  .user-header,
  .photo-section {
    padding: 25rpx;
    margin-bottom: 20rpx;
  }
  
  .photo-grid {
    gap: 10rpx;
  }
  
  .photo-item,
  .upload-item {
    width: calc(33.333% - 7rpx);
    height: 140rpx;
  }
}
