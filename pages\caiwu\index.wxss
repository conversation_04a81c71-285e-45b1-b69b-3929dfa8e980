.container {
  padding: 20rpx;
  background-color: #f8f9fa;
  min-height: 100vh;
  box-sizing: border-box;
  overflow-x: hidden;
}

/* 财务概览卡片样式 */
.finance-overview-card {
  background-color: #fff;
  border-radius: 16rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

.card-header {
  padding: 24rpx 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1rpx solid #f0f0f0;
}

.card-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.card-date {
  font-size: 26rpx;
  color: #666;
  background-color: #f5f7fa;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
}

.balance-summary {
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  background-color: #fafbfc;
}

.current-balance {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.balance-label {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 12rpx;
}

.balance-value {
  font-size: 52rpx;
  font-weight: bold;
}

.positive {
  color: #22c55e;
}

.negative {
  color: #ef4444;
}

.stats-container {
  display: flex;
  padding: 20rpx 0;
}

.stat-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16rpx 0;
  position: relative;
}

.stat-item:not(:last-child):after {
  content: '';
  position: absolute;
  right: 0;
  top: 20%;
  height: 60%;
  width: 1rpx;
  background-color: #eee;
}

.stat-label {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 10rpx;
}

.stat-value {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

/* 筛选和搜索区域 */
.control-panel {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.search-bar {
  flex: 1;
  margin-right: 16rpx;
  display: flex;
  align-items: center;
}

.search-input-wrap {
  height: 70rpx;
  background-color: #fff;
  border-radius: 35rpx;
  display: flex;
  align-items: center;
  padding: 0 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
  flex: 1;
}

.search-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 12rpx;
}

.search-input {
  flex: 1;
  height: 70rpx;
  font-size: 28rpx;
  color: #333;
}

.clear-icon {
  width: 36rpx;
  height: 36rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 36rpx;
  color: #999;
}

.search-btn {
  min-width: 100rpx;
  height: 70rpx;
  background-color: #22c55e;
  border-radius: 35rpx;
  color: #fff;
  font-size: 28rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 16rpx;
  padding: 0 20rpx;
  box-shadow: 0 4rpx 8rpx rgba(34, 197, 94, 0.2);
}

.search-btn:active {
  opacity: 0.9;
}

.filter-btn {
  height: 70rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #fff;
  border-radius: 35rpx;
  padding: 0 24rpx;
  font-size: 28rpx;
  color: #333;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}

.filter-icon {
  margin-left: 8rpx;
  display: inline-block;
  width: 0;
  height: 0;
  border-left: 10rpx solid transparent;
  border-right: 10rpx solid transparent;
  border-top: 10rpx solid #666;
}

/* 标签页样式 */
.transaction-tabs {
  display: flex;
  background-color: #fff;
  margin-bottom: 24rpx;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.06);
}

.tab {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 24rpx 0;
  position: relative;
}

.tab.active {
  background-color: #f8faf8;
}

.tab.active:after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 30%;
  width: 40%;
  height: 6rpx;
  background-color: #22c55e;
  border-radius: 3rpx;
}

.tab-text {
  font-size: 30rpx;
  color: #333;
  margin-bottom: 8rpx;
  font-weight: 500;
}

.tab.active .tab-text {
  color: #22c55e;
  font-weight: bold;
}

.tab-amount {
  font-size: 26rpx;
  color: #666;
}

/* 搜索状态提示 */
.search-status {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10rpx 30rpx;
  background-color: #f8f8f8;
  color: #666;
  font-size: 26rpx;
  border-bottom: 1px solid #eee;
}

.search-status .result-count {
  color: #ff6b6b;
  font-weight: bold;
  margin: 0 4rpx;
}

.clear-search {
  color: #007aff;
  padding: 6rpx 12rpx;
}

/* 交易列表样式 */
.transaction-list {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 8rpx 0;
  margin-bottom: 110rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.06);
}

.transaction-item {
  padding: 24rpx 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.transaction-item:last-child {
  border-bottom: none;
}

.transaction-info {
  display: flex;
  flex-direction: column;
}

.transaction-main {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
}

.transaction-title {
  font-size: 32rpx;
  color: #333;
  font-weight: 500;
  flex: 1;
  margin-right: 20rpx;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.transaction-amount {
  font-size: 34rpx;
  font-weight: bold;
  flex-shrink: 0;
  min-width: 140rpx;
  text-align: right;
}

.transaction-meta {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.transaction-category {
  display: flex;
  flex-wrap: wrap;
  flex: 1;
  margin-right: 20rpx;
}

.category-tag {
  font-size: 24rpx;
  color: #666;
  background-color: #f5f7fa;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  margin-right: 10rpx;
  margin-bottom: 6rpx;
}

.transaction-time {
  font-size: 24rpx;
  color: #999;
  flex-shrink: 0;
}

/* 空列表状态 */
.empty-list {
  text-align: center;
  padding: 80rpx 0;
  color: #999;
  font-size: 28rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.empty-icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 20rpx;
  opacity: 0.6;
}

/* 加载状态 */
.loading-status {
  text-align: center;
  padding: 24rpx 0;
  color: #999;
  font-size: 26rpx;
}

.loading-indicator {
  display: flex;
  justify-content: center;
  align-items: center;
}

.loading-spinner {
  width: 36rpx;
  height: 36rpx;
  border: 3rpx solid #ddd;
  border-top: 3rpx solid #22c55e;
  border-radius: 50%;
  margin-right: 12rpx;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.no-more-data {
  color: #999;
}

/* 浮动添加按钮 */
.fab-button {
  position: fixed;
  right: 40rpx;
  bottom: 180rpx;
  width: 110rpx;
  height: 110rpx;
  background: linear-gradient(to bottom right, #2dd36f, #22c55e);
  color: #fff;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0 6rpx 16rpx rgba(34, 197, 94, 0.3);
  z-index: 99;
}

.fab-icon {
  font-size: 60rpx;
  font-weight: 300;
  line-height: 1;
}

/* 筛选弹窗 */
.filter-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.4);
  z-index: 1000;
}

.filter-panel {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  background-color: #fff;
  border-top-left-radius: 24rpx;
  border-top-right-radius: 24rpx;
  z-index: 1001;
  display: flex;
  flex-direction: column;
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);
  max-height: 80vh;
}

.panel-header {
  padding: 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1rpx solid #f0f0f0;
}

.panel-title {
  font-size: 32rpx;
  color: #333;
  font-weight: bold;
}

.panel-close {
  font-size: 44rpx;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #999;
  border-radius: 50%;
}

.close-hover {
  background-color: #f0f0f0;
}

.panel-content {
  overflow-y: auto;
  max-height: calc(80vh - 200rpx);
  padding-bottom: 20rpx;
}

.filter-option {
  padding: 28rpx 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1rpx solid #f5f5f5;
  font-size: 30rpx;
  color: #333;
}

.filter-option.selected {
  color: #22c55e;
  font-weight: bold;
  background-color: #f8faf8;
}

.option-hover {
  background-color: #f5f5f5;
}

.check-mark {
  color: #22c55e;
  font-size: 36rpx;
  font-weight: bold;
}

.panel-footer {
  padding: 24rpx 30rpx;
  border-top: 1rpx solid #f0f0f0;
}

.btn-apply {
  width: 100%;
  height: 80rpx;
  background-color: #22c55e;
  color: #fff;
  font-size: 30rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 40rpx;
}

/* 无权限页面 */
.no-permission {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  background-color: #f8f9fa;
}

.lock-icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 30rpx;
  opacity: 0.8;
}

.no-permission-text {
  font-size: 34rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 12rpx;
}

.permission-desc {
  font-size: 28rpx;
  color: #666;
} 