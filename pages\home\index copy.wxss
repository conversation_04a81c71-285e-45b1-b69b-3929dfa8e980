/* 基础样式 */
.index-body {
  padding-bottom: 30rpx;
  background: #f5f5f5;
}

/* 搜索框样式 */
.search-section {
  background: #fff;
  padding: 30rpx 20rpx 20rpx 20rpx;
  margin-bottom: 20rpx;
}

.search-container {
  position: relative;
}

.search-box {
  display: flex;
  align-items: center;
  background: #f8f9fa;
  border-radius: 25rpx;
  padding: 0 20rpx;
  border: 2rpx solid #e9ecef;
  transition: all 0.3s ease;
}

.search-box:focus-within {
  border-color: #1890ff;
  box-shadow: 0 0 0 4rpx rgba(24, 144, 255, 0.1);
}

.search-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 16rpx;
  opacity: 0.6;
}

.search-input {
  flex: 1;
  height: 80rpx;
  font-size: 28rpx;
  color: #333;
  background: transparent;
}

.search-input::placeholder {
  color: #999;
}

.search-btn {
  background: #1890ff;
  color: #fff;
  padding: 12rpx 24rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  margin-left: 16rpx;
  transition: all 0.3s ease;
}

.search-btn:active {
  background: #096dd9;
  transform: scale(0.95);
}

/* 搜索历史样式 */
.search-history {
  margin-top: 20rpx;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
}

.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.history-title {
  font-size: 26rpx;
  color: #666;
  font-weight: 500;
}

.clear-history {
  font-size: 24rpx;
  color: #1890ff;
}

.history-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
}

.history-tag {
  padding: 8rpx 16rpx;
  background: #fff;
  color: #666;
  font-size: 24rpx;
  border-radius: 20rpx;
  border: 1rpx solid #e9ecef;
  transition: all 0.3s ease;
}

.history-tag:active {
  background: #e6f7ff;
  color: #1890ff;
  border-color: #1890ff;
}

/* 统计概览样式 */
.statistics-section {
  display: flex;
  justify-content: space-between;
  padding: 30rpx;
  margin: 20rpx;
  background: #fff;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.stat-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 24rpx 16rpx;
  margin: 0 8rpx;
  background: #fff;
  border-radius: 12rpx;
  position: relative;
  transition: all 0.3s ease;
  border: 1rpx solid rgba(24, 144, 255, 0.1);
}

.stat-item:hover {
  transform: translateY(-2rpx);
  box-shadow: 0 8rpx 24rpx rgba(24, 144, 255, 0.15);
  border-color: rgba(24, 144, 255, 0.3);
}

.stat-item:nth-child(1) {
  background: linear-gradient(135deg, #f0f7ff 0%, #e6f7ff 100%);
}

.stat-item:nth-child(2) {
  background: linear-gradient(135deg, #e6f7ff 0%, #bae7ff 100%);
}

.stat-item:nth-child(3) {
  background: linear-gradient(135deg, #bae7ff 0%, #91d5ff 100%);
}

.stat-item:nth-child(4) {
  background: linear-gradient(135deg, #91d5ff 0%, #69c0ff 100%);
}

.stat-item:not(:last-child)::after {
  content: '';
  position: absolute;
  right: 0;
  top: 15%;
  height: 70%;
  width: 1rpx;
  background: rgba(24, 144, 255, 0.15);
}

.stat-num {
  font-size: 40rpx;
  font-weight: 600;
  color: #1890ff;
  margin-bottom: 12rpx;
  text-shadow: 0 2rpx 4rpx rgba(24, 144, 255, 0.1);
}

.stat-label {
  font-size: 26rpx;
  color: #1890ff;
  font-weight: 500;
  opacity: 0.9;
}

/* 快捷功能区样式 */
.index-icons {
  display: flex;
  flex-wrap: wrap;
  padding: 20rpx;
  background: #fff;
  margin: 20rpx;
  border-radius: 12rpx;
}

.flex-item {
  width: 25%;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 20rpx;
}

.image-icon {
  width: 80rpx;
  height: 80rpx;
  margin-bottom: 10rpx;
}

.text-icon {
  font-size: 24rpx;
  color: #333;
}

/* 最新更新样式 */
.index-row {
  background: #fff;
  margin: 20rpx;
  border-radius: 12rpx;
  padding: 20rpx;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 30rpx;
  margin: -20rpx -20rpx 20rpx -20rpx;
  background: linear-gradient(135deg, #e6f7ff 0%, #bae7ff 100%);
  border-radius: 12rpx 12rpx 0 0;
  border-bottom: 1rpx solid rgba(24, 144, 255, 0.1);
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #1890ff;
  position: relative;
  padding-left: 16rpx;
}

.section-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 4rpx;
  height: 24rpx;
  background-color: #1890ff;
  border-radius: 2rpx;
}

/* 更新筛选器样式 */
.update-filter {
  display: flex;
  gap: 16rpx;
}

.filter-item {
  padding: 8rpx 16rpx;
  font-size: 24rpx;
  color: #666;
  background: #f8f9fa;
  border-radius: 20rpx;
  border: 1rpx solid #e9ecef;
  transition: all 0.3s ease;
}

.filter-item.active {
  background: #1890ff;
  color: #fff;
  border-color: #1890ff;
}

.filter-item:active {
  transform: scale(0.95);
}

/* 更新列表样式 */
.update-list {
  display: flex;
  flex-direction: column;
}

.update-item {
  display: flex;
  align-items: center;
  padding: 24rpx 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  transition: all 0.3s ease;
  position: relative;
}

.update-item:last-child {
  border-bottom: none;
}

.update-item:active {
  background: #f8f9fa;
  transform: scale(0.98);
}

.update-type-badge {
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  font-size: 20rpx;
  font-weight: 500;
  margin-right: 20rpx;
  min-width: 60rpx;
  text-align: center;
}

.ticket-badge {
  background: #e6f7ff;
  color: #1890ff;
  border: 1rpx solid rgba(24, 144, 255, 0.2);
}

.notice-badge {
  background: #fff7e6;
  color: #fa8c16;
  border: 1rpx solid rgba(250, 140, 22, 0.2);
}

.update-content {
  flex: 1;
  margin-right: 20rpx;
}

.update-title {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 8rpx;
  font-weight: 500;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.update-desc {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 12rpx;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.update-meta {
  display: flex;
  align-items: center;
  gap: 16rpx;
  font-size: 22rpx;
  color: #999;
}

.update-author {
  padding: 4rpx 8rpx;
  background: #f8f9fa;
  border-radius: 8rpx;
  font-size: 20rpx;
}

.update-time {
  color: #999;
}

.update-status {
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
  font-size: 20rpx;
  background: #f0f0f0;
  color: #666;
}

.update-arrow {
  display: flex;
  align-items: center;
}

.arrow-text {
  font-size: 24rpx;
  color: #ccc;
  font-weight: bold;
}

/* 空状态样式 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 80rpx 40rpx;
  color: #999;
}

.empty-icon {
  font-size: 80rpx;
  margin-bottom: 24rpx;
  opacity: 0.6;
}

.empty-text {
  font-size: 26rpx;
  color: #999;
}

/* 待受理状态 */
.status-pending {
  background: #fff7e6;
  color: #fa8c16;
  border: 1rpx solid #ffd591;
}

/* 待回复状态 */
.status-waiting {
  background: #fff0f6;
  color: #eb2f96;
  border: 1rpx solid #ffadd2;
}

/* 已回复状态 */
.status-replied {
  background: #f6ffed;
  color: #52c41a;
  border: 1rpx solid #b7eb8f;
}

/* 处理中状态 */
.status-processing {
  background: #e6f7ff;
  color: #1890ff;
  border: 1rpx solid #91d5ff;
}

/* 已完毕状态 */
.status-completed {
  background: #f9f0ff;
  color: #722ed1;
  border: 1rpx solid #d3adf7;
}

/* 默认状态 */
.status-default {
  background: #f5f5f5;
  color: #999;
  border: 1rpx solid #d9d9d9;
}

/* 加载更多样式 */
.load-more {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20rpx 0;
  color: #999;
  font-size: 26rpx;
}

.loading {
  display: flex;
  align-items: center;
  justify-content: center;
}

.loading::before {
  content: '';
  width: 30rpx;
  height: 30rpx;
  margin-right: 10rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #1890ff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.no-more {
  color: #999;
}

.load-more-btn {
  color: #1890ff;
  padding: 10rpx 30rpx;
  border: 1rpx solid #1890ff;
  border-radius: 30rpx;
  background: #fff;
  transition: all 0.3s;
}

.load-more-btn:active {
  background: #e6f7ff;
  transform: scale(0.98);
}

/* 公告通知样式 */
.notice-list {
  display: flex;
  flex-direction: column;
}

.notice-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 20rpx 30rpx;
  border-bottom: 1rpx solid #eee;
}

.notice-item:nth-child(odd) {
  background-color: #fff;
}

.notice-item:nth-child(even) {
  background-color: #f7faff;
}

.notice-item:last-child {
  border-bottom: none;
}

.notice-info {
  flex: 1;
}

.notice-title {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
  display: block;
  position: relative;
  padding-left: 16rpx;
}

.notice-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 4rpx;
  height: 24rpx;
  background-color: #1890ff;
  border-radius: 2rpx;
}

.notice-desc {
  font-size: 24rpx;
  color: #999;
  display: block;
  margin-bottom: 10rpx;
}

.notice-meta {
  display: flex;
  justify-content: space-between;
  font-size: 24rpx;
  color: #999;
}

.notice-author {
  margin-right: 20rpx;
}

.notice-time {
  color: #999;
}

/* 退出登录按钮样式 */
.logout-section {
  padding: 30rpx 20rpx;
  background: #fff;
  margin-top: 20rpx;
}

.logout-btn {
  width: 100%;
  background: #ff4757;
  color: #fff;
  border: none;
  border-radius: 10rpx;
  padding: 20rpx;
  font-size: 28rpx;
  text-align: center;
}