<wxs module="filters">
// 格式化金额，保留两位小数
var formatMoney = function(value) {
  if (value == null || value === '' || isNaN(value)) return '0.00';
  return parseFloat(value).toFixed(2);
}

module.exports = {
  formatMoney: formatMoney
}
</wxs>

<view class="container" wx:if="{{isAdmin}}">
  <!-- 顶部统计卡片 -->
  <view class="finance-overview-card">
    <view class="card-header">
      <text class="card-title">财务概览</text>
      <text class="card-date">{{selectedCategoryName || '全部年度'}}</text>
    </view>
    <view class="balance-summary">
      <view class="current-balance">
        <text class="balance-label">当前余额</text>
        <text class="balance-value {{(initialBalance + balance) >= 0 ? 'positive' : 'negative'}}">¥{{filters.formatMoney(initialBalance + balance)}}</text>
      </view>
    </view>
    <view class="stats-container">
      <view class="stat-item">
        <text class="stat-label">期初值</text>
        <text class="stat-value">¥{{filters.formatMoney(initialBalance)}}</text>
      </view>
      <view class="stat-item">
        <text class="stat-label">总收入</text>
        <text class="stat-value positive">¥{{filters.formatMoney(totalIncome)}}</text>
      </view>
      <view class="stat-item">
        <text class="stat-label">总支出</text>
        <text class="stat-value negative">¥{{filters.formatMoney(totalExpense)}}</text>
      </view>
    </view>
  </view>

  <!-- 筛选和搜索区域 -->
  <view class="control-panel">
    <!-- 搜索框 -->
    <view class="search-bar">
      <view class="search-input-wrap">
        <image class="search-icon" src="/icons/search.png"></image>
        <input 
          class="search-input" 
          placeholder="搜索交易记录" 
          confirm-type="search"
          value="{{searchKeyword}}"
          bindinput="onSearchInput"
          bindconfirm="onSearchConfirm"
          maxlength="20"
          adjust-position="{{true}}"
        ></input>
        <view class="clear-icon" bindtap="clearSearch" wx:if="{{searchKeyword}}">×</view>
      </view>
      <view class="search-btn" bindtap="onSearchConfirm">搜索</view>
    </view>

    <!-- 筛选器 -->
    <view class="filter-btn" bindtap="toggleFilterPopup">
      <text>筛选</text>
      <text class="filter-icon"></text>
    </view>
  </view>

  <!-- 标签页 -->
  <view class="transaction-tabs">
    <view class="tab {{currentTab == 0 ? 'active' : ''}}" bindtap="switchTab" data-index="0">
      <text class="tab-text">收入</text>
      <text class="tab-amount">¥{{filters.formatMoney(totalIncome)}}</text>
    </view>
    <view class="tab {{currentTab == 1 ? 'active' : ''}}" bindtap="switchTab" data-index="1">
      <text class="tab-text">支出</text>
      <text class="tab-amount">¥{{filters.formatMoney(totalExpense)}}</text>
    </view>
  </view>

  <!-- 搜索提示 -->
  <view class="search-status" wx:if="{{isSearching && searchKeyword}}">
    <text>搜索"{{searchKeyword}}"，{{currentTab == 0 ? '收入' : '支出'}}结果<text class="result-count">{{currentTab == 0 ? incomeData.length : expenseData.length}}</text>条</text>
    <text class="clear-search" bindtap="clearSearch">清除</text>
  </view>

  <!-- 收入列表 -->
  <view class="transaction-list" wx:if="{{currentTab == 0}}">
    <block wx:if="{{incomeData.length > 0}}">
      <view class="transaction-item" wx:for="{{incomeData}}" wx:key="id" bindtap="viewDetail" data-id="{{item.id}}">
        <view class="transaction-info">
          <view class="transaction-main">
            <view class="transaction-title">{{item.title || '收入'}}</view>
            <view class="transaction-amount positive">+¥{{filters.formatMoney(item.money)}}</view>
          </view>
          <view class="transaction-meta">
            <view class="transaction-category">
            <text class="category-tag" wx:if="{{item.id}}">凭证号{{item.id}}</text>
              <text class="category-tag">{{item.catname || '无分类'}}</text>
              <text class="category-tag" wx:if="{{item.shouruleibie}}">{{item.shouruleibie}}</text>
              <text class="category-tag" wx:if="{{item.skfs}}">{{item.skfs}}</text>
            </view>
            <view class="transaction-time">{{item.inputtime || ''}}</view>
          </view>
        </view>
      </view>
    </block>
    <view class="empty-list" wx:else>
      <image class="empty-icon" src="/icons/no-data.png"></image>
      <text wx:if="{{isSearching}}">没有找到相关收入记录</text>
      <text wx:else>暂无收入记录</text>
    </view>
  </view>

  <!-- 支出列表 -->
  <view class="transaction-list" wx:if="{{currentTab == 1}}">
    <block wx:if="{{expenseData.length > 0}}">
      <view class="transaction-item" wx:for="{{expenseData}}" wx:key="id" bindtap="viewDetail" data-id="{{item.id}}">
        <view class="transaction-info">
          <view class="transaction-main">
            <view class="transaction-title">{{item.title || '支出'}}</view>
            <view class="transaction-amount negative">-¥{{filters.formatMoney(item.money)}}</view>
          </view>
          <view class="transaction-meta">
            <view class="transaction-category">
              <text class="category-tag">{{item.catname || '无分类'}}</text>
              <text class="category-tag" wx:if="{{item.zhichuleibie}}">{{item.zhichuleibie}}</text>
              <text class="category-tag" wx:if="{{item.fkfs}}">{{item.fkfs}}</text>
            </view>
            <view class="transaction-time">{{item.inputtime || ''}}</view>
          </view>
        </view>
      </view>
    </block>
    <view class="empty-list" wx:else>
      <image class="empty-icon" src="/icons/no-data.png"></image>
      <text wx:if="{{isSearching}}">没有找到相关支出记录</text>
      <text wx:else>暂无支出记录</text>
    </view>
  </view>

  <!-- 加载更多提示 -->
  <view class="loading-status">
    <view class="loading-indicator" wx:if="{{loading}}">
      <view class="loading-spinner"></view>
      <text>加载中...</text>
    </view>
    <view class="no-more-data" wx:elif="{{!hasMore}}">没有更多数据了</view>
  </view>

  <!-- 添加按钮 -->
  <view class="fab-button" bindtap="addFinancialRecord">
    <text class="fab-icon">+</text>
  </view>
  
  <!-- 筛选弹窗 -->
  <view class="filter-mask" wx:if="{{showFilterPopup}}" bindtap="toggleFilterPopup" catchtouchmove="preventTouchMove"></view>
  <view class="filter-panel" wx:if="{{showFilterPopup}}" catchtouchmove="preventTouchMove">
    <view class="panel-header">
      <text class="panel-title">年度筛选</text>
      <view class="panel-close" hover-class="close-hover" bindtap="toggleFilterPopup">×</view>
    </view>
    <view class="panel-content">
      <view 
        wx:for="{{categoryList}}" 
        wx:key="id" 
        class="filter-option {{selectedCategory == item.id ? 'selected' : ''}}" 
        hover-class="option-hover"
        bindtap="selectCategory" 
        data-id="{{item.id}}"
        data-name="{{item.name}}">
        <text>{{item.name}}</text>
        <text class="check-mark" wx:if="{{selectedCategory == item.id}}">✓</text>
      </view>
    </view>
    <view class="panel-footer">
      <button class="btn-apply" bindtap="toggleFilterPopup">确定</button>
    </view>
  </view>
</view>

<!-- 非管理员提示 -->
<view class="no-permission" wx:else>
  <image src="/icons/lock.png" class="lock-icon"></image>
  <view class="no-permission-text">您没有访问财务管理的权限</view>
  <view class="permission-desc">请联系管理员开通权限</view>
</view> 