<!--pages/wenda/detail.wxml-->
<!-- 导入WxParse模板 -->
<import src="../../wxParse/wxParse.wxml"/>

<view class="container">
  <!-- 详情头部区域 -->
  <view class="detail-card">
    <view class="detail-header">
      <view class="detail-title">{{detail.title}}</view>
      <view class="detail-info">
        <text class="detail-info-text author">发布人: {{detail.author}}</text>
        <text class="detail-info-text post-time">发布时间: {{detail.inputtime}}</text>
        <text class="detail-info-text status {{detail.status_value == 1 ? 'solved' : (detail.status_value == 2 ? 'inprocess' : 'unsolved')}}">状态: {{detail.status_text}}</text>
      </view>
      
      <!-- 添加房产基本信息 -->
      <view class="property-info" wx:if="{{detail.zongdidaima || detail.jmzjfbh || detail.zongdimianji || detail.jzmj || detail.fangwujiegou || detail.cengshu || detail.jcnf}}">
        <view class="info-title"><view class="info-title-prefix"></view>房产基本信息</view>
        <view class="property-grid">
          <view class="property-grid-item" wx:if="{{detail.zongdidaima}}">
            <view class="property-label">宗地代码</view>
            <view class="property-value">{{detail.zongdidaima}}</view>
          </view>
          <view class="property-grid-item long-item" wx:if="{{detail.jmzjfbh}}">
            <view class="property-label">简明编号</view>
            <view class="property-value">{{detail.jmzjfbh}}</view>
          </view>
          <view class="property-grid-item" wx:if="{{detail.zongdimianji}}">
            <view class="property-label">宗地面积</view>
            <view class="property-value">{{detail.zongdimianji}}㎡</view>
          </view>
          <view class="property-grid-item" wx:if="{{detail.jzmj}}">
            <view class="property-label">建筑面积</view>
            <view class="property-value">{{detail.jzmj}}㎡</view>
          </view>
          <view class="property-grid-item" wx:if="{{detail.fangwujiegou}}">
            <view class="property-label">房屋结构</view>
            <view class="property-value">{{detail.fangwujiegou}}</view>
          </view>
          <view class="property-grid-item" wx:if="{{detail.cengshu}}">
            <view class="property-label">层数</view>
            <view class="property-value">{{detail.cengshu}}层</view>
          </view>
          <view class="property-grid-item" wx:if="{{detail.jcnf}}">
            <view class="property-label">建成年份</view>
            <view class="property-value">{{detail.jcnf}}</view>
          </view>
          <view class="property-grid-item long-item" wx:if="{{detail.lsdjzh}}">
            <view class="property-label">历史登记证号</view>
            <view class="property-value">{{detail.lsdjzh}}</view>
          </view>
        </view>
      </view>
    </view>

    <!-- 添加点赞功能 -->
    <view class="digg-container" wx:if="{{detail}}">
      <view class="digg-buttons">
        <view class="digg-item helpful" catchtap="moduleDigg" data-id="{{detail.id}}" data-value="1">
          <image src="../../icons/up.png" class="digg-icon"></image>
          <text class="detail-info-text">有帮助（<text id="digg_{{detail.id}}_1" class="detail-info-text">{{detail.like_count || detail.support || '-'}}</text>）</text>
        </view>
        <view class="digg-item unhelpful" catchtap="moduleDigg" data-id="{{detail.id}}" data-value="0">
          <image src="../../icons/shutdown.png" class="digg-icon"></image>
          <text class="detail-info-text">没帮助（<text id="digg_{{detail.id}}_0" class="detail-info-text">{{detail.unlike_count || detail.oppose || '-'}}</text>）</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 问题内容区域 - 只在有内容时显示 -->
  <view class="content-section" wx:if="{{detail.content}}">
    <view class="content-header"><view class="content-header-prefix"></view>问题详情</view>
    <view class="content-body">
      <template is="wxParse" data="{{wxParseData:article.nodes}}"/>
    </view>
  </view>

  <!-- 权属来源证明 - 只在有值时显示 -->
  <view class="document-section" wx:if="{{detail.qslyzm}}">
    <view class="section-title"><view class="section-title-prefix"></view>权属来源证明</view>
    <view class="document-item">
      <image src="{{detail.qslyzm}}" mode="widthFix" class="document-image" bindtap="previewSingleImage" data-url="{{detail.qslyzm}}"></image>
    </view>
  </view>

  <!-- 不动产权籍调查表 - 只在有值时显示 -->
  <view class="document-section" wx:if="{{detail.bdcdjb}}">
    <view class="section-title"><view class="section-title-prefix"></view>不动产权籍调查表</view>
    <view class="document-item">
      <view class="document-link" bindtap="openDocument" data-url="{{detail.bdcdjb}}">查看调查表文档</view>
    </view>
  </view>

  <!-- 身份证明材料 - 只在有值时显示 -->
  <view class="document-section" wx:if="{{detail.sfzmcl && detail.sfzmcl.length > 0}}">
    <view class="section-title"><view class="section-title-prefix"></view>身份证明材料</view>
    <view class="image-grid">
      <view class="image-item" wx:for="{{detail.sfzmcl}}" wx:key="url" bindtap="previewDocImage" data-index="{{index}}" data-images="{{detail.sfzmcl}}">
        <image src="{{item.url}}" mode="aspectFill" class="preview-image"></image>
      </view>
    </view>
  </view>

  <!-- 摸底调查表 - 只在有值时显示 -->
  <view class="document-section" wx:if="{{detail.mddcb && detail.mddcb.length > 0}}">
    <view class="section-title"><view class="section-title-prefix"></view>摸底调查表</view>
    <view class="image-grid">
      <view class="image-item" wx:for="{{detail.mddcb}}" wx:key="url" bindtap="previewDocImage" data-index="{{index}}" data-images="{{detail.mddcb}}">
        <image src="{{item.url}}" mode="aspectFill" class="preview-image"></image>
      </view>
    </view>
  </view>

  <!-- 不动产单元图 - 只在有值时显示 -->
  <view class="document-section" wx:if="{{detail.bdcdyt && detail.bdcdyt.length > 0}}">
    <view class="section-title"><view class="section-title-prefix"></view>不动产单元图</view>
    <view class="image-grid">
      <view class="image-item" wx:for="{{detail.bdcdyt}}" wx:key="url" bindtap="previewDocImage" data-index="{{index}}" data-images="{{detail.bdcdyt}}">
        <image src="{{item.url}}" mode="aspectFill" class="preview-image"></image>
        <view class="image-title" wx:if="{{item.title}}">{{item.title}}</view>
      </view>
    </view>
  </view>

  <!-- 反馈信息区 -->
  <view class="section feedback-section" wx:if="{{detail.fankui && detail.fankui.length > 0}}">
    <view class="section-header">
      <view class="section-title-container">
        <text class="section-title">相关信息</text>
      </view>
      <view class="section-badge">{{detail.fankui.length}}条</view>
    </view>
    <view class="section-content">
      <view class="feedback-list">
        <view class="feedback-item" wx:for="{{detail.fankui}}" wx:key="id" bindtap="navigateToFankui" data-url="{{item.url}}" data-id="{{item.id}}">
          <view class="feedback-dot"></view>
          <view class="feedback-title">{{item.title}}</view>
          <text class="feedback-arrow feedback-arrow-text">›</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 相关展品图片区域 - 只在有图片时显示 -->
  <view class="image-section" wx:if="{{relatedImages && relatedImages.length > 0}}">
    <view class="section-title"><view class="section-title-prefix"></view>相关展品</view>
    <view class="image-grid">
      <view class="image-item" wx:for="{{relatedImages}}" wx:key="id" bindtap="previewRelatedImage" data-index="{{index}}">
        <image src="{{item.file}}" mode="aspectFill" class="preview-image"></image>
      </view>
    </view>
  </view>

  <!-- 原图展品图片区域 - 只在有图片时显示 -->
  <view class="image-section" wx:if="{{originalImages && originalImages.length > 0}}">
    <view class="section-title"><view class="section-title-prefix"></view>原图展品</view>
    <view class="image-grid">
      <view class="image-item" wx:for="{{originalImages}}" wx:key="id" bindtap="previewOriginalImage" data-index="{{index}}">
        <image src="{{item.file}}" mode="aspectFill" class="preview-image"></image>
      </view>
    </view>
  </view>

  <!-- 页面底部信息区域 -->
  <view class="footer-info" wx:if="{{detail.updatetime || detail.hits}}">
    <view class="footer-item" wx:if="{{detail.updatetime}}">
      <text class="detail-info-text footer-label">更新时间:</text>
      <text class="detail-info-text footer-value">{{detail.updatetime}}</text>
    </view>
    <view class="footer-item" wx:if="{{detail.hits}}">
      <text class="detail-info-text footer-label">浏览量:</text>
      <text class="detail-info-text footer-value">{{detail.hits}}</text>
    </view>
  </view>

  <!-- 评论区域 -->
  <view class="section comments-section">
    <view class="section-header">
      <view class="section-title-container">
        <text class="section-title">评论记录</text>
      </view>
      <view class="section-badge">{{commentList.length || 0}}条</view>
    </view>
    
    <view class="section-content">
      <view class="comments-list">
        <block wx:if="{{commentList.length === 0}}">
          <view class="comments-empty">
            <image src="../../icons/news-pl.png" class="empty-icon"></image>
            <text class="detail-info-text empty-text">点击底部"评论"添加评论</text>
          </view>
        </block>
        <block wx:else>
          <view 
            class="comment-item"
            wx:for="{{commentList}}" 
            wx:key="uniqueKey"
          >
            <view class="comment-item-header">
              <image 
                class="comment-item-avatar" 
                src="{{item.avatar || '../../images/avatar.png'}}" 
                mode="aspectFill" 
                lazy-load
              />
              <view class="comment-item-meta">
                <view class="comment-item-author">
                  {{item.author || '匿名用户'}}
                  <text class="detail-info-text comment-item-tag" wx:if="{{item.uid == member.uid}}">我</text>
                </view>
                <text class="detail-info-text comment-item-time">{{item.inputtime}}</text>
              </view>
            </view>
            <view class="comment-item-content">
              <!-- 使用wxParse模板渲染评论内容 -->
              <template is="wxParse" data="{{wxParseData:item.parsedContent.nodes}}"/>
              
              <!-- 评论图片 -->
              <view class="comment-images" wx:if="{{item.images && item.images.length > 0}}">
                <view 
                  class="comment-image-item" 
                  wx:for="{{item.images}}" 
                  wx:for-item="imgUrl" 
                  wx:for-index="imgIndex" 
                  wx:key="imgIndex"
                  bindtap="previewImage"
                  data-current="{{imgUrl}}"
                  data-urls="{{item.images}}"
                >
                  <image src="{{imgUrl}}" mode="aspectFill" class="comment-image" lazy-load />
                </view>
              </view>
            </view>
          </view>
        </block>
      </view>

      <!-- 加载状态 -->
      <view class="loading-status" wx:if="{{isLoading || (!hasMore && commentList.length > 0)}}">
        <view class="loading-more" wx:if="{{isLoading}}">
          <view class="loading-spinner small"></view>
          <text class="detail-info-text">加载中...</text>
        </view>
        <view class="loading-end" wx:if="{{!hasMore && !isLoading && commentList.length > 0}}">
          <text class="detail-info-text">没有更多评论了</text>
        </view>
      </view>
    </view>
  </view>
  
  <!-- 底部间距 -->
  <view class="bottom-spacer"></view>
</view>

<!-- 底部操作栏 -->
<view class="action-bar">
  <button class="action-button back-button" bindtap="goBack">
    <text class="detail-info-text button-icon"><text class="back-icon-content"></text></text>
    <text class="detail-info-text button-text">返回</text>
  </button>
  <view class="comment-input-area" bindtap="showFullCommentBar">
    <view class="comment-input-box">
      <text class="detail-info-text input-placeholder">点击添加评论...</text>
    </view>
  </view>
  <button class="action-button share-button" open-type="share">
    <text class="detail-info-text button-icon"><text class="share-icon-content"></text></text>
  </button>
</view>

<!-- 评论弹出层 -->
<view class="comment-modal {{isCommentBarShow ? 'comment-modal-show' : ''}}">
  <view class="comment-modal-header">
    <text class="detail-info-text modal-title">添加评论</text>
    <view class="comment-modal-close" bindtap="hideFullCommentBar">
      <text class="detail-info-text close-icon">×</text>
    </view>
  </view>
  
  <view class="comment-modal-body">
    <textarea 
      class="comment-modal-textarea"
      placeholder="请输入评论内容..." 
      bindinput="getText"
      value="{{commentText}}"
      fixed
      maxlength="2000"
      show-confirm-bar="{{false}}"
    />
    
    <!-- 图片预览 -->
    <block wx:if="{{tempImagePaths.length > 0}}">
      <view class="image-preview">
        <view 
          class="image-preview-item" 
          wx:for="{{tempImagePaths}}" 
          wx:key="index"
        >
          <image src="{{item}}" class="image-preview-img" mode="aspectFill"></image>
          <view 
            class="image-preview-delete" 
            catchtap="deleteImage" 
            data-index="{{index}}"
          >×</view>
        </view>
      </view>
    </block>
  </view>

  <view class="comment-modal-footer">
    <view class="comment-modal-tools">
      <button class="upload-btn" bindtap="chooseImage">
        <text class="detail-info-text upload-icon"><text class="upload-icon-content"></text></text>
        <text class="detail-info-text">添加图片</text>
        <block wx:if="{{tempImagePaths.length > 0}}">
          <text class="detail-info-text image-count">({{tempImagePaths.length}})</text>
        </block>
      </button>
    </view>
    <view class="comment-modal-actions">
      <button 
        class="comment-modal-btn comment-modal-btn-cancel" 
        bindtap="hideFullCommentBar"
      >取消</button>
      <button 
        class="comment-modal-btn comment-modal-btn-submit {{commentText || tempImagePaths.length > 0 ? 'is-active' : ''}}" 
        bindtap="saveComment"
      >发表</button>
    </view>
  </view>
</view>