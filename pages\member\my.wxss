@import "../member/index.wxss";

.user-name{
    padding-left: 30rpx;
}
.user-info{
    margin-bottom: 0;
}
.avatar-wrapper {  right:70rpx;  position:absolute; padding:0;width:114rpx;height:114rpx;}
.avatar-wrapper .user-logo{
  margin:0rpx;
}
.userInfo-warp{
    border-bottom: 1px solid #e7e7e7;
    padding: 30rpx 30rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 36rpx;
    background: #fff;
    position: relative;
}
.userInfo-warp .nickname {
  right:70rpx;height:90%;  position:absolute; text-align: right;
}
.mb20{
 margin-bottom: 20rpx;
}
.mr40{
    margin-right: 40rpx;
}

/* 全局容器 */
page {
  height: 100%;
  background-color: #f7f7f7;
}

.container {
  min-height: 100vh;
  background-color: #f7f7f7;
  position: relative;
  padding-bottom: 40rpx;
  box-sizing: border-box;
}

/* 顶部背景 */
.top-bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 400rpx;
  background: linear-gradient(180deg, #1677ff, #1668dc);
  z-index: 0;
}

/* 页面头部 */
.page-header {
  margin-bottom: 30rpx;
}

.header-title {
  font-size: 40rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.header-subtitle {
  font-size: 28rpx;
  color: #888;
}

/* 用户信息卡片 */
.user-card {
  margin: 30rpx;
  border-radius: 20rpx;
  background-color: #fff;
  box-shadow: 0 6rpx 20rpx rgba(22, 119, 255, 0.08);
  padding: 30rpx;
  position: relative;
  z-index: 1;
}

.user-info-content {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
  position: relative;
}

/* 头像区域 */
.avatar-wrapper {
  width: 140rpx !important;
  height: 140rpx !important;
  border-radius: 70rpx !important;
  position: relative !important;
  padding: 0 !important;
  margin: 0 30rpx 0 0 !important;
  background: none !important;
  border: none !important;
  line-height: 1 !important;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  right: auto !important;
}

.avatar-wrapper::after {
  border: none;
}

.user-avatar {
  width: 140rpx !important;
  height: 140rpx !important;
  border-radius: 70rpx !important;
  border: 6rpx solid rgba(255, 255, 255, 0.9) !important;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1) !important;
}

.edit-avatar-icon {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 44rpx;
  height: 44rpx;
  border-radius: 22rpx;
  background-color: #fff;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.15);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2;
}

.camera-icon {
  width: 28rpx;
  height: 28rpx;
  opacity: 0.7;
}

/* 用户详情区域 */
.user-details {
  flex: 1;
  min-width: 0;
  padding-top: 10rpx;
}

.nickname-input {
  font-size: 40rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 12rpx;
  padding: 0;
  border: none;
  width: 100%;
  height: 56rpx;
  line-height: 56rpx;
  background: transparent;
}

.account-id {
  font-size: 26rpx;
  color: #999;
  display: block;
}

/* 账户数据统计 */
.account-stats {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 0 0;
  margin-top: 24rpx;
  border-top: 1rpx solid #f0f0f0;
}

.stat-item {
  flex: 1;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-value {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 26rpx;
  color: #999;
}

.stat-divider {
  width: 2rpx;
  height: 48rpx;
  background-color: #f0f0f0;
  margin: 0 20rpx;
}

/* 区块标题 */
.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin: 40rpx 30rpx 20rpx;
  position: relative;
  display: flex;
  align-items: center;
}

.section-title::before {
  content: '';
  width: 6rpx;
  height: 32rpx;
  background: #1677ff;
  border-radius: 6rpx;
  margin-right: 16rpx;
  display: inline-block;
}

/* 信息列表 */
.info-list {
  background: #fff;
  border-radius: 16rpx;
  margin: 0 30rpx;
  padding: 0 30rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.03);
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 0;
  border-bottom: 1rpx solid #f5f5f5;
}

.info-item:last-child {
  border-bottom: none;
}

.info-label {
  display: flex;
  align-items: center;
  color: #666;
  font-size: 28rpx;
}

.info-icon {
  width: 36rpx;
  height: 36rpx;
  margin-right: 12rpx;
}

.info-value {
  flex: 1;
  text-align: right;
  color: #333;
  font-size: 28rpx;
}

/* 会员组标签 */
.group-tag {
  display: inline-block;
  padding: 4rpx 16rpx;
  background: rgba(22, 119, 255, 0.1);
  color: #1677ff;
  border-radius: 100rpx;
  font-size: 24rpx;
  margin-left: 12rpx;
}

.group-tag:first-child {
  margin-left: 0;
}

/* 状态标识样式 */
.status-badge {
  padding: 4rpx 16rpx;
  border-radius: 6rpx;
  font-size: 24rpx;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 100rpx;
  background-color: #f5f5f5;
  border: 1rpx solid #d9d9d9;
}

.status-badge text {
  color: #999;
}

/* 激活状态样式 */
.status-badge.verify.active {
  background-color: #f6ffed;
  border: 1rpx solid #b7eb8f;
}

.status-badge.verify.active text {
  color: #52c41a;
}

.status-badge.mobile.active {
  background-color: #e6f7ff;
  border: 1rpx solid #91d5ff;
}

.status-badge.mobile.active text {
  color: #1890ff;
}

.status-badge.email.active {
  background-color: #f9f0ff;
  border: 1rpx solid #d3adf7;
}

.status-badge.email.active text {
  color: #722ed1;
}

.status-badge.avatar.active {
  background-color: #fff7e6;
  border: 1rpx solid #ffd591;
}

.status-badge.avatar.active text {
  color: #fa8c16;
}

.status-badge.complete.active {
  background-color: #f0f5ff;
  border: 1rpx solid #adc6ff;
}

.status-badge.complete.active text {
  color: #2f54eb;
}

.status-badge.lock.active {
  background-color: #fff1f0;
  border: 1rpx solid #ffa39e;
}

.status-badge.lock.active text {
  color: #f5222d;
}

/* 会员状态标识样式 */
.status-list {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.status-item {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.status-label {
  font-size: 24rpx;
  color: #666;
  margin-right: 12rpx;
}

/* 导航点击效果 */
.navigator-hover {
  background-color: transparent;
}

.navigator-hover .info-item {
  background-color: #f9f9f9;
}

/* 账户数据统计 - 新的 profile-stats 样式 */
.profile-stats {
  display: flex;
  flex-direction: column;
  background-color: rgba(22, 119, 255, 0.05);
  border-radius: 16rpx;
  padding: 20rpx 10rpx;
  margin-top: 24rpx;
  border-top: 1rpx solid #f0f0f0;
}

.profile-stats-row {
  display: flex;
  justify-content: space-between;
  padding: 12rpx 0;
}

.profile-row-divider {
  height: 1rpx;
  background-color: rgba(22, 119, 255, 0.1);
  margin: 10rpx 20rpx;
}

.profile-stat-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.profile-stat-value {
  font-size: 32rpx;
  font-weight: 600;
  color: #1677ff;
  max-width: 200rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  text-align: center;
}

.profile-stat-label {
  font-size: 24rpx;
  color: #666;
  margin-top: 6rpx;
}

.profile-stat-divider {
  width: 1rpx;
  height: 50rpx;
  background-color: rgba(22, 119, 255, 0.1);
}