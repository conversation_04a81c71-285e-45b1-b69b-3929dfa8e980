@import "../login/login.wxss";

.userPwd{
    margin-top: 0;
    margin-bottom: 30rpx;
}
.warp-pos image.yz-pos{
    width: 190rpx;
    height: 55rpx;
     margin-top: -27.5rpx;
}
.fw{
    font-size: 26rpx;
    color: #bcbdc1;
    text-align: center;
    margin-top: 40rpx;
}
.fw text{
     color: #389fcf;
}

.container {
  padding: 40rpx 40rpx;
  background-color: #fff;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.header {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 40rpx;
  padding: 20rpx 0;
}

.logo {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 20rpx;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.header-title {
  font-size: 46rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
  letter-spacing: 2rpx;
}

.header-subtitle {
  font-size: 28rpx;
  color: #888;
  letter-spacing: 1rpx;
}

.form-group {
  margin-bottom: 15rpx;
}

.form-label {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 8rpx;
  font-weight: 500;
}

.input-group {
  position: relative;
  display: flex;
  align-items: center;
  border: 1rpx solid #e8e8e8;
  border-radius: 12rpx;
  padding: 6rpx 20rpx;
  background-color: #f9f9f9;
  transition: all 0.3s;
  height: 80rpx;
  box-sizing: border-box;
}

.input-group:focus-within {
  border-color: #1890ff;
  background-color: #fff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
}

.input-group.error {
  border-color: #ff4d4f;
  background-color: #fff;
}

.input-icon {
  width: 36rpx;
  height: 36rpx;
  margin-right: 16rpx;
  opacity: 0.7;
}

.input {
  flex: 1;
  height: 100%;
  font-size: 28rpx;
  color: #333;
  padding-right: 20rpx;
}

.btn-eye {
  padding: 10rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.eye-icon {
  width: 44rpx;
  height: 44rpx;
  opacity: 0.7;
}

.error-message {
  font-size: 24rpx;
  color: #ff4d4f;
  margin-top: 5rpx;
  min-height: 24rpx;
  padding-left: 10rpx;
}

.agreement {
  display: flex;
  align-items: flex-start;
  margin: 20rpx 0 30rpx;
  padding: 0 10rpx;
}

.agreement-checkbox {
  transform: scale(0.8);
  margin-right: 10rpx;
  margin-top: 2rpx;
}

.agreement-text {
  font-size: 26rpx;
  color: #666;
  flex: 1;
  line-height: 1.5;
}

.link {
  color: #1890ff;
  display: inline;
}

.btn-submit {
  width: 100%;
  height: 90rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #1890ff, #0050b3);
  color: #fff;
  font-size: 32rpx;
  font-weight: 500;
  border-radius: 12rpx;
  margin-top: 10rpx;
  margin-bottom: 30rpx;
  letter-spacing: 2rpx;
  transition: opacity 0.3s;
  box-shadow: 0 8rpx 16rpx rgba(24, 144, 255, 0.2);
  border: none;
}

.btn-submit::after {
  border: none;
}

.btn-hover {
  opacity: 0.9;
  transform: translateY(2rpx);
}

.btn-submit.disabled {
  background: linear-gradient(135deg, #bfbfbf, #8c8c8c);
  color: #f5f5f5;
  box-shadow: none;
}

.login-link {
  text-align: center;
  font-size: 28rpx;
  color: #666;
  margin-top: 40rpx;
  padding-bottom: 40rpx;
}

.login-text {
  color: #1890ff;
  margin-left: 10rpx;
  font-weight: 500;
}

.linkage-group {
  display: flex;
  justify-content: space-between;
  gap: 20rpx;
  border-radius: 12rpx;
}

.linkage-group.error .linkage-level .picker {
  border-color: #ff4d4f;
  background-color: #fff;
}

.linkage-level {
  flex: 1;
}

.linkage-level .picker {
  display: flex;
  align-items: center;
  height: 80rpx;
  border: 1rpx solid #e8e8e8;
  border-radius: 12rpx;
  padding: 6rpx 20rpx;
  background-color: #f9f9f9;
  font-size: 28rpx;
  color: #333;
  box-sizing: border-box;
  transition: all 0.3s;
}

.linkage-level .picker:active {
  background-color: #f0f0f0;
}

.linkage-level .picker.placeholder {
  color: #999;
}

.linkage-level .picker.disabled {
  opacity: 0.6;
  background-color: #f5f5f5;
}

.linkage-level .picker text {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

