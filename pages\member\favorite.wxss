@import "../workorder/list.wxss";

.container {
  min-height: 100vh;
  background-color: #f5f7fa;
  padding: 24rpx;
}

/* 列表样式 */
.content-list {
  height: calc(100vh - 48rpx);
}

.list-content {
  padding: 0;
}

.card {
  border-radius: 16rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.card::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 8rpx;
  background: #1890ff;
  opacity: 0.7;
}

/* 隔行不同色 */
.card-odd {
  background: #ffffff;
}

.card-even {
  background: #f5f8ff;
}

.card-content {
  padding: 32rpx;
}

.card-hover {
  transform: translateY(-2rpx);
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
}

.card-title {
  font-size: 32rpx;
  color: #333333;
  line-height: 1.5;
  margin-bottom: 20rpx;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  font-weight: 500;
}

.card-meta {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  margin-top: 16rpx;
}

.meta-item {
  display: flex;
  align-items: center;
  margin-right: 32rpx;
  margin-bottom: 4rpx;
}

.meta-icon {
  width: 28rpx;
  height: 28rpx;
  margin-right: 8rpx;
  opacity: 0.45;
  transition: opacity 0.2s ease;
}

.meta-text {
  font-size: 26rpx;
  color: #666666;
  font-weight: 400;
}

/* 空状态样式 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 240rpx 0;
  background: transparent;
}

.empty-icon {
  width: 180rpx;
  height: 180rpx;
  margin-bottom: 24rpx;
  opacity: 0.4;
  transition: opacity 0.3s ease;
}

.empty-text {
  font-size: 28rpx;
  color: #8c8c8c;
  letter-spacing: 1rpx;
  margin-bottom: 16rpx;
}

.empty-tip {
  font-size: 26rpx;
  color: #999999;
  margin-top: 16rpx;
}

/* 加载状态样式 */
.loading-state {
  padding: 32rpx 0;
  text-align: center;
}

.loading-content {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.9);
  padding: 16rpx 32rpx;
  border-radius: 32rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.loading-icon {
  width: 34rpx;
  height: 34rpx;
  margin-right: 10rpx;
}

.loading-text {
  font-size: 26rpx;
  color: #666666;
  font-weight: 400;
}

.no-more {
  padding: 32rpx 0;
  text-align: center;
}

.no-more-text {
  font-size: 26rpx;
  color: #999999;
  font-weight: 400;
  position: relative;
  display: inline-block;
  padding: 0 32rpx;
}

.no-more-text::before,
.no-more-text::after {
  content: '';
  position: absolute;
  top: 50%;
  width: 60rpx;
  height: 1rpx;
  background: #e8e8e8;
}

.no-more-text::before {
  left: -40rpx;
}

.no-more-text::after {
  right: -40rpx;
}