<view class="container">
  <view class="account-card">
    <view class="account-header">
      <text class="account-title">个人资料</text>
      <text class="account-uid">UID: {{member.uid || member.username}}</text>
    </view>

    <form bindsubmit="formBindsubmit">
      <!-- 账户信息部分 -->
      <view class="form-group">
        <view class="group-title">
          <view class="title-line"></view>
          <text>基本信息</text>
        </view>
        
        <!-- 昵称 -->
        <view class="form-item">
          <text class="form-label">昵称</text>
          <input 
            class="form-input" 
            placeholder="请输入昵称" 
            name="name" 
            value="{{member.name}}"
          />
        </view>
        
        <!-- 手机号 -->
        <view class="form-item">
          <text class="form-label">手机号</text>
          <view class="form-input disabled">
            <text>{{member.phone || '未绑定'}}</text>
            <view class="action-btn" bindtap="bindPhone" wx:if="{{!member.phone}}">绑定</view>
          </view>
        </view>
        
        <!-- 邮箱 -->
        <view class="form-item">
          <text class="form-label">电子邮箱</text>
          <input 
            class="form-input" 
            placeholder="请输入电子邮箱" 
            name="email" 
            value="{{member.email}}"
            type="email"
          />
        </view>
      </view>
      
      <!-- 个人资料部分 -->
      <view class="form-group">
        <view class="group-title">
          <view class="title-line"></view>
          <text>个人资料</text>
        </view>
        
        <!-- 性别 -->
        <view class="form-item">
          <text class="form-label">性别</text>
          <radio-group class="radio-group" name="sex">
            <label class="radio">
              <radio value="1" checked="{{member.sex == 1}}" />
              <text>男</text>
            </label>
            <label class="radio">
              <radio value="2" checked="{{member.sex == 2}}" />
              <text>女</text>
            </label>
            <label class="radio">
              <radio value="0" checked="{{!member.sex || member.sex == 0}}" />
              <text>保密</text>
            </label>
          </radio-group>
        </view>
        
        <!-- 所属村组 -->
        <view class="form-item">
          <text class="form-label">所属村组</text>
          <view class="linkage-group">
            <view class="linkage-level">
              <picker bindchange="changeSsczLevel1" value="{{sscz_level1}}" range="{{ssczLevel1Options}}" range-key="region_name">
                <view class="picker {{!sscz_level1 ? 'placeholder' : ''}}">
                  <text>{{sscz_level1 !== '' ? (ssczLevel1Options[sscz_level1].region_name || '请选择') : '请选择村'}}</text>
                  <image class="picker-arrow" src="../../icons/right-back.png" mode="aspectFit"></image>
                </view>
              </picker>
            </view>
            <view class="linkage-level">
              <picker bindchange="changeSsczLevel2" value="{{sscz_level2}}" range="{{ssczLevel2Options}}" range-key="region_name" disabled="{{!sscz_level1 || ssczLevel2Options.length === 0}}">
                <view class="picker {{!sscz_level2 ? 'placeholder' : ''}} {{!sscz_level1 || ssczLevel2Options.length === 0 ? 'disabled' : ''}}">
                  <text>{{sscz_level2 !== '' ? (ssczLevel2Options[sscz_level2].region_name || '请选择') : '请选择组'}}</text>
                  <image class="picker-arrow" src="../../icons/right-back.png" mode="aspectFit"></image>
                </view>
              </picker>
            </view>
          </view>
        </view>
        
        <!-- 个人介绍 -->
        <view class="form-item">
          <text class="form-label">个人介绍</text>
          <textarea 
            class="form-textarea" 
            placeholder="请输入个人介绍" 
            name="description" 
            value="{{member.description}}"
            maxlength="200"
          ></textarea>
          <view class="textarea-counter">{{member.description ? member.description.length : 0}}/200</view>
        </view>
      </view>

      <!-- 提交按钮 -->
      <button class="submit-btn" formType="submit">保存修改</button>
    </form>
  </view>
  
  <!-- 注册时间信息 -->
  <view class="account-info">
    <view class="info-item">
      <text class="info-label">注册时间</text>
      <text class="info-value">{{member.regtime || '-'}}</text>
    </view>
    <view class="info-item">
      <text class="info-label">最后登录</text>
      <text class="info-value">{{member.lastlogintime || '-'}}</text>
    </view>
  </view>
</view>

