@import "../login/login.wxss";
@import "../member/index.wxss";
.userPwd{
    margin-top: 0;
    margin-bottom: 30rpx;
}
.warp-pos image.yz-pos{
    width: 190rpx;
    height: 55rpx;
     margin-top: -27.5rpx;
}
.fw{
    font-size: 26rpx;
    color: #bcbdc1;
    text-align: center;
    margin-top: 40rpx;
}
.fw text{
     color: #389fcf;
}
.login-username-info {
  margin-bottom: 70rpx;
  text-align: center
}

.container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 20rpx;
  box-sizing: border-box;
}

.account-card {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  margin: 0 auto;
  width: 100%;
  box-sizing: border-box;
  margin-bottom: 20rpx;
}

.account-header {
  margin-bottom: 40rpx;
}

.account-title {
  font-size: 36rpx;
  color: #333;
  font-weight: 600;
  margin-bottom: 16rpx;
  display: block;
}

.account-uid {
  font-size: 28rpx;
  color: #999;
}

/* 表单组样式 */
.form-group {
  margin-bottom: 40rpx;
}

.group-title {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
}

.title-line {
  width: 8rpx;
  height: 32rpx;
  background: #1890ff;
  margin-right: 16rpx;
  border-radius: 4rpx;
}

.group-title text {
  font-size: 30rpx;
  color: #333;
  font-weight: 500;
}

.form-item {
  margin-bottom: 30rpx;
  width: 100%;
  box-sizing: border-box;
}

.form-label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 16rpx;
  display: block;
}

.form-input {
  width: 100%;
  height: 88rpx;
  background: #f5f5f5;
  border-radius: 12rpx;
  padding: 0 24rpx;
  font-size: 28rpx;
  color: #333;
  box-sizing: border-box;
  display: flex;
  align-items: center;
}

.form-input.disabled {
  color: #999;
  background: #f9f9f9;
  justify-content: space-between;
}

.form-input::placeholder {
  color: #999;
}

.action-btn {
  font-size: 26rpx;
  color: #1890ff;
  background: #e6f7ff;
  padding: 8rpx 20rpx;
  border-radius: 20rpx;
}

/* 单选框组样式 */
.radio-group {
  display: flex;
  flex-wrap: wrap;
}

.radio {
  display: flex;
  align-items: center;
  margin-right: 40rpx;
  margin-bottom: 16rpx;
}

.radio text {
  font-size: 28rpx;
  color: #333;
  margin-left: 8rpx;
}

/* 地区选择器样式 */
.picker {
  justify-content: space-between;
}

.picker-arrow {
  width: 32rpx;
  height: 32rpx;
  opacity: 0.3;
}

/* 所属村组样式 */
.linkage-group {
  display: flex;
  justify-content: space-between;
  gap: 20rpx;
  border-radius: 12rpx;
}

.linkage-level {
  flex: 1;
}

.linkage-level .picker {
  width: 100%;
  height: 88rpx;
  background: #f5f5f5;
  border-radius: 12rpx;
  padding: 0 24rpx;
  font-size: 28rpx;
  color: #333;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.linkage-level .picker:active {
  background-color: #e8e8e8;
}

.linkage-level .picker text {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.linkage-level .picker.placeholder {
  color: #999;
}

.linkage-level .picker.disabled {
  opacity: 0.7;
  background-color: #f9f9f9;
  color: #999;
}

/* 文本域样式 */
.form-textarea {
  width: 100%;
  height: 200rpx;
  background: #f5f5f5;
  border-radius: 12rpx;
  padding: 24rpx;
  font-size: 28rpx;
  color: #333;
  box-sizing: border-box;
  line-height: 1.6;
}

.textarea-counter {
  text-align: right;
  font-size: 24rpx;
  color: #999;
  margin-top: 8rpx;
}

.submit-btn {
  width: 100%;
  height: 88rpx;
  background: #1890ff;
  color: white;
  font-size: 32rpx;
  font-weight: 500;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 40rpx;
  box-shadow: 0 4rpx 12rpx rgba(24, 144, 255, 0.2);
  box-sizing: border-box;
}

.submit-btn:active {
  opacity: 0.9;
  transform: scale(0.98);
}

/* 账户信息样式 */
.account-info {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid #f5f5f5;
}

.info-item:last-child {
  margin-bottom: 0;
  padding-bottom: 0;
  border-bottom: none;
}

.info-label {
  font-size: 28rpx;
  color: #666;
}

.info-value {
  font-size: 28rpx;
  color: #333;
}
