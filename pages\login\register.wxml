<view class="container">
  <view class="header">
    <image class="logo" src="../../icons/logo.png" mode="aspectFit"></image>
    <view class="header-title">创建账号</view>
    <view class="header-subtitle">填写信息开始使用我们的服务</view>
  </view>

  <view class="form-group">
    <text class="form-label">所属村组</text>
    <view class="linkage-group {{errors.sscz ? 'error' : ''}}">
      <view class="linkage-level">
        <picker bindchange="changeSsczLevel1" value="{{regData.sscz_level1}}" range="{{ssczLevel1Options}}" range-key="region_name">
          <view class="picker {{regData.sscz_level1 ? '' : 'placeholder'}}">
            <image class="input-icon" src="../../icons/location.png"></image>
            <text>{{regData.sscz_level1 ? (ssczLevel1Options[regData.sscz_level1].region_name || '请选择') : '请选择村'}}</text>
          </view>
        </picker>
      </view>
      <view class="linkage-level">
        <picker bindchange="changeSsczLevel2" value="{{regData.sscz_level2}}" range="{{ssczLevel2Options}}" range-key="region_name" disabled="{{!regData.sscz_level1 || ssczLevel2Options.length === 0}}">
          <view class="picker {{regData.sscz_level2 ? '' : 'placeholder'}} {{!regData.sscz_level1 || ssczLevel2Options.length === 0 ? 'disabled' : ''}}">
            <image class="input-icon" src="../../icons/location.png"></image>
            <text>{{regData.sscz_level2 ? (ssczLevel2Options[regData.sscz_level2].region_name || '请选择') : '请选择组'}}</text>
          </view>
        </picker>
      </view>
    </view>
    <view class="error-message">{{errors.sscz}}</view>
  </view>

  <view class="form-group">
    <text class="form-label">用户名</text>
    <view class="input-group {{errors.username ? 'error' : ''}}">
      <image class="input-icon" src="../../icons/user.png"></image>
      <input class="input" type="text" placeholder="请输入用户名" bindinput="inputUsername" value="{{regData.username}}" />
    </view>
    <view class="error-message">{{errors.username}}</view>
  </view>

  <view class="form-group">
    <text class="form-label">真实姓名</text>
    <view class="input-group {{errors.name ? 'error' : ''}}">
      <image class="input-icon" src="../../icons/user-on.png"></image>
      <input class="input" type="text" placeholder="请输入真实姓名，以便我们更好服务" bindinput="inputName" value="{{regData.name}}" />
    </view>
    <view class="error-message">{{errors.name}}</view>
  </view>

  <view class="form-group">
    <text class="form-label">电子邮箱</text>
    <view class="input-group {{errors.email ? 'error' : ''}}">
      <image class="input-icon" src="../../icons/email.png"></image>
      <input class="input" type="text" placeholder="请输入电子邮箱" bindinput="inputEmail" value="{{regData.email}}" />
    </view>
    <view class="error-message">{{errors.email}}</view>
  </view>

  <view class="form-group">
    <text class="form-label">手机号码</text>
    <view class="input-group {{errors.phone ? 'error' : ''}}">
      <image class="input-icon" src="../../icons/phone.png"></image>
      <input class="input" type="number" placeholder="请输入真实手机号码" bindinput="inputPhone" value="{{regData.phone}}" />
    </view>
    <view class="error-message">{{errors.phone}}</view>
  </view>

  <view class="form-group">
    <text class="form-label">设置密码</text>
    <view class="input-group {{errors.password ? 'error' : ''}}">
      <image class="input-icon" src="../../icons/lock.png"></image>
      <input class="input" password="{{!showPassword}}" placeholder="请输入密码" bindinput="inputPassword" value="{{regData.password}}" />
      <view class="btn-eye" bindtap="togglePasswordVisibility">
        <image class="eye-icon" src="{{showPassword ? '../../icons/see.png' : '../../icons/nosee.png'}}"></image>
      </view>
    </view>
    <view class="error-message">{{errors.password}}</view>
  </view>

  <view class="form-group">
    <text class="form-label">确认密码</text>
    <view class="input-group {{errors.confirmPassword ? 'error' : ''}}">
      <image class="input-icon" src="../../icons/lock.png"></image>
      <input class="input" password="{{!showPassword}}" placeholder="请再次输入密码" bindinput="inputConfirmPassword" value="{{regData.confirmPassword}}" />
    </view>
    <view class="error-message">{{errors.confirmPassword}}</view>
  </view>

  <view class="agreement">
    <checkbox class="agreement-checkbox" checked="{{regData.agreement}}" bindtap="toggleAgreement" color="#1890ff" />
    <view class="agreement-text">
      我已阅读并同意
      <text class="link" bindtap="showTerms">《用户条款》</text>
      和
      <text class="link" bindtap="showPrivacy">《隐私政策》</text>
    </view>
  </view>

  <button class="btn-submit {{loading || !regData.agreement ? 'disabled' : ''}}" bindtap="formSubmit" hover-class="btn-hover">
    <text>{{loading ? '处理中...' : '立即注册'}}</text>
  </button>

  <view class="login-link">
    已有账号？<text class="login-text" bindtap="goToLogin">立即登录</text>
  </view>
</view>

