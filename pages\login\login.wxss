.login-container {
  min-height: 100vh;
  padding: 40rpx 40rpx 40rpx 40rpx;
  background: linear-gradient(145deg, #f8fafc, #e2e8f0);
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  padding-top: 120rpx;
  box-sizing: border-box;
}

/* 针对小屏幕设备的适配 */
@media screen and (max-height: 600px) {
  .login-container {
    padding-top: 60rpx;
    padding-bottom: 20rpx;
  }
}

/* 针对大屏幕设备的适配 */
@media screen and (min-height: 800px) {
  .login-container {
    padding-top: 160rpx;
    padding-bottom: 60rpx;
  }
}

.login-header {
  text-align: center;
  margin-bottom: 40rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  animation: floatDown 0.8s ease-out;
  flex-shrink: 0;
}

/* 小屏幕适配 */
@media screen and (max-height: 600px) {
  .login-header {
    margin-bottom: 20rpx;
  }
}

@keyframes floatDown {
  from {
    opacity: 0;
    transform: translateY(-30rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.logo-image {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 20rpx;
  border-radius: 30rpx;
  box-shadow: 0 10rpx 25rpx rgba(0, 0, 0, 0.12), 0 4rpx 10rpx rgba(0, 0, 0, 0.06);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  position: relative;
  z-index: 1;
}

/* 小屏幕logo适配 */
@media screen and (max-height: 600px) {
  .logo-image {
    width: 80rpx;
    height: 80rpx;
    margin-bottom: 15rpx;
  }
}

/* 大屏幕logo适配 */
@media screen and (min-height: 800px) {
  .logo-image {
    width: 140rpx;
    height: 140rpx;
    margin-bottom: 30rpx;
  }
}

.logo-image:active {
  transform: scale(1.05);
  box-shadow: 0 20rpx 40rpx rgba(0, 0, 0, 0.18), 0 8rpx 20rpx rgba(0, 0, 0, 0.1);
}

.logo-image::after {
  content: '';
  position: absolute;
  top: -10rpx;
  left: -10rpx;
  right: -10rpx;
  bottom: -10rpx;
  background: rgba(52, 152, 219, 0.05);
  border-radius: 46rpx;
  z-index: -1;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.logo-image:active::after {
  opacity: 1;
}

.login-title {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: #1e3a5f;
  margin-bottom: 16rpx;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
  letter-spacing: 2rpx;
}

.login-subtitle {
  font-size: 28rpx;
  color: #64748b;
  letter-spacing: 3rpx;
}

/* 小屏幕标题适配 */
@media screen and (max-height: 600px) {
  .login-title {
    font-size: 40rpx;
    margin-bottom: 12rpx;
  }

  .login-subtitle {
    font-size: 24rpx;
  }
}

/* 大屏幕标题适配 */
@media screen and (min-height: 800px) {
  .login-title {
    font-size: 52rpx;
    margin-bottom: 20rpx;
  }

  .login-subtitle {
    font-size: 30rpx;
  }
}

.login-form {
  margin-bottom: 40rpx;
  background-color: rgba(255, 255, 255, 0.85);
  padding: 50rpx 40rpx;
  border-radius: 28rpx;
  box-shadow: 0 15rpx 35rpx rgba(0, 0, 0, 0.08), 0 5rpx 15rpx rgba(0, 0, 0, 0.04);
  backdrop-filter: blur(15rpx);
  animation: fadeScale 0.6s ease-out;
  position: relative;
  overflow: hidden;
  flex-shrink: 0;
}

/* 小屏幕表单适配 */
@media screen and (max-height: 600px) {
  .login-form {
    padding: 30rpx 30rpx;
    margin-bottom: 20rpx;
  }
}

/* 大屏幕表单适配 */
@media screen and (min-height: 800px) {
  .login-form {
    padding: 60rpx 50rpx;
    margin-bottom: 60rpx;
  }
}

.login-form::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 8rpx;
  background: linear-gradient(90deg, #3498db, #2ecc71, #3498db);
  background-size: 200% 100%;
  animation: gradientMove 8s linear infinite;
}

@keyframes gradientMove {
  0% {
    background-position: 0% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

@keyframes fadeScale {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.input-group {
  margin-bottom: 30rpx;
}

/* 小屏幕输入框适配 */
@media screen and (max-height: 600px) {
  .input-group {
    margin-bottom: 20rpx;
  }
}

.input-label {
  font-size: 28rpx;
  color: #334155;
  margin-bottom: 16rpx;
  font-weight: 500;
  padding-left: 14rpx;
  display: flex;
  align-items: center;
}

.input-label::before {
  content: "";
  display: inline-block;
  width: 8rpx;
  height: 24rpx;
  background-color: #3498db;
  margin-right: 10rpx;
  border-radius: 4rpx;
}

.input-wrapper {
  position: relative;
  margin-bottom: 30rpx;
  display: flex;
  align-items: center;
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 8rpx 24rpx;
  border: 2rpx solid #e2e8f0;
  transition: all 0.3s ease;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.03);
  animation: fadeIn 0.5s ease-out;
}

.input-wrapper:focus-within {
  border-color: #3498db;
  background-color: rgba(52, 152, 219, 0.05);
  box-shadow: 0 6rpx 16rpx rgba(52, 152, 219, 0.18);
  transform: translateY(-3rpx);
}

.input-icon {
  width: 44rpx;
  height: 44rpx;
  margin-right: 20rpx;
  opacity: 0.7;
  transition: opacity 0.3s ease;
}

.input-wrapper:focus-within .input-icon {
  opacity: 1;
}

.input-field {
  flex: 1;
  height: 90rpx;
  font-size: 32rpx;
  background-color: transparent;
  caret-color: #3498db;
}

.clear-icon,
.password-icon {
  width: 44rpx;
  height: 44rpx;
  padding: 20rpx;
  margin-right: -20rpx;
  opacity: 0.6;
  transition: opacity 0.3s ease;
}

.clear-icon:active,
.password-icon:active {
  opacity: 1;
  transform: scale(1.1);
}

.options-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 45rpx;
  font-size: 28rpx;
  padding: 0 10rpx;
}

.remember-pwd {
  display: flex;
  align-items: center;
}

.remember-pwd text {
  margin-left: 10rpx;
  color: #475569;
}



.login-btn {
  width: 100%;
  height: 96rpx;
  line-height: 96rpx;
  background: linear-gradient(135deg, #4facfe, #00f2fe);
  color: #ffffff;
  font-size: 34rpx;
  border-radius: 48rpx;
  border: none;
  box-shadow: 0 10rpx 25rpx rgba(79, 172, 254, 0.35);
  transition: all 0.3s ease;
  margin-bottom: 40rpx;
  font-weight: bold;
  letter-spacing: 6rpx;
  position: relative;
  overflow: hidden;
  animation: breathe 3s infinite ease-in-out;
}

.login-btn:active {
  transform: translateY(6rpx);
  box-shadow: 0 5rpx 10rpx rgba(79, 172, 254, 0.2);
  opacity: 0.9;
}

.login-btn:after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, 
    rgba(255, 255, 255, 0) 0%, 
    rgba(255, 255, 255, 0.3) 50%, 
    rgba(255, 255, 255, 0) 100%);
  transition: all 0.6s ease;
  border-radius: 48rpx;
}

.login-btn:active:after {
  left: 100%;
}

.btn-text {
  position: relative;
  display: inline-block;
  transition: all 0.3s ease;
}

.login-btn:active .btn-text {
  transform: scale(0.95);
}

/* 添加微妙的呼吸效果 */
@keyframes breathe {
  0% {
    box-shadow: 0 10rpx 25rpx rgba(79, 172, 254, 0.35);
  }
  50% {
    box-shadow: 0 15rpx 30rpx rgba(79, 172, 254, 0.5);
  }
  100% {
    box-shadow: 0 10rpx 25rpx rgba(79, 172, 254, 0.35);
  }
}

.btn-hover {
  opacity: 0.85;
  transform: scale(0.98);
}

