var app = getApp();

Page({

  /**
   * 页面的初始数据
   */
  data: {
    member: "",
    avatar: "",
    ssczName: "良田镇",
    columnList: [
      {
        "url": "../member/my",
        "iconPath": "../../icons/star.png",
        "columnName": "账号信息"
      },
      {
        "url": "../member/notice",
        "iconPath": "../../icons/star.png",
        "columnName": "我的消息"
      },
      {
        "url": "../member/account",
        "iconPath": "../../icons/star.png",
        "columnName": "修改资料"
      },
      {
        "url": "../member/password",
        "iconPath": "../../icons/star.png",
        "columnName": "修改密码"
      },
      {
        "url": "../member/recharge",
        "iconPath": "../../icons/star.png",
        "columnName": "账户充值"
      },
      {
        "url": "../member/paylog",
        "iconPath": "../../icons/star.png",
        "columnName": "我的交易"
      },
      {
        "url": "../member/scorelog",
        "iconPath": "../../icons/star.png",
        "columnName": "积分"
      },
      {
        "url": "../member/comment",
        "iconPath": "../../icons/star.png",
        "columnName": "文章评论"
      },
      {
        "url": "../member/favorite",
        "iconPath": "../../icons/star.png",
        "columnName": "文章收藏"
      },
      {
        "url": "../member/workorder",
        "iconPath": "../../icons/pencil.png",
        "columnName": "我的工单"
      }
    ],
  },
  onShow: function(){
    // 检查登录状态
    const member = wx.getStorageSync('member');
    const member_auth = wx.getStorageSync('member_auth');
    const member_uid = wx.getStorageSync('member_uid');

    // 如果未登录，跳转到登录页面
    if (!member || !member_auth || !member_uid || !member.id) {
      wx.reLaunch({
        url: '/pages/login/login'
      });
      return;
    }

    // 用户已登录，更新页面数据
    this.setData({
      member: member,
      avatar: member.avatar || '../../icons/user.png'
    });

    if (member && member.sscz) {
      this.loadSsczName(member.sscz);
    }

    // 设置当前选中的tabbar项为会员
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().setData({
        selected: 4
      });
    }
  },

  onLoad: function () {
    // 检查登录状态
    const member = wx.getStorageSync('member');
    const member_auth = wx.getStorageSync('member_auth');
    const member_uid = wx.getStorageSync('member_uid');

    // 如果未登录，跳转到登录页面
    if (!member || !member_auth || !member_uid || !member.id) {
      wx.reLaunch({
        url: '/pages/login/login'
      });
      return;
    }

    // 用户已登录，初始化页面数据
    const avatar = member.avatar || '../../icons/user.png';
    console.log('member:', member);
    console.log('avatar:', avatar);

    this.setData({
      member: member,
      avatar: avatar
    });

    if (member.sscz) {
      this.loadSsczName(member.sscz);
    }
  },

  loadSsczName: function(ssczId) {
    if (!ssczId) {
      this.setData({ ssczName: '未设置' });
      return;
    }

    const ssczIdStr = String(ssczId);
    
    wx.request({
      url: app.globalData.http_api + 's=api&c=api&m=linkage_ld&mid=member&file=&code=sscz&parent_id=0',
      method: 'GET',
      success: (res) => {
        if (res.data && res.data.data && Array.isArray(res.data.data)) {
          const level1Item = res.data.data.find(item => String(item.region_id) === ssczIdStr);
          
          if (level1Item) {
            this.setData({ ssczName: level1Item.region_name });
            return;
          }
          
          this.checkLevel2Sscz(res.data.data, ssczIdStr);
        }
      },
      fail: (err) => {
        this.setData({ ssczName: ssczIdStr + ' (查询失败)' });
      }
    });
  },

  checkLevel2Sscz: function(level1Options, ssczIdStr) {
    this.sequentialCheckLevel2(level1Options, ssczIdStr, 0);
  },
  
  sequentialCheckLevel2: function(level1Options, ssczIdStr, index) {
    if (index >= level1Options.length) {
      this.setData({ ssczName: ssczIdStr });
      return;
    }
    
    const parent = level1Options[index];
    wx.request({
      url: app.globalData.http_api + 's=api&c=api&m=linkage_ld&mid=member&file=&code=sscz&parent_id=' + parent.region_id,
      method: 'GET',
      success: (res) => {
        if (res.data && res.data.data && Array.isArray(res.data.data)) {
          const level2Item = res.data.data.find(item => String(item.region_id) === ssczIdStr);
          
          if (level2Item) {
            this.setData({ 
              ssczName: parent.region_name + '' + level2Item.region_name 
            });
            return;
          }
        }
        
        this.sequentialCheckLevel2(level1Options, ssczIdStr, index + 1);
      },
      fail: (err) => {
        this.sequentialCheckLevel2(level1Options, ssczIdStr, index + 1);
      }
    });
  },

  downLogin: function () {
    wx.showModal({
      title: "退出",
      content: "是否退出？",
      success: function (res) {
        if (res.confirm) {
          wx.clearStorageSync('member');
          wx.clearStorageSync('member_uid');
          wx.clearStorageSync('member_auth');          
          wx.reLaunch({ url: "../login/login" });
        }
      }
    })
  },
    userInfo:function(){
      if (wx.getStorageSync('member')) {
        wx.navigateTo({ url: "../member/my" });
      } else {
        wx.reLaunch({ url: "../login/login" });
      }       

    },

})