var app = getApp();

// 初始化全局变量
var memberAuthString = "&api_auth_code=" + wx.getStorageSync('member_auth') + "&api_auth_uid=" + wx.getStorageSync('member_uid');
var postUrl = app.globalData.http_api + "s=member&app=workorder&c=home&m=add"
var uploadUrl = app.globalData.http_api + "s=api&c=file&m=upload&fid=211"

Page({
    data: {
        postData: {},
        tupianxinxi: [], // 存储图片信息
        uploadedImages: [], // 存储已上传的图片ID
        isSubmitting: false, // 提交状态
        isUploading: false, // 上传状态
        isLoggedIn: false, // 登录状态
        publishType: '1', // 默认选择反应问题
        xinxileixing: '', // 添加信息类型字段
        isPrivate: false, // 新增隐私设置
        
        // 村组二级联动数据
        villageLevel1: [], // 一级村组数据
        villageLevel2: [], // 二级村组数据
        villageGroupColumns: [[], []], // 二级联动显示数据
        villageGroupIndices: [0, 0], // 当前选中的索引值
        selectedVillageId: '', // 选中的村组ID
        selectedVillageName: '', // 选中的村组名称
        arrowIconMissing: false, // 控制是否显示下拉箭头
        
        // 区域选择相关数据
        areaType: '1', // 默认选择本村组
        memberSscz: '' // 当前会员所属村组名称
    },

    // 页面加载
    onLoad: function() {
        // 检查登录状态并处理
        this.checkAndHandleLoginStatus();
    },

    // 页面显示
    onShow: function() {
        // 每次页面显示时检查登录状态并处理
        this.checkAndHandleLoginStatus();
        
        // 重置表单状态，但保留图片数据
        this.setData({
            isSubmitting: false,
            isUploading: false
        });
        
        // 设置当前选中的tabbar项为发布
        if (typeof this.getTabBar === 'function' && this.getTabBar()) {
            this.getTabBar().setData({
                selected: 2
            });
        }
    },

    // 页面初次渲染完成
    onReady: function() {
        // 页面渲染完成后再次检查登录状态，确保表单不被未登录用户操作
        if (!this.data.isLoggedIn) {
            this.showLoginModal();
        }
    },

    // 检查登录状态并处理
    checkAndHandleLoginStatus: function() {
        const member = wx.getStorageSync('member');
        const memberAuth = wx.getStorageSync('member_auth');
        const memberUid = wx.getStorageSync('member_uid');

        // 如果未登录，直接跳转到登录页面
        if (!member || !memberAuth || !memberUid || !member.id) {
            wx.reLaunch({
                url: '/pages/login/login'
            });
            return false;
        }

        // 已登录，设置登录状态并执行页面初始化
        this.setData({
            isLoggedIn: true
        });
        this.initPageData();
        return true;
    },
    
    // 初始化页面数据
    initPageData: function() {
        // 初始化状态
        this.setData({
            isSubmitting: false,
            isUploading: false
        });

        // 先获取村组数据，再获取会员所属村组信息
        // 这样可以确保村组数据已经加载，避免后续操作找不到村组数据
        this.fetchVillageGroups();
        
        // 获取会员所属村组信息
        setTimeout(() => {
            // 记录当前区域类型，避免被重置
            const currentAreaType = this.data.areaType;
            
            // 只有当选择"本村组"时才加载会员村组信息
            if (currentAreaType === '1' || !this.data.selectedVillageId) {
                this.getMemberVillageInfo();
            }
        }, 300); // 延迟一点执行，确保村组数据已加载
    },

    // 获取会员所属村组信息
    getMemberVillageInfo: function() {
        // 检查当前区域类型，如果是"其它村组"且已有选择，则跳过设置会员村组
        if (this.data.areaType === '2' && this.data.selectedVillageId) {
            return;
        }
        
        const member = wx.getStorageSync('member');
        if (member && member.sscz) {
            // 直接设置村组ID
            this.setData({
                selectedVillageId: member.sscz
            });
            
            // 使用loadSsczName方法获取村组名称
            this.loadSsczName(member.sscz);
        } else {
            // 如果在本地存储中没有找到会员信息，尝试从服务器获取
            const memberUid = wx.getStorageSync('member_uid');
            const memberAuth = wx.getStorageSync('member_auth');
            
            if (memberUid && memberAuth) {
                const that = this;
                // 构建API请求URL
                const userInfoUrl = app.globalData.http_api + "s=member&c=api&m=userinfo&uid=" + memberUid + "&api_auth_code=" + memberAuth + "&api_auth_uid=" + memberUid;
                
                wx.request({
                    url: userInfoUrl,
                    method: 'GET',
                    success: function(res) {
                        if (res.data && res.data.code && res.data.data) {
                            // 获取到用户信息，存储到本地
                            const userData = res.data.data;
                            wx.setStorageSync('member', userData);
                            
                            // 设置村组ID
                            that.setData({
                                selectedVillageId: userData.sscz
                            });
                            
                            // 使用loadSsczName方法获取村组名称
                            that.loadSsczName(userData.sscz);
                        }
                    },
                    fail: function(err) {
                        // 请求失败处理
                        console.error("获取会员信息失败", err);
                    }
                });
            }
        }
    },
    
    // 加载村组名称 - 从member/index.js参考的方法
    loadSsczName: function(ssczId) {
        if (!ssczId) {
            this.setData({ 
                memberSscz: '未设置',
                selectedVillageId: '' // 确保没有村组ID时也清空
            });
            return;
        }

        const ssczIdStr = String(ssczId);
        const that = this;
        
        // 保存原始的村组ID，确保在获取名称失败时仍然能使用ID提交
        that.setData({
            selectedVillageId: ssczIdStr
        });
        
        wx.request({
            url: app.globalData.http_api + 's=api&c=api&m=linkage_ld&mid=member&file=&code=sscz&parent_id=0',
            method: 'GET',
            success: (res) => {
                if (res.data && res.data.data && Array.isArray(res.data.data)) {
                    const level1Item = res.data.data.find(item => String(item.region_id) === ssczIdStr);
                    
                    if (level1Item) {
                        that.setData({ 
                            memberSscz: level1Item.region_name,
                            selectedVillageName: level1Item.region_name,
                            selectedVillageId: ssczIdStr // 再次确认ID设置正确
                        });
                        return;
                    }
                    
                    that.checkLevel2Sscz(res.data.data, ssczIdStr);
                }
            },
            fail: (err) => {
                that.setData({ 
                    memberSscz: ssczIdStr + ' (查询失败)',
                    selectedVillageId: ssczIdStr // 确保失败时仍保持ID
                });
                console.error("查询村组名称失败", err);
            }
        });
    },

    checkLevel2Sscz: function(level1Options, ssczIdStr) {
        this.sequentialCheckLevel2(level1Options, ssczIdStr, 0);
    },
    
    sequentialCheckLevel2: function(level1Options, ssczIdStr, index) {
        const that = this;
        
        if (index >= level1Options.length) {
            that.setData({ 
                memberSscz: ssczIdStr,
                selectedVillageId: ssczIdStr // 确保不变
            });
            return;
        }
        
        const parent = level1Options[index];
        wx.request({
            url: app.globalData.http_api + 's=api&c=api&m=linkage_ld&mid=member&file=&code=sscz&parent_id=' + parent.region_id,
            method: 'GET',
            success: (res) => {
                if (res.data && res.data.data && Array.isArray(res.data.data)) {
                    const level2Item = res.data.data.find(item => String(item.region_id) === ssczIdStr);
                    
                    if (level2Item) {
                        const fullName = parent.region_name + ' - ' + level2Item.region_name;
                        that.setData({ 
                            memberSscz: fullName,
                            selectedVillageName: fullName,
                            selectedVillageId: ssczIdStr // 确保ID不变
                        });
                        return;
                    }
                }
                
                that.sequentialCheckLevel2(level1Options, ssczIdStr, index + 1);
            },
            fail: (err) => {
                that.sequentialCheckLevel2(level1Options, ssczIdStr, index + 1);
            }
        });
    },

    // 区域类型变更处理
    onAreaChange: function(e) {
        const areaType = e.detail.value;
        
        // 更新区域类型
        this.setData({ areaType: areaType });
        
        if (areaType === '1') {
            // 选择本村组时，使用会员自己的村组信息
            const member = wx.getStorageSync('member');
            
            if (member && member.sscz) {
                this.setData({
                    selectedVillageId: member.sscz,
                    // 重置索引值，便于UI显示正确
                    villageGroupIndices: [0, 0]
                });
                
                // 使用loadSsczName方法获取村组名称
                this.loadSsczName(member.sscz);
            } else {
                this.getMemberVillageInfo();
            }
        } else if (areaType === '2') {
            // 检查是否已经有其他村组的选择
            const hasOtherVillageSelected = this.data.areaType === '2' && this.data.selectedVillageId;
            
            if (hasOtherVillageSelected) {
                return; // 保留当前选择，不重置
            }
            
            // 重置村组选择
            this.resetOtherVillageSelection();
            
            // 如果已经加载了村组数据，可以使用
            if (this.data.villageGroupColumns[0].length > 0) {
                this.setupInitialOtherVillage();
            } else {
                // 还没有加载村组数据，尝试加载
                this.fetchVillageGroups();
                
                // 延迟设置初始选择，确保数据已加载
                setTimeout(() => {
                    this.setupInitialOtherVillage();
                }, 500);
            }
        }
    },
    
    // 重置其它村组的选择
    resetOtherVillageSelection: function() {
        this.setData({
            selectedVillageId: '',
            selectedVillageName: ''
        });
    },
    
    // 设置初始的其它村组选择
    setupInitialOtherVillage: function() {
        if (this.data.villageGroupColumns[0].length > 0) {
            const level1Index = this.data.villageGroupIndices[0];
            const level1Item = this.data.villageGroupColumns[0][level1Index];
            
            if (this.data.villageGroupColumns[1].length > 0) {
                // 有二级数据，使用二级选中的值
                const level2Index = this.data.villageGroupIndices[1];
                const level2Item = this.data.villageGroupColumns[1][level2Index];
                
                if (level2Item) {
                    this.setData({
                        selectedVillageId: level2Item.region_id,
                        selectedVillageName: level1Item ? level1Item.region_name + ' - ' + level2Item.region_name : level2Item.region_name
                    });
                    return;
                }
            }
            
            if (level1Item) {
                // 只有一级数据，使用一级选中的值
                this.setData({
                    selectedVillageId: level1Item.region_id,
                    selectedVillageName: level1Item.region_name
                });
            }
        }
    },

    // 检查登录状态
    checkLoginStatus: function() {
        const memberAuth = wx.getStorageSync('member_auth');
        const memberUid = wx.getStorageSync('member_uid');
        
        const isLoggedIn = !!(memberAuth && memberUid);
        
        this.setData({
            isLoggedIn: isLoggedIn
        });
        
        return isLoggedIn;
    },

    // 跳转到登录页面
    goToLogin: function() {
        wx.navigateTo({
            url: '/pages/login/login'
        });
    },

    // 表单提交
    formSubmit: function (e) {
        // 检查登录状态
        if (!this.data.isLoggedIn) {
            this.showLoginModal();
            return;
        }

        // 检查是否正在提交
        if (this.data.isSubmitting) {
            return;
        }

        var that = this;
        var formData = e.detail.value;
        
        // 添加发布类型到表单数据
        formData.fabuleixing = this.data.publishType;
        
        // 基础必填字段验证
        if (!formData.title) {
            wx.showToast({
                title: '请输入主题',
                icon: 'none'
            });
            return;
        }
        if (!formData.content) {
            wx.showToast({
                title: '请输入内容',
                icon: 'none'
            });
            return;
        }
        
        // 确认村组ID - 根据区域类型处理
        let villageId = this.data.selectedVillageId;
        
        // 检查逻辑：确保区域类型与村组ID匹配
        if (this.data.areaType === '2' && this.data.selectedVillageId) {
            // 其它村组模式下使用已选择的村组ID
            villageId = this.data.selectedVillageId;
        } else if (this.data.areaType === '1') {
            // 本村组模式下使用会员的村组ID
            const member = wx.getStorageSync('member');
            if (member && member.sscz) {
                villageId = member.sscz;
            }
        }
        
        // 检查最终是否有村组ID
        if (!villageId) {
            if (this.data.areaType === '1') {
                wx.showToast({
                    title: '无法获取您的村组信息，请选择其它村组',
                    icon: 'none'
                });
                that.setData({ isSubmitting: false });
                return;
            } else if (this.data.areaType === '2') {
                // 其它村组但没有选择
                // 尝试获取当前选中的一级村组
                if (this.data.villageGroupColumns[0].length > 0) {
                    const level1Index = this.data.villageGroupIndices[0];
                    const level1Item = this.data.villageGroupColumns[0][level1Index];
                    if (level1Item) {
                        villageId = level1Item.region_id;
                        this.setData({
                            selectedVillageId: villageId,
                            selectedVillageName: level1Item.region_name
                        });
                    } else {
                        wx.showToast({
                            title: '请选择所属村组',
                            icon: 'none'
                        });
                        that.setData({ isSubmitting: false });
                        return;
                    }
                } else {
                    wx.showToast({
                        title: '请选择所属村组',
                        icon: 'none'
                    });
                    that.setData({ isSubmitting: false });
                    return;
                }
            }
        }
        
        // 最后确认一下村组ID
        if (!villageId) {
            wx.showToast({
                title: '请选择所属村组',
                icon: 'none'
            });
            that.setData({ isSubmitting: false });
            return;
        }
        
        // 根据发布类型验证不同字段
        if (this.data.publishType === '1') { // 反应问题
            if (!formData.leixing) {
                wx.showToast({
                    title: '请选择问题类型',
                    icon: 'none'
                });
                return;
            }
            if (!formData.gongkai) {
                wx.showToast({
                    title: '请选择公开设置',
                    icon: 'none'
                });
                return;
            }
        } else if (this.data.publishType === '2') { // 发布信息
            if (!formData.xinxileixing) {
                wx.showToast({
                    title: '请选择信息类型',
                    icon: 'none'
                });
                return;
            }
        }
        
        // 添加处理进度字段，默认值为"待受理"
        formData.jindu = "1";
        
        // 添加发布人信息
        const member = wx.getStorageSync('member');
        if (member) {
            formData.author = member.name || member.username || wx.getStorageSync('member_uid');
        } else {
            formData.author = wx.getStorageSync('member_uid');
        }
        
        // 设置提交状态
        that.setData({ isSubmitting: true });
        
        // 更新memberAuthString，确保使用最新的登录信息
        const memberAuth = wx.getStorageSync('member_auth');
        const memberUid = wx.getStorageSync('member_uid');
        const currentMemberAuthString = "&api_auth_code=" + memberAuth + "&api_auth_uid=" + memberUid;
        const currentPostUrl = app.globalData.http_api + "s=member&app=workorder&c=home&m=add" + currentMemberAuthString;

        // 构建提交数据
        var postParams = {
            is_ajax: 1,
            data: {
                title: formData.title,
                content: formData.content,
                fabuleixing: formData.fabuleixing,
                leixing: formData.leixing,
                gongkai: formData.gongkai,
                xinxileixing: formData.xinxileixing,
                jindu: formData.jindu,
                author: formData.author,
                uid: memberUid,
                sscz: villageId, // 使用确认过的村组ID
                quyu: formData.quyu || this.data.areaType // 添加区域字段
            },
            catid: 1
        };
        
        // 输出日志确认村组ID已正确设置
        //console.log("提交区域类型: ", this.data.areaType);
        //console.log("提交村组ID: ", villageId);
        
        // 如果有上传图片，则添加到提交数据中
        if (that.data.uploadedImages.length > 0) {
            postParams.data.tupianxinxi = that.data.uploadedImages;
        }
        
        // 发送请求
        wx.request({
            url: currentPostUrl,
            method: 'POST',
            data: postParams,
            header: { 'Content-Type': 'application/json' },
            success: function (res) {
                if (res.data.code) {
                    // 提交成功后重置状态
                    that.setData({
                        isSubmitting: false,
                        tupianxinxi: [],
                        uploadedImages: []
                    });
                    
                    // 设置全局变量，标记工单刚刚发布成功，需要刷新列表
                    app.globalData.needRefreshWorkorderList = true;
                    
                    // 显示成功提示
                    wx.showModal({
                        title: '提示',
                        content: '发表成功',
                        showCancel: false,
                        success: function(res) {
                            if (res.confirm) {
                                // 使用switchTab而不是redirectTo，因为list是tabBar页面
                                wx.switchTab({
                                    url: '/pages/workorder/list'
                                });
                            }
                        }
                    });
                } else {
                    that.setData({ isSubmitting: false });
                    
                    // 检查是否是登录过期
                    if (res.data.msg && res.data.msg.indexOf('登录') !== -1) {
                        that.showLoginModal();
                    } else {
                        showModal(res.data.msg);
                    }
                }
            },
            fail: function () {
                that.setData({ isSubmitting: false });
                showModal("网络错误，请重试");
            },
        });
    },

    // 上传图片
    uploadFile: function () {
        // 检查登录状态
        if (!this.data.isLoggedIn) {
            this.showLoginModal();
            return;
        }

        if (this.data.isUploading) {
            this.showToast('正在上传中，请稍候');
            return;
        }

        // 保存当前的村组选择状态，以便在上传过程中保持
        const currentVillageId = this.data.selectedVillageId;
        const currentVillageName = this.data.selectedVillageName;
        const currentAreaType = this.data.areaType;
        // 保存当前的索引值，避免选择器显示不一致
        const currentIndices = [...this.data.villageGroupIndices];
        
        var that = this;
        const maxImages = 5;
        const remainingSlots = maxImages - that.data.uploadedImages.length;
        
        if (remainingSlots <= 0) {
            this.showToast('最多只能上传5张图片');
            return;
        }

        that.setData({ isUploading: true });
        
        wx.chooseImage({
            count: remainingSlots,
            sizeType: ['compressed'],
            sourceType: ['album', 'camera'],
            success: function (res2) {
                let uploadCount = 0;
                const totalFiles = res2.tempFilePaths.length;
                
                // 更新memberAuthString，确保使用最新的登录信息
                const memberAuth = wx.getStorageSync('member_auth');
                const memberUid = wx.getStorageSync('member_uid');
                
                if (!memberAuth || !memberUid) {
                    that.setData({ isUploading: false });
                    that.showLoginModal();
                    return;
                }
                
                const currentMemberAuthString = "&api_auth_code=" + memberAuth + "&api_auth_uid=" + memberUid;
                const currentUploadUrl = app.globalData.http_api + "s=api&c=file&m=upload&fid=211" + currentMemberAuthString;
                
                // 先显示临时预览图
                const tempImages = res2.tempFilePaths;
                const currentTupianxinxi = that.data.tupianxinxi || [];
                
                // 将临时图片添加到预览区域，同时确保村组选择保持不变
                that.setData({
                    tupianxinxi: currentTupianxinxi.concat(tempImages),
                    // 确保村组数据保持不变
                    selectedVillageId: currentVillageId,
                    selectedVillageName: currentVillageName,
                    areaType: currentAreaType,
                    // 恢复索引值
                    villageGroupIndices: currentIndices
                });
                
                // 上传图片
                tempImages.forEach(function (filePath, index) {
                    // 检查文件大小
                    wx.getFileSystemManager().getFileInfo({
                        filePath: filePath,
                        success: (res) => {
                            if (res.size > 10 * 1024 * 1024) { // 10MB限制
                                that.showToast('图片大小不能超过10MB');
                                return;
                            }
                            
                            wx.uploadFile({
                                url: currentUploadUrl,
                                filePath: filePath,
                                name: 'file_data',
                                formData: { 
                                    is_ajax: 1,
                                    file_type: 'image'
                                },
                                header: { 
                                    'Content-Type': 'multipart/form-data'
                                },
                                success: function (res) {
                                    // 解析返回的数据，uploadFile返回的是字符串
                                    let responseData;
                                    try {
                                        responseData = JSON.parse(res.data);
                                    } catch (error) {
                                        that.showToast('解析服务器响应失败');
                                        // 处理图片移除逻辑...
                                        return;
                                    }
                                    
                                    if (responseData.code) {
                                        // 获取当前已上传的图片ID数组
                                        const currentUploadedImages = that.data.uploadedImages || [];
                                        
                                        // 将新上传的图片ID添加到数组中
                                        const newUploadedImages = currentUploadedImages.concat(responseData.code);
                                        
                                        // 更新已上传的图片ID数组，同时保持村组选择状态
                                        that.setData({
                                            uploadedImages: newUploadedImages,
                                            // 确保村组数据保持不变
                                            selectedVillageId: currentVillageId,
                                            selectedVillageName: currentVillageName,
                                            areaType: currentAreaType,
                                            // 恢复索引值
                                            villageGroupIndices: currentIndices
                                        });
                                        
                                        // 更新预览图，将临时图片替换为服务器返回的URL
                                        const tupianxinxi = that.data.tupianxinxi;
                                        const tempIndex = currentTupianxinxi.length + index;
                                        if (tempIndex < tupianxinxi.length) {
                                            tupianxinxi[tempIndex] = responseData.data.url;
                                            that.setData({
                                                tupianxinxi: tupianxinxi,
                                                // 确保村组数据保持不变
                                                selectedVillageId: currentVillageId,
                                                selectedVillageName: currentVillageName,
                                                areaType: currentAreaType,
                                                // 恢复索引值
                                                villageGroupIndices: currentIndices
                                            });
                                        }
                                    } else {
                                        // 检查是否是登录过期
                                        if (responseData.msg && responseData.msg.indexOf('登录') !== -1) {
                                            that.showLoginModal();
                                        } else {
                                            that.showToast(responseData.msg || '上传失败，请重试');
                                        }
                                        
                                        // 上传失败，从预览中移除对应的临时图片
                                        const tupianxinxi = that.data.tupianxinxi;
                                        const tempIndex = currentTupianxinxi.length + index;
                                        if (tempIndex < tupianxinxi.length) {
                                            tupianxinxi.splice(tempIndex, 1);
                                            that.setData({
                                                tupianxinxi: tupianxinxi,
                                                // 确保村组数据保持不变
                                                selectedVillageId: currentVillageId,
                                                selectedVillageName: currentVillageName,
                                                areaType: currentAreaType,
                                                // 恢复索引值
                                                villageGroupIndices: currentIndices
                                            });
                                        }
                                    }
                                },
                                fail: function (err) {
                                    that.showToast('网络错误，请重试');
                                    
                                    // 上传失败，从预览中移除对应的临时图片
                                    const tupianxinxi = that.data.tupianxinxi;
                                    const tempIndex = currentTupianxinxi.length + index;
                                    if (tempIndex < tupianxinxi.length) {
                                        tupianxinxi.splice(tempIndex, 1);
                                        that.setData({
                                            tupianxinxi: tupianxinxi,
                                            // 确保村组数据保持不变
                                            selectedVillageId: currentVillageId,
                                            selectedVillageName: currentVillageName,
                                            areaType: currentAreaType,
                                            // 恢复索引值
                                            villageGroupIndices: currentIndices
                                        });
                                    }
                                },
                                complete: function() {
                                    uploadCount++;
                                    if (uploadCount === totalFiles) {
                                        that.setData({ 
                                            isUploading: false,
                                            // 最后确保村组数据保持不变
                                            selectedVillageId: currentVillageId,
                                            selectedVillageName: currentVillageName,
                                            areaType: currentAreaType,
                                            // 恢复索引值
                                            villageGroupIndices: currentIndices
                                        });
                                        
                                        // 输出日志确认最终状态
                                        //console.log("上传完成后村组状态 - ID:", that.data.selectedVillageId, "名称:", that.data.selectedVillageName, "区域类型:", that.data.areaType, "索引:", that.data.villageGroupIndices);
                                    }
                                }
                            });
                        },
                        fail: (err) => {
                            that.showToast('获取图片信息失败');
                        }
                    });
                });
            },
            fail: function(err) {
                that.setData({ 
                    isUploading: false,
                    // 确保村组数据保持不变，即使上传失败
                    selectedVillageId: currentVillageId,
                    selectedVillageName: currentVillageName,
                    areaType: currentAreaType,
                    // 恢复索引值
                    villageGroupIndices: currentIndices
                });
                that.showToast('未选择任何图片');
            }
        });
    },

    // 删除图片
    deleteImage: function(e) {
        // 保存当前的村组选择状态，以便在图片删除过程中保持
        const currentVillageId = this.data.selectedVillageId;
        const currentVillageName = this.data.selectedVillageName;
        const currentAreaType = this.data.areaType;
        // 保存当前的索引值，避免选择器显示不一致
        const currentIndices = [...this.data.villageGroupIndices];
        
        const index = e.currentTarget.dataset.index;
        const tupianxinxi = this.data.tupianxinxi;
        const uploadedImages = this.data.uploadedImages;
        
        // 如果删除的是已上传的图片，也需要从uploadedImages中删除
        if (index < uploadedImages.length) {
            uploadedImages.splice(index, 1);
            this.setData({
                uploadedImages: uploadedImages,
                // 确保村组数据保持不变
                selectedVillageId: currentVillageId,
                selectedVillageName: currentVillageName,
                areaType: currentAreaType,
                // 恢复索引值
                villageGroupIndices: currentIndices
            });
        }
        
        // 从预览图中删除
        tupianxinxi.splice(index, 1);
        
        this.setData({
            tupianxinxi: tupianxinxi,
            // 确保村组数据保持不变
            selectedVillageId: currentVillageId,
            selectedVillageName: currentVillageName,
            areaType: currentAreaType,
            // 恢复索引值
            villageGroupIndices: currentIndices
        });
    },

    // 显示登录提示框
    showLoginModal: function() {
        wx.showModal({
            title: '提示',
            content: '请先登录后再操作',
            confirmText: '去登录',
            cancelText: '返回',
            showCancel: true,
            success: function(res) {
                if (res.confirm) {
                    wx.navigateTo({
                        url: '/pages/login/login'
                    });
                } else {
                    // 用户取消登录，返回上一页
                    wx.navigateBack({
                        delta: 1
                    });
                }
            }
        });
    },

    // 显示提示信息
    showToast: function(title) {
        wx.showToast({
            title: title,
            icon: 'none',
            duration: 2000
        });
    },

    onPublishTypeChange(e) {
        this.setData({
            publishType: e.detail.value,
            xinxileixing: '' // 切换发布类型时清空信息类型
        });
    },

    onXinxiTypeChange(e) {
        this.setData({
            xinxileixing: e.detail.value
        });
    },

    // 处理公开设置变化
    onPrivacyChange(e) {
        this.setData({
            isPrivate: e.detail.value === '2'
        });
    },

    // 获取村组数据
    fetchVillageGroups: function() {
        const that = this;
        // 获取一级村组数据
        wx.request({
            url: app.globalData.http_api + 's=api&c=api&m=linkage_ld&mid=workorder&file=&code=sscz&parent_id=0',
            method: 'GET',
            success: function(res) {
                if (res.data && res.data.data) {
                    const level1 = res.data.data;
                    
                    // 设置一级数据，处理正确的字段名
                    that.setData({
                        villageLevel1: level1,
                        villageGroupColumns: [[...level1], []] // 第一列填充数据
                    });
                    
                    // 如果有一级数据，获取第一个一级的二级数据
                    if (level1 && level1.length > 0) {
                        that.fetchVillageLevel2(level1[0].region_id);
                        
                        // 如果当前是其它村组模式，需要设置初始选择
                        if (that.data.areaType === '2' && !that.data.selectedVillageId) {
                            // 先设置为一级选择
                            that.setData({
                                selectedVillageId: level1[0].region_id,
                                selectedVillageName: level1[0].region_name
                            });
                        }
                    }
                } else {
                    console.error('获取村组数据失败:', res);
                }
                
                // 检查箭头图标是否存在
                wx.getFileSystemManager().access({
                    path: '../../icons/arrow-down.png',
                    fail: function() {
                        that.setData({
                            arrowIconMissing: true
                        });
                    }
                });
            },
            fail: function(err) {
                console.error('获取村组数据请求失败:', err);
            }
        });
    },
    
    // 获取二级村组数据
    fetchVillageLevel2: function(parentId) {
        const that = this;
        wx.request({
            url: app.globalData.http_api + 's=api&c=api&m=linkage_ld&mid=workorder&file=&code=sscz&parent_id=' + parentId,
            method: 'GET',
            success: function(res) {
                if (res.data && res.data.data) {
                    const level2 = res.data.data;
                    
                    // 设置二级数据
                    that.setData({
                        villageLevel2: level2,
                        'villageGroupColumns[1]': level2,
                        'villageGroupIndices[1]': 0
                    });
                    
                    // 如果当前已有选择的村组ID，检查是否在当前二级数据中
                    const currentVillageId = that.data.selectedVillageId;
                    
                    if (currentVillageId) {
                        // 检查当前选择的ID是否属于这个父级下的二级村组
                        const level2Index = level2.findIndex(item => String(item.region_id) === String(currentVillageId));
                        
                        if (level2Index !== -1) {
                            // 找到匹配的二级选项，保留用户之前的选择
                            const level1Item = that.data.villageGroupColumns[0][that.data.villageGroupIndices[0]];
                            const level2Item = level2[level2Index];
                            
                            that.setData({
                                'villageGroupIndices[1]': level2Index,
                                selectedVillageId: level2Item.region_id,
                                selectedVillageName: level1Item ? level1Item.region_name + ' - ' + level2Item.region_name : level2Item.region_name
                            });
                            return;
                        }
                    }
                    
                    // 如果没有之前的选择或者之前的选择不在当前二级列表中，则默认选择第一个
                    if (level2 && level2.length > 0) {
                        const level1Item = that.data.villageGroupColumns[0][that.data.villageGroupIndices[0]];
                        const level2Item = level2[0];
                        
                        that.setData({
                            selectedVillageId: level2Item.region_id,
                            selectedVillageName: level1Item ? level1Item.region_name + ' - ' + level2Item.region_name : level2Item.region_name
                        });
                    }
                } else {
                    // 没有二级数据时，使用一级数据
                    const level1Item = that.data.villageGroupColumns[0][that.data.villageGroupIndices[0]];
                    if (level1Item) {
                        that.setData({
                            selectedVillageId: level1Item.region_id,
                            selectedVillageName: level1Item.region_name,
                            'villageGroupColumns[1]': [],
                            'villageGroupIndices[1]': 0
                        });
                    } else {
                        // 清空二级列表和选择
                        that.setData({
                            'villageGroupColumns[1]': [],
                            'villageGroupIndices[1]': 0,
                            selectedVillageId: '',
                            selectedVillageName: ''
                        });
                    }
                }
            },
            fail: function(err) {
                console.error('获取二级村组数据请求失败:', err);
            }
        });
    },
    
    // 处理一级村组选择
    onVillageLevel1Change: function(e) {
        const index = e.detail.value;
        const selectedLevel1 = this.data.villageGroupColumns[0][index];
        
        if (selectedLevel1 && selectedLevel1.region_id) {
            // 更新索引
            this.setData({
                'villageGroupIndices[0]': index,
                'villageGroupIndices[1]': 0, // 重置二级索引
                selectedVillageId: selectedLevel1.region_id, // 先设置为一级村ID
                selectedVillageName: selectedLevel1.region_name
            });
            
            // 获取新的二级数据
            this.fetchVillageLevel2(selectedLevel1.region_id);
        }
    },
    
    // 处理二级村组选择
    onVillageLevel2Change: function(e) {
        const index = e.detail.value;
        const level1Index = this.data.villageGroupIndices[0];
        
        const level1Item = this.data.villageGroupColumns[0][level1Index];
        const level2Item = this.data.villageGroupColumns[1][index];
        
        if (level1Item && level2Item) {
            this.setData({
                'villageGroupIndices[1]': index,
                selectedVillageId: level2Item.region_id,
                selectedVillageName: level1Item.region_name + ' - ' + level2Item.region_name
            });
        }
    },
});

// 公共方法：显示提示框
function showModal(content, successCallback = null, showLogin = false) {
    wx.showModal({
        title: '提示',
        content: content,
        showCancel: showLogin,
        cancelText: showLogin ? '取消' : '',
        confirmText: showLogin ? '去登录' : '确定',
        success: function(res) {
            if (res.confirm) {
                if (showLogin) {
                    wx.navigateTo({
                        url: '/pages/login/login'
                    });
                } else if (typeof successCallback === 'function') {
                    successCallback();
                }
            }
        }
    });
}
