const app = getApp()

Page({
  /**
   * 页面的初始数据
   */
  data: {
    title: '',
    content: '',
    titleLength: 0,
    contentLength: 0,
    watermarkImages: [],
    originalImages: [],
    watermarkTempFiles: [], // 水印图片临时文件路径
    originalTempFiles: [], // 原图临时文件路径
    uploadedWatermarkIds: [], // 上传成功后的水印图片ID
    uploadedOriginalIds: [], // 上传成功后的原图ID
    
    // 栏目数据
    parentCategories: [], // 一级栏目数据
    childCategories: {}, // 二级栏目数据，按父栏目ID分组
    
    // 多列选择器数据
    categoryColumns: [[], []], // 两列选择器数据
    categoryIndex: [0, 0], // 当前选中的栏目索引
    
    canSubmit: false,
    isSubmitting: false,
    isLoadingCategories: true,
    selectedCategoryId: 0, // 最终选择的栏目ID
    userInfo: null // 用户信息
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    // 检查用户登录状态
    const member_uid = wx.getStorageSync('member_uid');
    const member_auth = wx.getStorageSync('member_auth');
    const member_name = wx.getStorageSync('member_name');
    
    // 打印登录状态，用于调试
    console.log('当前登录状态:', {
      member_uid: member_uid,
      member_auth: member_auth,
      member_name: member_name
    });
    
    if (!member_uid || !member_auth) {
      wx.showModal({
        title: '提示',
        content: '请先登录后再发布问题',
        confirmText: '去登录',
        success: (res) => {
          if (res.confirm) {
            wx.navigateTo({
              url: '../login/login?redirect=../wenda/publish'
            });
          } else {
            wx.navigateBack();
          }
        }
      });
      return;
    }
    
    // 获取用户信息
    this.getUserInfo();
    
    // 加载栏目分类
    this.loadParentCategories();
  },
  
  /**
   * 获取用户信息
   */
  getUserInfo: function() {
    // 记录当前获取用户信息的时间，用于调试
    console.log('开始获取用户信息，时间:', new Date().toISOString());
    
    // 首先检查登录状态
    const member_uid = wx.getStorageSync('member_uid');
    const member_auth = wx.getStorageSync('member_auth');
    const member_name = wx.getStorageSync('member_name');
    
    if (!member_uid || !member_auth) {
      console.log('未检测到登录信息，无法获取用户信息');
      return;
    }
    
    // 优先从全局数据获取用户信息
    if (app.globalData.member && app.globalData.member.username) {
      this.setData({
        userInfo: app.globalData.member
      });
      console.log('从全局获取到用户信息:', app.globalData.member);
      
      // 如果存在member_name但全局数据中没有记录，则更新全局数据
      if (member_name && !app.globalData.member.name) {
        app.globalData.member.name = member_name;
      }
      return;
    }
    
    // 尝试从本地缓存获取
    const userInfo = wx.getStorageSync('member');
    if (userInfo) {
      // 如果存在member_name但缓存中没有，则添加
      if (member_name && !userInfo.name) {
        userInfo.name = member_name;
      }
      
      this.setData({
        userInfo: userInfo
      });
      console.log('从缓存获取到用户信息:', userInfo);
      
      // 同时更新全局数据
      app.globalData.member = userInfo;
      return;
    }
    
    // 如果本地也没有，则从接口获取
    console.log('从本地未获取到用户信息，准备从接口获取, uid:', member_uid);
    
    wx.request({
      url: app.globalData.http_api + 's=member&c=api&m=userinfo&api_auth_uid=' + member_uid + '&api_auth_code=' + member_auth,
      method: 'GET',
      success: (res) => {
        console.log('从接口获取用户信息结果:', res.data);
        if (res.data && res.data.code === 1 && res.data.data) {
          const userInfo = res.data.data;
          
          // 如果接口返回的数据中没有name字段但存在member_name，则添加
          if (member_name && !userInfo.name) {
            userInfo.name = member_name;
          }
          
          this.setData({
            userInfo: userInfo
          });
          
          // 缓存用户信息
          wx.setStorageSync('member', userInfo);
          
          // 更新全局数据
          app.globalData.member = userInfo;
          
          console.log('成功从接口获取到用户信息:', userInfo);
        } else {
          console.error('获取用户信息失败:', res.data);
        }
      },
      fail: (err) => {
        console.error('请求用户信息接口失败:', err);
      }
    });
  },
  
  /**
   * 加载父级栏目
   */
  loadParentCategories: function() {
    wx.showLoading({
      title: '加载分类...',
      mask: true
    });
    
    const url = app.globalData.http_api + 's=httpapi&m=category&mid=wenda&pid=0&appid=' + app.globalData.appid + '&appsecret=' + app.globalData.appsecret;
    
    wx.request({
      url: url,
      method: 'GET',
      success: (res) => {
        if (res.data && res.data.code === 1 && res.data.data) {
          const data = res.data.data;
          const parentCategories = [];
          const columnData = [[], []];
          const childCategories = {};
          
          // 将对象转换为数组
          Object.keys(data).forEach(key => {
            const category = {
              id: parseInt(data[key].id),
              pid: parseInt(data[key].pid),
              name: data[key].name,
              child: parseInt(data[key].child),
              childids: data[key].childids ? data[key].childids.split(',') : []
            };
            
            parentCategories.push(category);
            columnData[0].push(category.name);
          });
          
          // 排序父级栏目
          parentCategories.sort((a, b) => a.id - b.id);
          
          // 如果存在父级栏目，加载第一个父级栏目的子栏目
          if (parentCategories.length > 0) {
            this.setData({
              parentCategories,
              categoryColumns: columnData,
              isLoadingCategories: true
            });
            
            this.loadChildCategories(parentCategories[0].id);
          } else {
            this.setData({
              parentCategories,
              categoryColumns: columnData,
              isLoadingCategories: false
            });
            
            wx.hideLoading();
          }
        } else {
          this.setData({
            isLoadingCategories: false
          });
          
          wx.hideLoading();
          wx.showToast({
            title: res.data.msg || '加载分类失败',
            icon: 'none'
          });
        }
      },
      fail: (err) => {
        console.error('加载父级分类失败:', err);
        
        this.setData({
          isLoadingCategories: false
        });
        
        wx.hideLoading();
        wx.showToast({
          title: '网络请求失败',
          icon: 'none'
        });
      }
    });
  },
  
  /**
   * 加载子栏目
   */
  loadChildCategories: function(parentId) {
    const url = app.globalData.http_api + 's=httpapi&m=category&mid=wenda&pid=' + parentId + '&appid=' + app.globalData.appid + '&appsecret=' + app.globalData.appsecret;
    
    wx.request({
      url: url,
      method: 'GET',
      success: (res) => {
        if (res.data && res.data.code === 1 && res.data.data) {
          const data = res.data.data;
          const childList = [];
          const columnData = [...this.data.categoryColumns];
          
          // 将对象转换为数组
          Object.keys(data).forEach(key => {
            childList.push({
              id: parseInt(data[key].id),
              pid: parseInt(data[key].pid),
              name: data[key].name
            });
          });
          
          // 排序子栏目
          childList.sort((a, b) => a.id - b.id);
          
          // 更新子栏目数据
          const childCategories = {...this.data.childCategories};
          childCategories[parentId] = childList;
          
          // 更新第二列数据
          columnData[1] = childList.map(item => item.name);
          
          const selectedCategoryId = childList.length > 0 ? childList[0].id : parentId;
          
          this.setData({
            childCategories,
            categoryColumns: columnData,
            isLoadingCategories: false,
            selectedCategoryId
          });
          
          this.checkCanSubmit();
        } else {
          // 如果没有子栏目，使用父栏目作为选择
          const columnData = [...this.data.categoryColumns];
          columnData[1] = ['请选择'];
          
          this.setData({
            categoryColumns: columnData,
            isLoadingCategories: false,
            selectedCategoryId: parentId
          });
          
          this.checkCanSubmit();
        }
        
        wx.hideLoading();
      },
      fail: (err) => {
        console.error('加载子分类失败:', err);
        
        // 出错时，使用父栏目作为选择
        const columnData = [...this.data.categoryColumns];
        columnData[1] = ['请选择'];
        
        this.setData({
          categoryColumns: columnData,
          isLoadingCategories: false,
          selectedCategoryId: parentId
        });
        
        this.checkCanSubmit();
        wx.hideLoading();
      }
    });
  },

  /**
   * 栏目选择变化处理
   */
  bindMultiPickerChange: function(e) {
    const categoryIndex = e.detail.value;
    const parentIndex = categoryIndex[0];
    const childIndex = categoryIndex[1];
    
    const parentCategory = this.data.parentCategories[parentIndex];
    let selectedCategoryId = parentCategory.id;
    
    // 如果有子栏目且子栏目列表不为空
    if (this.data.childCategories[parentCategory.id] && 
        this.data.childCategories[parentCategory.id].length > 0 &&
        childIndex < this.data.childCategories[parentCategory.id].length) {
      selectedCategoryId = this.data.childCategories[parentCategory.id][childIndex].id;
    }
    
    this.setData({
      categoryIndex,
      selectedCategoryId
    });
    
    this.checkCanSubmit();
  },
  
  /**
   * 栏目选择列变化处理
   */
  bindMultiPickerColumnChange: function(e) {
    const data = {
      categoryColumns: this.data.categoryColumns,
      categoryIndex: this.data.categoryIndex
    };
    
    data.categoryIndex[e.detail.column] = e.detail.value;
    
    // 如果是第一列(父栏目)变化，则需要更新第二列(子栏目)数据
    if (e.detail.column === 0) {
      const parentIndex = e.detail.value;
      const parentId = this.data.parentCategories[parentIndex].id;
      
      // 如果已经加载了该父栏目的子栏目
      if (this.data.childCategories[parentId]) {
        data.categoryColumns[1] = this.data.childCategories[parentId].map(item => item.name);
        data.categoryIndex[1] = 0;
        
        // 更新selectedCategoryId
        const selectedCategoryId = this.data.childCategories[parentId].length > 0 ? 
                                 this.data.childCategories[parentId][0].id : parentId;
        
        this.setData({
          ...data,
          selectedCategoryId
        });
        
        this.checkCanSubmit();
      } else {
        // 需要加载该父栏目的子栏目
        this.setData({
          ...data,
          isLoadingCategories: true
        });
        
        this.loadChildCategories(parentId);
      }
    }
  },

  /**
   * 标题输入处理
   */
  onTitleInput: function (e) {
    const value = e.detail.value;
    this.setData({
      title: value,
      titleLength: value.length
    });
    this.checkCanSubmit();
  },

  /**
   * 内容输入处理
   */
  onContentInput: function (e) {
    const value = e.detail.value;
    this.setData({
      content: value,
      contentLength: value.length
    });
    this.checkCanSubmit();
  },

  /**
   * 检查是否可以提交
   */
  checkCanSubmit: function () {
    const { title, content, selectedCategoryId } = this.data;
    const canSubmit = title.length >= 5 && content.length >= 10 && selectedCategoryId > 0;
    this.setData({ canSubmit });
  },

  /**
   * 选择图片
   */
  chooseImage: function (e) {
    const type = e.currentTarget.dataset.type; // 'watermark' 或 'original'
    const images = type === 'watermark' ? this.data.watermarkImages : this.data.originalImages;
    const tempFiles = type === 'watermark' ? this.data.watermarkTempFiles : this.data.originalTempFiles;
    
    // 修改最大上传数量限制为30张
    const maxImageCount = 30;
    const remainCount = maxImageCount - images.length;
    
    if (remainCount <= 0) {
      wx.showToast({
        title: `最多上传${maxImageCount}张图片`,
        icon: 'none'
      });
      return;
    }

    wx.chooseImage({
      count: remainCount,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        // 检查图片大小，每张不超过10M
        const maxSize = 10 * 1024 * 1024; // 10MB
        const validFiles = [];
        const validPaths = [];
        let hasInvalidSize = false;
        
        // 检查每张图片大小
        res.tempFiles.forEach(file => {
          if (file.size <= maxSize) {
            validFiles.push(file.path);
            validPaths.push(file.path);
          } else {
            hasInvalidSize = true;
          }
        });
        
        // 如果有超过大小限制的图片，显示提示
        if (hasInvalidSize) {
          wx.showToast({
            title: '已过滤大于10M的图片',
            icon: 'none'
          });
        }
        
        // 如果没有有效的图片，直接返回
        if (validFiles.length === 0) {
          wx.showToast({
            title: '请选择小于10M的图片',
            icon: 'none'
          });
          return;
        }
        
        // 更新临时文件路径
        const newTempFiles = [...tempFiles, ...validPaths];
        // 更新预览图片
        const newImages = [...images, ...validPaths];
        
        if (type === 'watermark') {
          this.setData({
            watermarkImages: newImages,
            watermarkTempFiles: newTempFiles
          });
        } else {
          this.setData({
            originalImages: newImages,
            originalTempFiles: newTempFiles
          });
        }
      }
    });
  },

  /**
   * 移除图片
   */
  removeImage: function (e) {
    const type = e.currentTarget.dataset.type; // 'watermark' 或 'original'
    const index = e.currentTarget.dataset.index;
    
    if (type === 'watermark') {
      const { watermarkImages, watermarkTempFiles, uploadedWatermarkIds } = this.data;
      
      // 移除对应索引的图片
      watermarkImages.splice(index, 1);
      watermarkTempFiles.splice(index, 1);
      
      if (uploadedWatermarkIds.length > index) {
        uploadedWatermarkIds.splice(index, 1);
      }
      
      this.setData({
        watermarkImages,
        watermarkTempFiles,
        uploadedWatermarkIds
      });
    } else {
      const { originalImages, originalTempFiles, uploadedOriginalIds } = this.data;
      
      // 移除对应索引的图片
      originalImages.splice(index, 1);
      originalTempFiles.splice(index, 1);
      
      if (uploadedOriginalIds.length > index) {
        uploadedOriginalIds.splice(index, 1);
      }
      
      this.setData({
        originalImages,
        originalTempFiles,
        uploadedOriginalIds
      });
    }
  },

  /**
   * 预览图片
   */
  previewImage: function (e) {
    const type = e.currentTarget.dataset.type; // 'watermark' 或 'original'
    const url = e.currentTarget.dataset.url;
    const images = type === 'watermark' ? this.data.watermarkImages : this.data.originalImages;
    
    wx.previewImage({
      current: url,
      urls: images
    });
  },

  /**
   * 上传图片
   */
  uploadImagesByType: function (type) {
    return new Promise((resolve, reject) => {
      const tempFiles = type === 'watermark' ? this.data.watermarkTempFiles : this.data.originalTempFiles;
      // 水印图片fid=507，原图fid=508
      const fid = type === 'watermark' ? '507' : '508'; 
      
      if (tempFiles.length === 0) {
        resolve([]);
        return;
      }
      
      const uploadedIds = [];
      let uploadCount = 0;
      
      // 显示上传进度
      wx.showLoading({
        title: '正在上传' + (type === 'watermark' ? '水印图片' : '原图') + '...',
        mask: true
      });
      
      // 获取验证信息
      const member_uid = wx.getStorageSync('member_uid');
      const member_auth = wx.getStorageSync('member_auth');
      
      tempFiles.forEach((tempFilePath, index) => {
        // 构建上传URL - 参考cmdaedit.js中的上传接口格式
        const uploadUrl = `${app.globalData.http_api}s=api&c=file&m=upload&api_auth_uid=${member_uid}&api_auth_code=${member_auth}&fid=${fid}`;
        
        console.log(`上传${type === 'watermark' ? '水印图片' : '原图'}:`, tempFilePath);
        console.log('上传URL:', uploadUrl);
        
        wx.uploadFile({
          url: uploadUrl,
          filePath: tempFilePath,
          name: 'file_data', // 使用file_data参数名
          formData: {
            'type': type === 'watermark' ? 'xgzp' : 'ytzp',
            'appid': app.globalData.appid,
            'appsecret': app.globalData.appsecret,
            'uid': member_uid,
            'siteid': 1
          },
          success: (res) => {
            try {
              console.log('上传返回原始数据:', res.data);
              let data;
              
              // 尝试解析JSON数据
              try {
                data = JSON.parse(res.data);
              } catch(e) {
                // 如果解析失败，尝试提取纯数字作为ID
                console.log('JSON解析失败，尝试直接提取ID');
                const matches = res.data.match(/\d+/);
                if (matches && matches.length > 0) {
                  uploadedIds.push(matches[0]);
                  console.log('直接从返回数据提取ID:', matches[0]);
                  return;
                } else {
                  console.error('无法解析返回数据:', res.data);
                  return;
                }
              }
              
              // 解析成功，提取ID
              if (data) {
                console.log('解析后的数据:', data);
                
                // 方式1: 直接从code字段获取ID（常见情况）
                if (typeof data.code === 'number' && data.code > 1) {
                  console.log('从code获取图片ID:', data.code);
                  uploadedIds.push(data.code.toString());
                } 
                // 方式2: 从data.id字段获取ID
                else if (data.data && data.data.id) {
                  console.log('从data.id获取图片ID:', data.data.id);
                  uploadedIds.push(data.data.id.toString());
                }
                // 方式3: data.data本身是字符串ID
                else if (data.data && typeof data.data === 'string' && /^\d+$/.test(data.data.trim())) {
                  console.log('从data字符串获取图片ID:', data.data);
                  uploadedIds.push(data.data.trim());
                }
                // 方式4: 直接从data对象中找id属性
                else if (data.id) {
                  console.log('从根对象id属性获取图片ID:', data.id);
                  uploadedIds.push(data.id.toString());
                }
                else {
                  console.error('未找到有效的图片ID:', data);
                  wx.showToast({
                    title: '图片上传成功但ID获取失败',
                    icon: 'none'
                  });
                }
              }
            } catch (e) {
              console.error('图片上传解析失败:', e, res.data);
            }
          },
          fail: (err) => {
            console.error('图片上传失败:', err);
          },
          complete: () => {
            uploadCount++;
            if (uploadCount === tempFiles.length) {
              wx.hideLoading();
              console.log(`${type}上传完成，得到IDs:`, uploadedIds);
              resolve(uploadedIds);
            }
          }
        });
      });
    });
  },

  /**
   * 上传图片
   */
  uploadImages: async function () {
    try {
      // 上传水印图片
      const watermarkIds = await this.uploadImagesByType('watermark');
      console.log('上传完成的水印图片IDs:', watermarkIds);
      
      // 上传原图
      const originalIds = await this.uploadImagesByType('original');
      console.log('上传完成的原图IDs:', originalIds);
      
      // 保存上传成功的图片ID
      this.setData({
        uploadedWatermarkIds: watermarkIds,
        uploadedOriginalIds: originalIds
      });
      
      return {
        watermarkIds,
        originalIds
      };
    } catch (error) {
      console.error('上传图片失败:', error);
      throw error;
    }
  },

  /**
   * 提交问题
   */
  submitQuestion: async function () {
    if (!this.data.canSubmit || this.data.isSubmitting) return;
    
    const { title, content, selectedCategoryId, userInfo } = this.data;
    
    // 检查标题和内容长度
    if (title.length < 5) {
      wx.showToast({
        title: '标题至少需要5个字',
        icon: 'none'
      });
      return;
    }
    
    if (content.length < 10) {
      wx.showToast({
        title: '内容至少需要10个字',
        icon: 'none'
      });
      return;
    }
    
    if (selectedCategoryId === 0) {
      wx.showToast({
        title: '请选择问题分类',
        icon: 'none'
      });
      return;
    }
    
    // 获取用户信息
    const member_uid = wx.getStorageSync('member_uid');
    const member_auth = wx.getStorageSync('member_auth');
    const member_name = wx.getStorageSync('member_name');
    
    if (!member_uid || !member_auth) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }
    
    // 获取正确的用户名（多个来源尝试）
    let username = '微信用户';
    let userid = member_uid;
    let name = member_name || '';
    
    // 1. 首先从当前页面的userInfo数据中获取
    if (userInfo) {
      if (userInfo.username) {
        username = userInfo.username;
        console.log('从页面userInfo获取用户名:', username);
      }
      if (userInfo.name) {
        name = userInfo.name;
        console.log('从页面userInfo获取name:', name);
      }
    } 
    // 2. 从全局变量获取
    else if (app.globalData.member) {
      if (app.globalData.member.username) {
        username = app.globalData.member.username;
        console.log('从全局变量获取用户名:', username);
      }
      if (app.globalData.member.name) {
        name = app.globalData.member.name;
        console.log('从全局变量获取name:', name);
      }
    }
    // 3. 从本地存储获取
    else {
      const localMember = wx.getStorageSync('member');
      if (localMember) {
        if (localMember.username) {
          username = localMember.username;
          console.log('从本地存储获取用户名:', username);
        }
        if (localMember.name) {
          name = localMember.name;
          console.log('从本地存储获取name:', name);
        }
      } else {
        console.warn('无法获取用户名，使用默认值:', username);
        console.warn('无法获取name，使用默认值:', name);
      }
    }
    
    console.log('最终使用的用户信息:', {username, userid, name});
    
    this.setData({ isSubmitting: true });
    
    wx.showLoading({
      title: '正在提交...',
      mask: true
    });
    
    try {
      // 1. 上传图片
      const uploadResult = await this.uploadImages();
      console.log('图片上传结果:', uploadResult);
      
      // 2. 提交问题
      const catid = selectedCategoryId;
      
      // 在URL中添加用户认证信息
      const url = app.globalData.http_api + 's=wenda&c=post&catid=' + catid + 
                  '&api_auth_uid=' + member_uid + 
                  '&api_auth_code=' + member_auth;
      
      console.log('提交URL:', url);
      
      // 构建表单数据
      let formData = {
        appid: app.globalData.appid,
        appsecret: app.globalData.appsecret,
        // 确保用户信息正确传递
        uid: member_uid,
        api_auth_uid: member_uid,
        api_auth_code: member_auth,
        member_uid: member_uid,
        member_username: username,
        member_name: name, // 添加name字段
        is_form: 1,
        is_ajax: 1,
        is_admin: 0,
        catid: catid,
        model: 'wenda',
        module: 'wenda',
        action: 'post',
        'data[title]': title,
        'data[content]': content,
        'data[keywords]': '',
        'data[author]': name, // 将username改为name
        'data[name]': name, // 在data中也添加name字段
        'is_auto_thumb_content': 1,
        'is_auto_description_content': 1,
        'is_remove_a_content': 1
      };
      
      // 添加水印图片数据
      if (uploadResult.watermarkIds && uploadResult.watermarkIds.length > 0) {
        uploadResult.watermarkIds.forEach((id, index) => {
          formData[`data[xgzp][${index}]`] = id;
        });
      }
      
      // 添加原图数据
      if (uploadResult.originalIds && uploadResult.originalIds.length > 0) {
        uploadResult.originalIds.forEach((id, index) => {
          formData[`data[ytzp][${index}]`] = id;
        });
      }
      
      console.log('提交数据:', formData);
      
      // 将formData转换为x-www-form-urlencoded格式的字符串
      let formDataString = Object.keys(formData).map(key => {
        return encodeURIComponent(key) + '=' + encodeURIComponent(formData[key] === null ? '' : formData[key]);
      }).join('&');
      
      // 获取所有存储的cookies
      const cookies = wx.getStorageSync('cookies') || '';
      console.log('使用的cookies:', cookies);
      
      wx.request({
        url: url,
        method: 'POST',
        header: {
          'content-type': 'application/x-www-form-urlencoded',
          'Cookie': cookies // 添加Cookie认证
        },
        data: formDataString,
        success: (res) => {
          wx.hideLoading();
          
          console.log('提交结果:', res.data);
          
          if (res.data && res.data.code === 1) {
            wx.showToast({
              title: '发布成功',
              icon: 'success'
            });
            
            // 更新列表页面的标记，表示需要刷新
            app.globalData.needRefreshQuestionList = true;
            
            // 延迟返回列表页
            setTimeout(() => {
              wx.navigateBack();
            }, 1500);
          } else {
            wx.showToast({
              title: res.data.msg || '发布失败，请重试',
              icon: 'none'
            });
            this.setData({ isSubmitting: false });
          }
        },
        fail: (err) => {
          console.error('提交问题失败:', err);
          wx.hideLoading();
          wx.showToast({
            title: '网络错误，请重试',
            icon: 'none'
          });
          this.setData({ isSubmitting: false });
        }
      });
    } catch (error) {
      console.error('发布问题异常:', error);
      wx.hideLoading();
      wx.showToast({
        title: '发布失败，请重试',
        icon: 'none'
      });
      this.setData({ isSubmitting: false });
    }
  },

  /**
   * 选择父分类（新UI用）
   */
  selectParentCategory: function(e) {
    const index = e.currentTarget.dataset.index;
    const parentId = this.data.parentCategories[index].id;
    
    console.log('选择父分类:', index, parentId);
    
    // 如果选择的是同一个父分类，不做处理
    if (this.data.categoryIndex[0] === index) {
      return;
    }
    
    // 更新选中的索引
    this.setData({
      'categoryIndex[0]': index,
      'categoryIndex[1]': 0,
      isLoadingCategories: true
    });
    
    // 如果已经加载了子分类数据
    if (this.data.childCategories[parentId]) {
      // 更新selectedCategoryId
      const selectedCategoryId = this.data.childCategories[parentId].length > 0 ? 
        this.data.childCategories[parentId][0].id : parentId;
      
      this.setData({
        isLoadingCategories: false,
        selectedCategoryId
      });
      
      this.checkCanSubmit();
    } else {
      // 需要加载子分类
      this.loadChildCategories(parentId);
    }
  },
  
  /**
   * 选择子分类（新UI用）
   */
  selectChildCategory: function(e) {
    const index = e.currentTarget.dataset.index;
    const parentIndex = this.data.categoryIndex[0];
    const parentId = this.data.parentCategories[parentIndex].id;
    
    console.log('选择子分类:', index);
    
    // 如果子分类数据存在
    if (this.data.childCategories[parentId] && this.data.childCategories[parentId].length > index) {
      const selectedCategoryId = this.data.childCategories[parentId][index].id;
      
      this.setData({
        'categoryIndex[1]': index,
        selectedCategoryId
      });
      
      this.checkCanSubmit();
    }
  }
}); 