const app = getApp();

Page({
  data: {
    villager: null, // 村民信息
    loading: true, // 加载状态
    totalPhotos: 0, // 总照片数

    // 图片相关数据
    tempGrzpList: [], // 身份证件照片
    tempQtzjzpList: [], // 户口薄照片
    tempFwzpList: [], // 房屋照片
    tempGczpList: [], // 改厕照片
    thumbUrl: '', // 个人照片缩略图

    // 原始图片数据（用于对比变化）
    originalGrzpList: [],
    originalQtzjzpList: [],
    originalFwzpList: [],
    originalGczpList: [],
    originalThumbUrl: '',

    // 删除的图片列表（用于后端删除）
    deletedImages: {
      grzp: [],
      qtzjzp: [],
      fwzp: [],
      gczp: [],
      thumb: []
    }
  },

  onLoad: function(options) {
    // 检查传入的ID参数
    if (!options.id) {
      wx.showToast({
        title: '参数错误，缺少村民ID',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
      return;
    }

    this.villagerId = options.id;
    this.scrollTarget = options.scrollTo || ''; // 保存滚动目标

    // 检查权限
    if (!this.checkPermission()) {
      return;
    }

    // 加载村民详情
    this.loadVillagerDetail();
  },

  /**
   * 检查权限
   */
  checkPermission: function() {
    const member = wx.getStorageSync('member');
    const member_auth = wx.getStorageSync('member_auth');
    const member_uid = wx.getStorageSync('member_uid');
    
    if (!member || !member_auth || !member_uid) {
      wx.showModal({
        title: '提示',
        content: '请先登录',
        showCancel: false,
        success: () => {
          wx.reLaunch({
            url: '/pages/login/login'
          });
        }
      });
      return false;
    }
    
    if (!member.is_admin || member.is_admin <= 0) {
      wx.showModal({
        title: '权限不足',
        content: '只有管理员可以更新村民图片',
        showCancel: false,
        success: () => {
          wx.navigateBack();
        }
      });
      return false;
    }
    
    return true;
  },

  /**
   * 加载村民详情
   */
  loadVillagerDetail: function() {
    this.setData({ loading: true });
    
    const member_uid = wx.getStorageSync('member_uid');
    const member_auth = wx.getStorageSync('member_auth');
    
    const requestUrl = app.globalData.http_api + 
      's=cmda&c=show&id=' + this.villagerId + 
      '&api_call_function=module_show' + 
      '&api_auth_uid=' + member_uid + 
      '&api_auth_code=' + member_auth;
    
    wx.showLoading({
      title: '加载中...'
    });
    
    wx.request({
      url: requestUrl,
      method: 'GET',
      success: res => {
        wx.hideLoading();
        
        if (res.data.code == 1 && res.data.data) {
          const villagerData = res.data.data;

          // 调试日志：查看原始数据
          console.log('村民原始数据:', villagerData);
          console.log('身份证原始数据:', villagerData.grzp);

          // 处理图片数据
          const grzpList = this.processImageList(villagerData.grzp);
          const qtzjzpList = this.processImageList(villagerData.qtzjzp);
          const fwzpList = this.processImageList(villagerData.fwzp);
          const gczpList = this.processImageList(villagerData.gczp);

          // 处理缩略图数据 - 保持原始格式以便正确提取ID
          let thumbUrl = '';
          if (villagerData.thumb) {
            console.log('原始 thumb 数据:', villagerData.thumb, '类型:', typeof villagerData.thumb);

            // 直接使用原始数据，不通过 processImageList 处理
            // 这样可以保持对象格式，便于后续正确提取 ID
            thumbUrl = villagerData.thumb;
            console.log('保持原始格式的 thumbUrl:', thumbUrl, '类型:', typeof thumbUrl);
          }

          // 调试日志：查看处理后的数据
          console.log('身份证处理后数据:', grzpList);
          console.log('户口薄处理后数据:', qtzjzpList);
          console.log('房屋处理后数据:', fwzpList);
          console.log('改厕处理后数据:', gczpList);
          
          // 为页面显示准备 URL 列表
          const grzpDisplayList = grzpList.map(item => this.getImageDisplayUrl(item));
          const qtzjzpDisplayList = qtzjzpList.map(item => this.getImageDisplayUrl(item));
          const fwzpDisplayList = fwzpList.map(item => this.getImageDisplayUrl(item));
          const gczpDisplayList = gczpList.map(item => this.getImageDisplayUrl(item));
          const thumbDisplayUrl = this.getImageDisplayUrl(thumbUrl);

          console.log('thumbDisplayUrl:', thumbDisplayUrl);

          this.setData({
            villager: villagerData,
            // 原始数据（用于保存时处理）
            tempGrzpList: [...grzpList],
            tempQtzjzpList: [...qtzjzpList],
            tempFwzpList: [...fwzpList],
            tempGczpList: [...gczpList],
            thumbUrl: thumbUrl,
            // 显示用的 URL 列表
            grzpDisplayList: [...grzpDisplayList],
            qtzjzpDisplayList: [...qtzjzpDisplayList],
            fwzpDisplayList: [...fwzpDisplayList],
            gczpDisplayList: [...gczpDisplayList],
            thumbDisplayUrl: thumbDisplayUrl,
            // 保存原始数据
            originalGrzpList: [...grzpList],
            originalQtzjzpList: [...qtzjzpList],
            originalFwzpList: [...fwzpList],
            originalGczpList: [...gczpList],
            originalThumbUrl: thumbUrl,
            loading: false
          }, () => {
            // 数据加载完成后计算总照片数
            this.calculateTotalPhotos();

            // 如果有滚动目标，延迟执行滚动定位
            if (this.scrollTarget) {
              setTimeout(() => {
                this.scrollToTarget(this.scrollTarget);
              }, 500);
            }
          });
        } else {
          wx.showToast({
            title: '加载村民信息失败',
            icon: 'none'
          });
          setTimeout(() => {
            wx.navigateBack();
          }, 1500);
        }
      },
      fail: err => {
        wx.hideLoading();
        console.error('加载村民详情失败:', err);
        wx.showToast({
          title: '网络错误',
          icon: 'none'
        });
        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
      }
    });
  },

  /**
   * 获取图片显示 URL
   */
  getImageDisplayUrl: function(imageItem) {
    if (!imageItem) return '';

    if (typeof imageItem === 'string') {
      // 如果是数字 ID，需要构建完整 URL
      if (/^\d+$/.test(imageItem)) {
        return `${app.globalData.http_api}s=api&c=file&m=show&id=${imageItem}`;
      }
      // 如果是临时路径（新选择的图片），直接返回
      if (imageItem.startsWith('http://tmp/') || imageItem.startsWith('wxfile://')) {
        return imageItem;
      }
      // 如果已经是完整 URL，直接返回
      return imageItem;
    } else if (typeof imageItem === 'object' && imageItem !== null) {
      // 对象格式，优先使用 file 字段（完整 URL）
      if (imageItem.file) {
        return imageItem.file;
      } else if (imageItem.url) {
        return imageItem.url;
      } else if (imageItem.id) {
        // 如果只有 ID，构建完整 URL
        return `${app.globalData.http_api}s=api&c=file&m=show&id=${imageItem.id}`;
      }
    }

    return '';
  },

  /**
   * 处理图片列表
   */
  processImageList: function(imageData) {
    if (!imageData) return [];

    // 处理字符串格式
    if (typeof imageData === 'string') {
      try {
        const parsed = JSON.parse(imageData);
        return this.processImageList(parsed); // 递归处理解析后的数据
      } catch (e) {
        return imageData.split(',').filter(url => url.trim());
      }
    }

    // 处理数组格式
    if (Array.isArray(imageData)) {
      return imageData.map(item => {
        // 如果是对象格式 {id: "xxx", file: "xxx"}，保持对象结构
        if (typeof item === 'object' && item !== null) {
          // 保持完整的对象结构，这样后续处理时可以同时访问 id 和 file 字段
          return item;
        }
        // 如果是字符串，直接返回
        return typeof item === 'string' ? item : '';
      }).filter(item => {
        // 过滤无效项
        if (typeof item === 'string') {
          return item.trim();
        } else if (typeof item === 'object' && item !== null) {
          return item.id || item.file || item.url;
        }
        return false;
      });
    }

    // 处理单个对象
    if (typeof imageData === 'object' && imageData !== null) {
      // 如果有有效的图片信息，返回对象本身
      if (imageData.id || imageData.file || imageData.url) {
        return [imageData];
      }
    }

    return [];
  },

  /**
   * 选择图片
   */
  chooseImage: function(e) {
    const type = e.currentTarget.dataset.type;
    const typeMap = {
      'grzp': 'tempGrzpList',
      'fwzp': 'tempFwzpList',
      'qtzjzp': 'tempQtzjzpList',
      'gczp': 'tempGczpList'
    };
    
    const listKey = typeMap[type];
    if (!listKey) return;
    
    const currentList = this.data[listKey];
    const count = 9 - currentList.length;
    
    if (count <= 0) {
      wx.showToast({
        title: '最多只能上传9张图片',
        icon: 'none'
      });
      return;
    }
    
    wx.chooseImage({
      count: count,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        const newList = [...currentList, ...res.tempFilePaths];
        // 同时更新显示列表
        const displayKey = type + 'DisplayList';
        const newDisplayList = newList.map(item => this.getImageDisplayUrl(item));

        this.setData({
          [listKey]: newList,
          [displayKey]: newDisplayList
        }, () => {
          // 选择图片后重新计算总数
          this.calculateTotalPhotos();
        });
      }
    });
  },

  /**
   * 选择缩略图
   */
  chooseThumb: function() {
    wx.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        const newThumbUrl = res.tempFilePaths[0];
        const newThumbDisplayUrl = this.getImageDisplayUrl(newThumbUrl);

        this.setData({
          thumbUrl: newThumbUrl,
          thumbDisplayUrl: newThumbDisplayUrl
        }, () => {
          // 选择缩略图后重新计算总数
          this.calculateTotalPhotos();
        });
      }
    });
  },

  /**
   * 删除图片
   */
  deleteImage: function(e) {
    const type = e.currentTarget.dataset.type;
    const index = e.currentTarget.dataset.index;
    
    const typeMap = {
      'grzp': 'tempGrzpList',
      'fwzp': 'tempFwzpList',
      'qtzjzp': 'tempQtzjzpList',
      'gczp': 'tempGczpList'
    };
    
    const listKey = typeMap[type];
    if (!listKey) return;
    
    let currentList = [...this.data[listKey]];
    const deletedImage = currentList[index];
    
    // 如果删除的是原有图片，记录到删除列表
    const originalKey = 'original' + listKey.charAt(4).toUpperCase() + listKey.slice(5);
    if (this.data[originalKey].includes(deletedImage)) {
      const deletedImages = { ...this.data.deletedImages };
      deletedImages[type].push(deletedImage);
      this.setData({ deletedImages });
    }
    
    currentList.splice(index, 1);

    // 同时更新显示列表
    const displayKey = type + 'DisplayList';
    const newDisplayList = currentList.map(item => this.getImageDisplayUrl(item));

    this.setData({
      [listKey]: currentList,
      [displayKey]: newDisplayList
    }, () => {
      // 删除图片后重新计算总数
      this.calculateTotalPhotos();
    });
  },

  /**
   * 删除缩略图
   */
  deleteThumb: function() {
    const currentThumb = this.data.thumbUrl;
    
    // 如果删除的是原有缩略图，记录到删除列表
    if (this.data.originalThumbUrl === currentThumb) {
      const deletedImages = { ...this.data.deletedImages };
      deletedImages.thumb.push(currentThumb);
      this.setData({ deletedImages });
    }
    
    this.setData({
      thumbUrl: '',
      thumbDisplayUrl: ''
    }, () => {
      // 删除缩略图后重新计算总数
      this.calculateTotalPhotos();
    });
  },

  /**
   * 预览图片
   */
  previewImage: function(e) {
    const type = e.currentTarget.dataset.type;
    const index = e.currentTarget.dataset.index;
    
    const typeMap = {
      'grzp': 'tempGrzpList',
      'fwzp': 'tempFwzpList',
      'qtzjzp': 'tempQtzjzpList',
      'gczp': 'tempGczpList'
    };
    
    const listKey = typeMap[type];
    if (!listKey) return;
    
    const currentList = this.data[listKey];
    
    wx.previewImage({
      current: currentList[index],
      urls: currentList
    });
  },

  /**
   * 预览缩略图
   */
  previewThumb: function() {
    if (this.data.thumbUrl) {
      wx.previewImage({
        current: this.data.thumbUrl,
        urls: [this.data.thumbUrl]
      });
    }
  },

  /**
   * 保存图片
   */
  saveImages: function() {
    wx.showModal({
      title: '确认保存',
      content: '确定要保存图片更改吗？',
      success: (res) => {
        if (res.confirm) {
          this.doSaveImages();
        }
      }
    });
  },

  /**
   * 执行保存图片
   */
  doSaveImages: function() {
    wx.showLoading({
      title: '上传图片中...'
    });

    // 获取认证信息
    const member_uid = wx.getStorageSync('member_uid');
    const member_auth = wx.getStorageSync('member_auth');

    if (!member_uid || !member_auth) {
      wx.hideLoading();
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }

    // 收集需要上传的新图片
    const uploadTasks = [];

    // 检查各类型图片的变化
    this.collectUploadTasks('grzp', this.data.tempGrzpList, this.data.originalGrzpList, 471, uploadTasks);
    this.collectUploadTasks('qtzjzp', this.data.tempQtzjzpList, this.data.originalQtzjzpList, 482, uploadTasks);
    this.collectUploadTasks('fwzp', this.data.tempFwzpList, this.data.originalFwzpList, 483, uploadTasks);
    this.collectUploadTasks('gczp', this.data.tempGczpList, this.data.originalGczpList, 484, uploadTasks);

    // 检查缩略图变化
    if (this.data.thumbUrl !== this.data.originalThumbUrl && this.data.thumbUrl && typeof this.data.thumbUrl === 'string') {
      // 检查是否是新上传的图片（临时路径）
      if (this.data.thumbUrl.startsWith('http://tmp/') || this.data.thumbUrl.startsWith('wxfile://')) {
        uploadTasks.push({
          type: 'thumb',
          filePath: this.data.thumbUrl,
          fid: 485
        });
      }
    }

    console.log('需要上传的图片任务:', uploadTasks);

    if (uploadTasks.length === 0) {
      // 没有新图片需要上传，直接保存数据
      this.saveImageData();
      return;
    }

    // 执行图片上传
    this.uploadImages(uploadTasks, member_uid, member_auth);
  },

  /**
   * 收集上传任务
   */
  collectUploadTasks: function(type, currentList, originalList, fid, uploadTasks) {
    // 确保列表存在
    if (!Array.isArray(currentList)) {
      console.warn(`${type} currentList 不是数组:`, currentList);
      return;
    }

    // 找出新添加的图片（临时路径）
    currentList.forEach(imageItem => {
      let imagePath = '';

      // 处理不同的数据格式
      if (typeof imageItem === 'string') {
        imagePath = imageItem;
      } else if (typeof imageItem === 'object' && imageItem !== null) {
        imagePath = imageItem.file || imageItem.url || '';
      } else {
        console.warn(`${type} imageItem 格式不正确:`, imageItem, typeof imageItem);
        return;
      }

      // 检查是否是新上传的图片（临时路径）
      if (typeof imagePath === 'string' && (imagePath.startsWith('http://tmp/') || imagePath.startsWith('wxfile://'))) {
        // 检查是否不在原始列表中
        const isInOriginal = originalList.some(originalItem => {
          const originalPath = typeof originalItem === 'string' ? originalItem : (originalItem.file || originalItem.url || '');
          return originalPath === imagePath;
        });

        if (!isInOriginal) {
          uploadTasks.push({
            type: type,
            filePath: imagePath,
            fid: fid
          });
        }
      }
    });
  },

  /**
   * 上传图片
   */
  uploadImages: function(uploadTasks, member_uid, member_auth) {
    let completedCount = 0;
    let totalCount = uploadTasks.length;
    let uploadedResults = {};
    let hasError = false;

    // 更新进度提示
    wx.showLoading({
      title: `上传图片 0/${totalCount}`
    });

    uploadTasks.forEach((task, index) => {
      const uploadUrl = `${app.globalData.http_api}s=api&c=file&m=upload&fid=${task.fid}&api_auth_uid=${member_uid}&api_auth_code=${member_auth}`;

      wx.uploadFile({
        url: uploadUrl,
        filePath: task.filePath,
        name: 'file_data',
        formData: {
          is_ajax: 1,
          file_type: 'image'
        },
        success: (res) => {
          try {
            const responseData = JSON.parse(res.data);
            console.log(`${task.type} 上传结果:`, responseData);

            if (responseData.code && responseData.data) {
              // 保存上传结果
              if (!uploadedResults[task.type]) {
                uploadedResults[task.type] = [];
              }
              uploadedResults[task.type].push({
                oldPath: task.filePath,
                newId: responseData.data.id || responseData.code
              });
            } else {
              console.error(`${task.type} 上传失败:`, responseData.msg);
              hasError = true;
            }
          } catch (error) {
            console.error(`${task.type} 解析响应失败:`, error);
            hasError = true;
          }

          completedCount++;
          wx.showLoading({
            title: `上传图片 ${completedCount}/${totalCount}`
          });

          // 检查是否全部完成
          if (completedCount === totalCount) {
            if (hasError) {
              wx.hideLoading();
              wx.showToast({
                title: '部分图片上传失败',
                icon: 'none'
              });
            } else {
              // 所有图片上传成功，保存数据
              this.saveImageData(uploadedResults);
            }
          }
        },
        fail: (error) => {
          console.error(`${task.type} 上传请求失败:`, error);
          hasError = true;
          completedCount++;

          if (completedCount === totalCount) {
            wx.hideLoading();
            wx.showToast({
              title: '图片上传失败',
              icon: 'none'
            });
          }
        }
      });
    });
  },

  /**
   * 保存图片数据到服务器
   */
  saveImageData: function(uploadedResults = {}) {
    wx.showLoading({
      title: '保存数据中...'
    });

    // 构建图片数据
    const imageData = this.buildImageData(uploadedResults);

    // 构建API请求
    const member_uid = wx.getStorageSync('member_uid');
    const member_auth = wx.getStorageSync('member_auth');
    const requestUrl = `${app.globalData.http_api}s=member&app=cmda&c=home&m=edit&id=${this.villagerId}&api_auth_uid=${member_uid}&api_auth_code=${member_auth}`;

    console.log('保存图片数据URL:', requestUrl);
    console.log('图片数据:', imageData);

    wx.request({
      url: requestUrl,
      method: 'POST',
      data: imageData,
      header: {
        'Content-Type': 'application/x-www-form-urlencoded'
      },
      success: (res) => {
        wx.hideLoading();
        console.log('保存图片数据结果:', res.data);

        if (res.data.code == 1) {
          wx.showToast({
            title: '保存成功',
            icon: 'success'
          });

          setTimeout(() => {
            wx.navigateBack();
          }, 1500);
        } else {
          wx.showToast({
            title: res.data.msg || '保存失败',
            icon: 'none'
          });
        }
      },
      fail: (error) => {
        wx.hideLoading();
        console.error('保存图片数据失败:', error);
        wx.showToast({
          title: '网络错误',
          icon: 'none'
        });
      }
    });
  },

  /**
   * 检查图片是否有变化
   */
  hasImageChanges: function(currentList, originalList, uploadedList) {
    // 如果有新上传的图片，说明有变化
    if (uploadedList && uploadedList.length > 0) {
      return true;
    }

    // 比较当前列表和原始列表的长度
    if (currentList.length !== originalList.length) {
      return true;
    }

    // 比较列表内容是否相同
    for (let i = 0; i < currentList.length; i++) {
      const currentItem = currentList[i];
      const originalItem = originalList[i];

      // 获取实际的标识符进行比较
      const currentId = typeof currentItem === 'string' ? currentItem : (currentItem.id || currentItem.file || currentItem.url || '');
      const originalId = typeof originalItem === 'string' ? originalItem : (originalItem.id || originalItem.file || originalItem.url || '');

      if (currentId !== originalId) {
        return true;
      }
    }

    return false;
  },

  /**
   * 构建图片数据
   */
  buildImageData: function(uploadedResults) {
    // 构建基础表单数据
    let formDataString = "is_ajax=1";
    formDataString += "&id=" + this.villagerId;
    formDataString += "&catid=" + (this.data.villager.catid || 2);
    formDataString += "&model=cmda";
    formDataString += "&module=cmda";
    formDataString += "&action=edit";

    // 添加村民完整信息（保持所有现有数据）
    if (this.data.villager) {
      // 遍历村民对象的所有属性，保持完整数据
      for (const key in this.data.villager) {
        if (this.data.villager.hasOwnProperty(key)) {
          const value = this.data.villager[key];

          // 跳过特殊处理的字段和空值
          const skipFields = [
            'id', 'catid', 'grzpList', 'fwzpList', 'qtzjzpList', 'gczpList', 'allImages', 'otherInfo', 'isXiaohu', 'photoCount',
            // 跳过图片相关字段，这些字段会在后面单独处理
            'grzp', 'fwzp', 'qtzjzp', 'gczp', 'thumb'
          ];
          if (skipFields.includes(key) || value === undefined || value === null) {
            continue;
          }

          // 处理数组类型的字段（如户籍属性）
          if (Array.isArray(value)) {
            if (key === 'hujishuxing') {
              // 户籍属性特殊处理
              value.forEach(item => {
                if (typeof item === 'string' || typeof item === 'number') {
                  formDataString += "&data[hujishuxing][]=" + encodeURIComponent(item);
                }
              });
            } else {
              // 其他数组字段 - 确保只处理基本类型
              value.forEach(item => {
                if (typeof item === 'string' || typeof item === 'number') {
                  formDataString += "&data[" + key + "][]=" + encodeURIComponent(item);
                } else {
                  console.warn(`跳过非基本类型的数组项 ${key}:`, item);
                }
              });
            }
          } else {
            // 普通字段 - 确保是基本类型
            if (typeof value === 'string' || typeof value === 'number' || typeof value === 'boolean') {
              formDataString += "&data[" + key + "]=" + encodeURIComponent(value);
            } else {
              console.warn(`跳过非基本类型的字段 ${key}:`, value);
            }
          }
        }
      }
    }

    // 处理各类型图片 - 只更新有变化的字段
    const imageTypes = [
      { key: 'grzp', list: 'tempGrzpList', original: 'originalGrzpList' },
      { key: 'qtzjzp', list: 'tempQtzjzpList', original: 'originalQtzjzpList' },
      { key: 'fwzp', list: 'tempFwzpList', original: 'originalFwzpList' },
      { key: 'gczp', list: 'tempGczpList', original: 'originalGczpList' }
    ];

    imageTypes.forEach(type => {
      const currentList = this.data[type.list] || [];
      const originalList = this.data[type.original] || [];
      const uploadedList = uploadedResults[type.key] || [];

      // 检查该类型的图片是否有变化
      const hasChanges = this.hasImageChanges(currentList, originalList, uploadedList);

      console.log(`${type.key} 图片变化检查:`, hasChanges ? '有变化' : '无变化');

      // 无论是否有变化，都要发送数据以保持完整性
      const finalIds = [];

      // 确保 currentList 是数组
      if (Array.isArray(currentList)) {
        currentList.forEach(imageItem => {
          let imagePath = '';
          let imageId = '';

          // 处理不同的数据格式
          if (typeof imageItem === 'string') {
            // 字符串格式：直接是路径或ID
            imagePath = imageItem;
            imageId = imageItem;
          } else if (typeof imageItem === 'object' && imageItem !== null) {
            // 对象格式：{id: "xxx", file: "xxx"}
            imagePath = imageItem.file || imageItem.url || '';
            // 修复：优先使用 id 字段，如果 id 不存在且是 URL 格式，则从 URL 中提取 ID
            if (imageItem.id) {
              imageId = imageItem.id;
            } else {
              // 如果没有 id 字段，尝试从 file 或 url 中提取数字 ID
              const urlPath = imageItem.file || imageItem.url || '';
              // 尝试从 URL 路径中提取数字 ID（如果是纯数字格式）
              if (/^\d+$/.test(urlPath)) {
                imageId = urlPath;
              } else {
                // 如果是 URL 格式，记录警告但仍然使用（向后兼容）
                console.warn(`${type.key} 图片对象缺少 id 字段，使用 URL 作为标识:`, imageItem);
                imageId = urlPath;
              }
            }
          } else {
            console.warn(`${type.key} imageItem 格式不正确:`, imageItem);
            return;
          }

          // 检查是否是新上传的图片
          const uploaded = uploadedList.find(item => item.oldPath === imagePath);
          if (uploaded) {
            // 使用新上传的ID
            finalIds.push(uploaded.newId);
          } else if (typeof imagePath === 'string' && (imagePath.startsWith('http://tmp/') || imagePath.startsWith('wxfile://'))) {
            // 跳过临时路径（应该已经上传）
            console.warn(`${type.key} 临时路径未找到上传结果:`, imagePath);
          } else {
            // 保留原有的图片ID - 确保是有效的ID
            if (imageId && (typeof imageId === 'string' || typeof imageId === 'number')) {
              // 检查是否是纯数字 ID（正确格式）
              const idStr = imageId.toString();
              if (/^\d+$/.test(idStr)) {
                finalIds.push(idStr);
              } else if (idStr.startsWith('http')) {
                // 如果是 URL 格式，记录警告但仍然保存（向后兼容）
                console.warn(`${type.key} 检测到 URL 格式的图片标识，可能导致数据格式不一致:`, idStr);
                finalIds.push(idStr);
              } else {
                finalIds.push(idStr);
              }
            } else {
              console.warn(`${type.key} 无效的图片ID:`, imageId, '原始数据:', imageItem);
            }
          }
        });
      }

      // 构建表单数据字符串 - 始终发送数据以保持完整性
      finalIds.forEach(id => {
        formDataString += "&data[" + type.key + "][]=" + encodeURIComponent(id);
      });

      console.log(`${type.key} 最终图片ID (${finalIds.length}张):`, finalIds);
    });

    // 处理缩略图 - 始终发送数据以保持完整性
    const thumbHasChanges = this.data.thumbUrl !== this.data.originalThumbUrl ||
                           (uploadedResults.thumb && uploadedResults.thumb.length > 0);

    console.log('缩略图变化检查:', thumbHasChanges ? '有变化' : '无变化');

    // 处理缩略图 - 使用与其他图片字段相同的逻辑
    let thumbId = '';
    console.log('开始处理缩略图，当前 thumbUrl:', this.data.thumbUrl, '类型:', typeof this.data.thumbUrl);
    if (this.data.thumbUrl) {
      // 处理不同的数据格式
      if (typeof this.data.thumbUrl === 'string') {
        // 检查是否是新上传的图片
        if (uploadedResults.thumb && uploadedResults.thumb.length > 0) {
          thumbId = uploadedResults.thumb[0].newId;
        } else if (!this.data.thumbUrl.startsWith('http://tmp/') && !this.data.thumbUrl.startsWith('wxfile://')) {
          // 检查是否是纯数字 ID（正确格式）
          if (/^\d+$/.test(this.data.thumbUrl)) {
            thumbId = this.data.thumbUrl;
            console.log('使用数字ID格式的缩略图:', thumbId);
          } else if (this.data.thumbUrl.startsWith('http')) {
            // 如果是 URL 格式，不发送此字段，避免格式不一致
            console.warn('缩略图是 URL 格式，跳过发送以避免数据格式不一致:', this.data.thumbUrl);
            thumbId = ''; // 不发送 URL 格式的 thumb
          } else {
            thumbId = this.data.thumbUrl;
          }
        }
      } else if (typeof this.data.thumbUrl === 'object' && this.data.thumbUrl !== null) {
        // 处理对象格式的缩略图数据
        console.log('处理对象格式的缩略图:', this.data.thumbUrl);
        if (uploadedResults.thumb && uploadedResults.thumb.length > 0) {
          thumbId = uploadedResults.thumb[0].newId;
          console.log('使用新上传的缩略图ID:', thumbId);
        } else if (this.data.thumbUrl.id) {
          thumbId = this.data.thumbUrl.id;
          console.log('使用对象中的ID字段:', thumbId);
        } else {
          const urlPath = this.data.thumbUrl.file || this.data.thumbUrl.url || '';
          if (/^\d+$/.test(urlPath)) {
            thumbId = urlPath;
            console.log('从URL中提取的数字ID:', thumbId);
          } else {
            console.warn('缩略图对象缺少 id 字段且包含 URL，跳过发送以避免数据格式不一致:', this.data.thumbUrl);
            thumbId = ''; // 不发送 URL 格式的 thumb
          }
        }
      }
    }

    // 发送缩略图数据
    if (thumbId) {
      formDataString += "&data[thumb]=" + encodeURIComponent(thumbId);
      console.log('缩略图ID:', thumbId);
    } else {
      // 如果没有缩略图，发送空值
      formDataString += "&data[thumb]=";
      console.log('没有缩略图，发送空值');
    }

    return formDataString;
  },

  /**
   * 返回上一页
   */
  navigateBack: function() {
    wx.navigateBack();
  },

  /**
   * 计算总照片数
   */
  calculateTotalPhotos: function() {
    const total = this.data.tempGrzpList.length +
                  this.data.tempQtzjzpList.length +
                  this.data.tempFwzpList.length +
                  this.data.tempGczpList.length +
                  (this.data.thumbUrl ? 1 : 0);

    this.setData({
      totalPhotos: total
    });
  },

  /**
   * 预览缩略图
   */
  previewThumb: function() {
    if (this.data.thumbUrl) {
      wx.previewImage({
        current: this.data.thumbUrl,
        urls: [this.data.thumbUrl]
      });
    }
  },

  /**
   * 取消编辑
   */
  cancelEdit: function() {
    wx.showModal({
      title: '提示',
      content: '确定放弃编辑？未保存的更改将丢失',
      success: (res) => {
        if (res.confirm) {
          wx.navigateBack();
        }
      }
    });
  },

  /**
   * 滚动到指定目标
   */
  scrollToTarget: function(target) {
    if (!target) return;

    // 使用 createSelectorQuery 获取目标元素位置
    const query = wx.createSelectorQuery();
    query.select('#' + target).boundingClientRect();
    query.selectViewport().scrollOffset();

    query.exec((res) => {
      if (res[0] && res[1]) {
        const targetTop = res[0].top + res[1].scrollTop;

        // 滚动到目标位置，减去一些偏移量以便更好地显示
        wx.pageScrollTo({
          scrollTop: Math.max(0, targetTop - 100),
          duration: 500
        });
      }
    });
  }
});
