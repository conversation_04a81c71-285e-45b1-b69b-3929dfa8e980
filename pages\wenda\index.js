const app = getApp()

Page({
  /**
   * 页面初始数据
   */
  data: {
    currentCategory: 'all',
    questions: [],
    page: 1,
    limit: 10,
    hasMore: true,
    isLoading: false,
    // 问题状态映射
    statusMap: {
      'unanswered': '0',  // 未解决
      'inprocess': '2',   // 解决中
      'solved': '1'       // 已完成
    }
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    this.loadQuestions()
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {
    this.resetList()
    this.loadQuestions()
    wx.stopPullDownRefresh()
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function() {
    if (this.data.hasMore && !this.data.isLoading) {
      this.loadQuestions();
    }
  },

  /**
   * 重置列表数据
   */
  resetList: function() {
    this.setData({
      questions: [],
      page: 1,
      hasMore: true
    })
  },

  /**
   * 构建API请求URL
   */
  buildRequestUrl: function(params = {}) {
    // 基础URL
    let url = app.globalData.http_api + 
      's=wenda&c=search' +
      '&api_call_function=module_list' +
      '&appid=' + app.globalData.appid +
      '&appsecret=' + app.globalData.appsecret +
      '&more=1';  // 显示栏目模型字段
    
    // 添加分页参数  
    url += '&page=' + (params.page || this.data.page);
    url += '&pagesize=' + (params.pagesize || this.data.limit);
    
    // 添加分类参数
    const category = params.category || this.data.currentCategory;
    if (this.data.statusMap[category]) {
      url += '&is_comment=' + this.data.statusMap[category];
    } else if (category === 'hot') {
      url += '&order=hits'; // 热门按点击量排序
    }
    
    // 添加搜索关键词
    if (params.keyword) {
      url += '&keyword=' + encodeURIComponent(params.keyword);
    }
    
    return url;
  },

  /**
   * 发起网络请求
   */
  requestData: function(url, successCallback) {
    this.setData({ isLoading: true });
    
    wx.request({
      url: url,
      method: 'GET',
      header: {
        'content-type': 'application/json'
      },
      success: (res) => {
        if (res.data && res.data.code === 1 && Array.isArray(res.data.data)) {
          const questions = this.processApiData(res.data.data);
          successCallback(questions);
        } else {
          wx.showToast({
            title: res.data.msg || '数据加载失败',
            icon: 'none'
          });
          this.setData({ isLoading: false });
        }
      },
      fail: (err) => {
        console.error('API请求失败:', err);
        wx.showToast({
          title: '网络请求失败',
          icon: 'none'
        });
        this.setData({ isLoading: false });
      }
    });
  },

  /**
   * 加载问题列表
   */
  loadQuestions: function () {
    if (this.data.isLoading || !this.data.hasMore) return;
    
    // 构建请求URL
    const requestUrl = this.buildRequestUrl();
    console.log('请求URL:', requestUrl);
    
    // 发起请求
    this.requestData(requestUrl, (questions) => {
      this.setData({
        questions: [...this.data.questions, ...questions],
        page: this.data.page + 1,
        hasMore: questions.length >= this.data.limit,
        isLoading: false
      }, () => {
        // 加载完问题列表后，加载点赞数据
        this.loadDiggData();
      });
    });
  },

  /**
   * 处理API返回的数据
   */
  processApiData: function (apiData) {
    if (!Array.isArray(apiData)) {
      return [];
    }
    
    return apiData.map(item => {
      // 处理状态值，根据is_comment字段
      let statusValue = 0;
      let statusText = '未解决';
      
      if (item.is_comment !== undefined) {
        statusValue = parseInt(item.is_comment);
        // 根据状态值映射获取对应的状态文本
        switch(statusValue) {
          case 1:
            statusText = '已完成';
            break;
          case 2:
            statusText = '解决中';
            break;
          default:
            statusText = '未解决';
        }
      }
      
      return {
        id: item.id,
        avatar: '../../icons/user.png', // 默认头像
        username: item.author || '匿名用户',
        create_time: item.inputtime || '',
        is_solved: statusValue === 1,
        status_value: statusValue,
        status_text: statusText,
        title: item.title || '',
        content: item.description || '无描述内容',
        view_count: parseInt(item.hits) || 0,
        answer_count: parseInt(item.comments) || 0,
        like_count: parseInt(item.support) || 0,
        unlike_count: parseInt(item.oppose) || 0, // 添加反对数量
        keywords: item.keywords || '',
        catid: item.catid || '',
        thumb: item.thumb || ''
      };
    });
  },

  /**
   * 加载更多数据
   */
  loadMore: function () {
    this.loadQuestions();
  },

  /**
   * 切换问题分类
   */
  switchCategory: function (e) {
    const category = e.currentTarget.dataset.category;
    
    this.setData({
      currentCategory: category,
      questions: [],
      page: 1,
      hasMore: true
    });
    
    // 构建请求URL
    const requestUrl = this.buildRequestUrl({category: category});
    console.log('分类切换URL:', requestUrl);
    
    // 发起请求
    this.requestData(requestUrl, (questions) => {
      this.setData({
        questions: questions,
        page: this.data.page + 1,
        hasMore: questions.length >= this.data.limit,
        isLoading: false
      }, () => {
        // 加载完问题列表后，加载点赞数据
        this.loadDiggData();
      });
    });
  },

  /**
   * 搜索输入事件处理
   */
  onSearchInput: function (e) {
    this.searchKeyword = e.detail.value;
  },
  
  /**
   * 执行搜索
   */
  doSearch: function() {
    if (!this.searchKeyword || this.searchKeyword.trim() === '') {
      return;
    }
    
    this.resetList();
    
    // 构建搜索URL
    const searchUrl = this.buildRequestUrl({
      page: 1, 
      keyword: this.searchKeyword
    });
    
    console.log('搜索URL:', searchUrl);
    
    // 发起请求
    this.requestData(searchUrl, (questions) => {
      this.setData({
        questions: questions,
        page: 2, // 搜索后从第2页开始
        hasMore: questions.length >= this.data.limit,
        isLoading: false
      });
    });
  },

  /**
   * 创建新问题
   */
  createQuestion: function () {
    // 检查用户是否登录
    const member_uid = wx.getStorageSync('member_uid');
    const member_auth = wx.getStorageSync('member_auth');
    
    if (!member_uid || !member_auth) {
      wx.showModal({
        title: '提示',
        content: '请先登录后再发布问题',
        confirmText: '去登录',
        success: (res) => {
          if (res.confirm) {
            wx.navigateTo({
              url: '../login/login'
            });
          }
        }
      });
      return;
    }
    
    // 跳转到发布问题页面
    wx.navigateTo({
      url: '../wenda/publish'
    });
  },

  /**
   * 更新统计信息
   */
  updateStats: function(stats) {
    if (!stats) return;
    
    // 保存统计数据到全局变量以备其他地方使用
    if (!app.globalData.wendaStats) {
      app.globalData.wendaStats = {};
    }
    
    app.globalData.wendaStats = {
      all: stats.all || 0,
      wait: stats.wait || 0,  // 对应未解决(0)
      inprocess: stats.inprocess || 0,  // 对应解决中(2)
      solved: stats.solved || 0,  // 对应已完成(1)
      statusDetail: stats.status_detail || []
    };
  },

  /**
   * 点赞/踩操作
   */
  moduleDigg: function(e) {
    const id = e.currentTarget.dataset.id;
    const value = e.currentTarget.dataset.value;
    
    // 检查用户是否登录
    const member_uid = wx.getStorageSync('member_uid');
    const member_auth = wx.getStorageSync('member_auth');
    
    if (!member_uid || !member_auth) {
      wx.showModal({
        title: '提示',
        content: '请先登录后再进行此操作',
        confirmText: '去登录',
        success: (res) => {
          if (res.confirm) {
            wx.navigateTo({
              url: '../login/login'
            });
          }
        }
      });
      return;
    }
    
    // 构建请求URL
    const url = app.globalData.http_api + 
      'is_ajax=1&s=api&app=wenda&c=module&m=digg&id=' + id + '&value=' + value +
      '&appid=' + app.globalData.appid +
      '&appsecret=' + app.globalData.appsecret;
    
    wx.request({
      url: url,
      method: 'GET',
      header: {
        'content-type': 'application/json'
      },
      success: (res) => {
        if (res.data && res.data.code) {
          // 更新点赞数量
          const diggElem = `digg_${id}_${value}`;
          const selector = `#${diggElem}`;
          const query = wx.createSelectorQuery();
          query.select(selector).node().exec((nodeRes) => {
            if (nodeRes[0] && nodeRes[0].node) {
              nodeRes[0].node.textContent = res.data.data;
            }
          });
          
          wx.showToast({
            title: res.data.msg || '操作成功',
            icon: 'success'
          });
          
          // 更新本地数据
          const questions = this.data.questions;
          const index = questions.findIndex(item => item.id == id);
          if (index !== -1) {
            if (value == 1) {
              questions[index].like_count = res.data.data;
            } else {
              questions[index].unlike_count = res.data.data;
            }
            this.setData({ questions });
          }
        } else {
          wx.showToast({
            title: res.data.msg || '操作失败',
            icon: 'none'
          });
        }
      },
      fail: (err) => {
        console.error('点赞操作失败:', err);
        wx.showToast({
          title: '网络请求失败',
          icon: 'none'
        });
      }
    });
  },
  
  /**
   * 加载点赞数据
   */
  loadDiggData: function() {
    // 获取列表中的问题ID
    const questions = this.data.questions;
    if (questions.length === 0) return;
    
    questions.forEach(item => {
      // 构建请求URL获取点赞数据
      const url = app.globalData.http_api + 
        'is_ajax=1&s=zan&mid=wenda&id=' + item.id +
        '&appid=' + app.globalData.appid +
        '&appsecret=' + app.globalData.appsecret;
      
      wx.request({
        url: url,
        method: 'GET',
        header: {
          'content-type': 'application/json'
        },
        success: (res) => {
          if (res.data && res.data.code) {
            const data = res.data.data;
            // 更新本地数据
            const questions = this.data.questions;
            const index = questions.findIndex(q => q.id == item.id);
            if (index !== -1) {
              questions[index].unlike_count = data.a;
              questions[index].like_count = data.b;
              this.setData({ questions });
            }
          }
        },
        fail: (err) => {
          console.error('获取点赞数据失败:', err, item.id);
        }
      });
    });
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    // 检查是否需要刷新列表（从发布页面返回）
    if (app.globalData.needRefreshQuestionList) {
      console.log('检测到需要刷新问题列表');
      app.globalData.needRefreshQuestionList = false;
      this.resetList();
      this.loadQuestions();
    }
  },
}); 