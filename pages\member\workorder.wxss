/* 页面容器 */
.container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 40rpx;
}

/* 搜索栏样式 */
.search-section {
  background: white;
  padding: 14rpx 24rpx;
  margin-bottom: 12rpx;
  box-shadow: 0 1rpx 6rpx rgba(0, 0, 0, 0.03);
}

.search-box {
  display: flex;
  align-items: center;
}

.search-input-wrap {
  display: flex;
  align-items: center;
  background: #f5f5f5;
  border-radius: 24rpx;
  padding: 10rpx 20rpx;
  flex: 1;
}

.search-icon {
  width: 28rpx;
  height: 28rpx;
  margin-right: 12rpx;
  opacity: 0.5;
}

.search-input {
  flex: 1;
  font-size: 26rpx;
  color: #333;
}

.clear-icon {
  width: 32rpx;
  height: 32rpx;
  opacity: 0.5;
}

.search-btn {
  margin-left: 16rpx;
  background-color: #1890ff;
  color: white;
  padding: 8rpx 24rpx;
  border-radius: 24rpx;
  font-size: 26rpx;
}

/* 工单列表样式 */
.content-list {
  padding: 0 16rpx;
  height: calc(100vh - 160rpx);
}

.workorder-card {
  background: white;
  border-radius: 12rpx;
  margin-bottom: 14rpx;
  padding: 24rpx 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
  position: relative;
  overflow: hidden;
  border-left: 6rpx solid #1890ff;
  transition: all 0.3s ease;
}

.workorder-card:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.workorder-card::after {
  content: none;
}

.card-header {
  margin-bottom: 16rpx;
}

.order-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
  gap: 14rpx;
}

.order-number {
  font-size: 26rpx;
  color: #666;
  background: #f8f8f8;
  padding: 8rpx 12rpx 8rpx 16rpx;
  border-radius: 16rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  min-width: 146rpx;
  text-align: left;
  flex: 1.2;
}

.status-label {
  opacity: 0.7;
  font-weight: normal;
  margin-right: 8rpx;
}

.status-public {
  background-color: #f6ffed;
  color: #52c41a;
  border-left: 6rpx solid #52c41a;
  font-weight: 600;
  padding-left: 8rpx;
}

.status-private {
  background-color: #fff1f0;
  color: #ff4d4f;
  border-left: 6rpx solid #ff4d4f;
  font-weight: 600;
  padding-left: 8rpx;
}

.status-pending {
  background-color: #fff7e6;
  color: #fa8c16;
  border-left: 6rpx solid #fa8c16;
  padding-left: 8rpx;
}

.status-processing {
  background-color: #e6f7ff;
  color: #1890ff;
  border-left: 6rpx solid #1890ff;
  padding-left: 8rpx;
}

.status-completed {
  background-color: #f6ffed;
  color: #52c41a;
  border-left: 6rpx solid #52c41a;
  padding-left: 8rpx;
}

.status-replied {
  background-color: #f9f0ff;
  color: #722ed1;
  border-left: 6rpx solid #722ed1;
  padding-left: 8rpx;
}

.status-waiting {
  background-color: #fff1f0;
  color: #ff4d4f;
  border-left: 6rpx solid #ff4d4f;
  padding-left: 8rpx;
}

.order-status {
  padding: 8rpx 12rpx 8rpx 16rpx;
  border-radius: 16rpx;
  font-size: 24rpx;
  font-weight: 500;
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
  text-align: left;
  min-width: 126rpx;
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.order-title {
  font-size: 32rpx;
  color: #333;
  font-weight: 500;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
}

.highlight-title {
  font-size: 30rpx;
  color: #111;
  font-weight: 600;
  padding: 8rpx 0;
  position: relative;
  display: block;
  margin-top: 4rpx;
}

.highlight-title::before {
  content: '主题：';
  color: #666;
  font-weight: normal;
  margin-right: 6rpx;
  font-size: 26rpx;
  opacity: 0.8;
}

.card-body {
  padding-top: 12rpx;
  border-top: 1rpx solid #f5f5f5;
}

.info-item {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-label {
  font-size: 28rpx;
  color: #666;
}

.info-value {
  font-size: 28rpx;
  color: #333;
}

.time-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #f9f9f9;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  margin-top: 4rpx;
}

.time-icon {
  width: 24rpx;
  height: 24rpx;
  margin-right: 8rpx;
  opacity: 0.6;
}

.time-text {
  font-size: 24rpx;
  color: #666;
  flex: 1;
}

.arrow-icon {
  width: 24rpx;
  height: 24rpx;
  opacity: 0.3;
}

.arrow-icon image {
  width: 100%;
  height: 100%;
}

/* 搜索结果提示 */
.search-result-tip {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 30rpx;
  background: #f0f7ff;
  border-radius: 8rpx;
  margin-bottom: 20rpx;
}

.search-result-tip text {
  font-size: 26rpx;
  color: #1890ff;
}

.clear-search {
  font-size: 26rpx;
  color: #1890ff;
  padding: 6rpx 20rpx;
  background: white;
  border-radius: 20rpx;
}

/* 空状态样式 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.empty-icon {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
  opacity: 0.5;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
  margin-bottom: 16rpx;
}

.empty-tip {
  font-size: 26rpx;
  color: #999;
}

.empty-action {
  margin-top: 30rpx;
  background: #f0f7ff;
  color: #1890ff;
  padding: 12rpx 30rpx;
  border-radius: 30rpx;
  font-size: 28rpx;
}

/* 加载状态样式 */
.loading-state {
  padding: 30rpx 0;
  text-align: center;
}

.loading-content {
  display: flex;
  align-items: center;
  justify-content: center;
}

.loading-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 10rpx;
}

.loading-text {
  font-size: 26rpx;
  color: #999;
}

.no-more {
  padding: 20rpx 0;
  text-align: center;
}

.no-more-text {
  font-size: 26rpx;
  color: #999;
}

/* 创建按钮样式 */
.create-button {
  position: fixed;
  bottom: 30rpx;
  left: 50%;
  transform: translateX(-50%);
  z-index: 100;
}

.create-button-inner {
  display: flex;
  align-items: center;
  justify-content: center;
  background: #1890ff;
  padding: 16rpx 34rpx;
  border-radius: 32rpx;
  box-shadow: 0 4rpx 12rpx rgba(24, 144, 255, 0.3);
  transition: all 0.3s;
}

.create-button-inner:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 8rpx rgba(24, 144, 255, 0.2);
}

.create-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 8rpx;
}

.create-text {
  font-size: 30rpx;
  color: white;
  font-weight: 500;
} 