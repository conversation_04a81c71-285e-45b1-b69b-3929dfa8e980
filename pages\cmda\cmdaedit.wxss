/* 编辑档案页面样式 */

.container {
  background-color: #f5f6fa;
  min-height: 100vh;
  width: 100%;
  padding-bottom: 120rpx;
  position: relative;
}

/* 顶部状态栏 */
.status-bar {
  width: 100%;
  height: 88rpx;
  background-color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  position: sticky;
  top: 0;
  z-index: 100;
}

.status-indicator {
  font-size: 32rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.status-dot {
  width: 12rpx;
  height: 12rpx;
  border-radius: 50%;
  background-color: currentColor;
}

.status-indicator.loading {
  color: #666;
}

.status-indicator.success {
  color: #07c160;
}

.status-indicator.error {
  color: #e64340;
}

/* 加载中样式 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.loading-spinner {
  width: 80rpx;
  height: 80rpx;
  border: 6rpx solid rgba(0, 0, 0, 0.1);
  border-left-color: #07c160;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 30rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #999;
}

/* 表单区域样式 */
.edit-form {
  height: calc(100vh - 88rpx - 120rpx);
  padding: 20rpx;
}

.form-section {
  margin-bottom: 30rpx;
  background-color: #fff;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.section-header {
  padding: 24rpx;
  border-bottom: 1px solid #f0f0f0;
}

.section-title-container {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.section-icon {
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8rpx;
}

.section-icon .iconfont {
  font-size: 24rpx;
  color: #fff;
}

.basic-icon {
  background-color: #4cd964;
}

.photo-icon {
  background-color: #ff9500;
}

.house-icon {
  background-color: #5856d6;
}

.id-icon {
  background-color: #ff2d55;
}

.land-icon {
  background-color: #34aadc;
}

.section-title {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
}

.form-group {
  padding: 0 24rpx;
}

.form-item {
  display: flex;
  align-items: center;
  padding: 24rpx 0;
  border-bottom: 1px solid #f5f5f5;
}

.form-item:last-child {
  border-bottom: none;
}

.form-label {
  flex: 0 0 180rpx;
  font-size: 28rpx;
  color: #666;
  position: relative;
}

.form-label.required::before {
  content: '*';
  color: #ff2d55;
  margin-right: 4rpx;
}

.form-input {
  flex: 1;
  font-size: 28rpx;
  color: #333;
  height: 60rpx;
  padding: 0 20rpx;
  background-color: #f8f8f8;
  border-radius: 8rpx;
}

.form-value {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}

/* 复选框样式 */
.checkbox-group-container {
  flex: 1;
}

.selected-tips {
  margin-bottom: 16rpx;
}

.selected-title {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 8rpx;
}

.selected-content {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
}

.selected-tag {
  padding: 4rpx 16rpx;
  background-color: #f0f0f0;
  border-radius: 24rpx;
  font-size: 24rpx;
  color: #666;
}

.checkbox-group {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}

.checkbox-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 12rpx 20rpx;
  background-color: #f8f8f8;
  border-radius: 8rpx;
  transition: all 0.3s;
}

.checkbox-item.checkbox-selected {
  background-color: #e6f7ff;
  color: #1890ff;
}

.checkbox-icon {
  width: 32rpx;
  height: 32rpx;
  border: 2rpx solid #ddd;
  border-radius: 4rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s;
}

.checkbox-selected .checkbox-icon {
  background-color: #1890ff;
  border-color: #1890ff;
}

.checkbox-selected .checked {
  width: 16rpx;
  height: 16rpx;
  background-color: #fff;
  border-radius: 2rpx;
}

/* 图片上传区域 */
.photo-sections {
  display: flex;
  flex-direction: column;
  gap: 30rpx;
}



/* 底部操作栏 */
.action-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 120rpx;
  background-color: #fff;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 24rpx;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.action-button {
  flex: 1;
  height: 80rpx;
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  font-weight: 500;
  margin: 0 12rpx;
  transition: all 0.3s;
}

.cancel-button {
  background-color: #f5f5f5;
  color: #666;
}

.save-button {
  background-color: #07c160;
  color: #fff;
}

.button-hover {
  opacity: 0.8;
  transform: scale(0.98);
}

/* 图标字体 */
@font-face {
  font-family: 'iconfont';
  src: url('//at.alicdn.com/t/font_xxxxx.woff2') format('woff2');
}

.iconfont {
  font-family: 'iconfont';
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
} 