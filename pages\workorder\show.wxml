<!-- 工单详情页 -->
<view class="page-container">
  <!-- 顶部信息区域 -->
  <view class="header">
    <view class="header__title-section">
      <view class="header__author-info">
        <image class="header__author-avatar" src="{{content.avatar ? content.avatar : '../../images/avatar.png'}}" mode="aspectFill"></image>
        <view class="header__text-content">
          <text class="header__main-title">{{content.title}}</text>
          <view class="header__meta">
            <text class="header__author">{{content.author}}</text>
            <text class="header__time">于{{content.inputtime}}发表</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 工单状态卡片 -->
    <view class="status-panel">
      <view class="status-panel__grid">
        <block wx:if="{{content.fabuleixing != '发布信息'}}">
        <!-- 所属区域 - 调整样式，增加视觉重要性 -->
        <view class="status-item status-item--area">
          <view class="status-item__icon">
            <image src="../../icons/sscz.png" class="status-item__icon-img" mode="aspectFit"></image>
          </view>
          <view class="status-item__content">
            <text class="status-item__label">所属区域：</text>
            <text class="status-item__value status-item__value--highlight">{{content.sscz || '未指定'}}</text>
          </view>
        </view>
          <view class="status-item">
            <view class="status-item__icon">
              <image src="../../icons/gk.png" class="status-item__icon-img" mode="aspectFit"></image>
            </view>
            <text class="status-item__label">状态</text>
            <text class="status-item__value">{{content.gongkai}}</text>
          </view>
        </block>
        <view class="status-item">
          <view class="status-item__icon">
            <image src="../../icons/lx.png" class="status-item__icon-img" mode="aspectFit"></image>
          </view>
          <text class="status-item__label">类型</text>
          <text class="status-item__value">{{content.fabuleixing == '发布信息' ? content.xinxileixing : content.leixing}}</text>
        </view>
        <view class="status-item">
          <view class="status-item__icon">
            <image src="../../icons/jd.png" class="status-item__icon-img" mode="aspectFit"></image>
          </view>
          <text class="status-item__label">进度</text>
          <text class="status-item__value">{{content.jindu}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 主要内容区域 -->
  <view class="main-content">
    <!-- 详细内容卡片 -->
    <view class="content-card">
      <view class="section-header">
        <view class="section-header__line"></view>
        <text class="section-header__title">详细内容</text>
      </view>
      <view class="content-card__body">
        <import src="../../wxParse/wxParse.wxml"/>
        <template is="wxParse" data="{{wxParseData:data.nodes}}"/>
      </view>
    </view>

    <!-- 图片展示区域 -->
    <block wx:if="{{content.tupianxinxi && content.tupianxinxi.length > 0}}">
      <view class="image-gallery-card">
        <view class="section-header">
          <view class="section-header__line"></view>
          <text class="section-header__title">{{content.fabuleixing == '发布信息' ? '详情图片' : '图片佐证'}}</text>
        </view>
        <view class="image-gallery__grid">
          <view 
            class="image-gallery__item"
            wx:for="{{content.tupianxinxi}}" 
            wx:for-item="image"
            wx:key="index"
            bindtap="previewEvidenceImage"
            bindlongpress="handleEvidenceImageLongPress"
            data-urls="{{content.tupianxinxi}}"
            data-current="{{image}}"
            catchtouchmove="preventTouchMove"
          >
            <image 
              src="{{image.file}}" 
              mode="aspectFill"
              class="image-gallery__img"
              lazy-load
              show-menu-by-longpress
            />
          </view>
          <view class="image-count" wx:if="{{content.tupianxinxi.length > 0}}">
            <image class="image-count__icon" src="../../icons/star.png" mode="aspectFit" />
            <text>共{{content.tupianxinxi.length}}张</text>
          </view>
        </view>
      </view>
    </block>

    <!-- 评论区域 -->
    <view class="comments">
      <view class="section-header">
        <view class="section-header__line"></view>
        <text class="section-header__title">{{content.fabuleixing == '发布信息' ? '关注情况' : '处理意见'}}</text>
        <text class="section-header__count">（{{content.comments}}条）</text>
      </view>
      
      <view class="comments__list">
        <block wx:if="{{commentList.length === 0}}">
          <view class="comments__empty">
            <text>暂无评论，来发表第一条吧</text>
          </view>
        </block>
        <block wx:else>
          <view 
            class="comment-item"
            wx:for="{{commentList}}" 
            wx:key="uniqueKey"
          >
            <view class="comment-item__header">
              <image 
                class="comment-item__avatar" 
                src="{{item.avatar || '/images/default-avatar.png'}}" 
                mode="aspectFill" 
                lazy-load
              />
              <view class="comment-item__meta">
                <view class="comment-item__author">
                  {{item.author || '匿名用户'}}
                  <text class="comment-item__tag" wx:if="{{item.uid == content.uid}}">发布者</text>
                </view>
                <text class="comment-item__time">{{item.inputtime}}</text>
              </view>
            </view>
            <view class="comment-item__content">
              <import src="../../wxParse/wxParse.wxml"/>
              <template is="wxParse" data="{{wxParseData:item.parsedContent.nodes}}"/>
              <view class="image-count" wx:if="{{item.images && item.images.length > 0}}">
                <image class="image-count__icon" src="../../icons/star.png" mode="aspectFit" />
                <text>{{item.images.length}}张</text>
              </view>
            </view>
          </view>
        </block>
      </view>

      <!-- 加载状态 -->
      <view class="loading-status">
        <view class="loading-status__more" wx:if="{{isLoading}}">
          <image src="../../icons/waiting.gif" class="loading-status__icon" />
          <text>加载中...</text>
        </view>
        <view class="loading-status__end" wx:if="{{!hasMore && !isLoading && commentList.length > 0}}">
          <text>暂无更多信息</text>
        </view>
      </view>
    </view>
    
    <view class="bottom-spacer"></view>
  </view>

  <!-- 底部操作栏 -->
  <view class="footer">
    <!-- 评论输入区 -->
    <block wx:if="{{content.jindu != '已完毕'}}">
      <view class="footer__input-wrapper">
        <view class="footer__input-box" bindtap="showFullCommentBar">
          <text>发表评论...</text>
        </view>
      </view>
    </block>
    <block wx:else>
      <view class="footer__input-wrapper">
        <view class="footer__completed-notice">
          <text>工单已完成，不提供回复</text>
        </view>
      </view>
    </block>
    
    <!-- 操作按钮组 -->
    <view class="footer__actions">
      <block wx:if="{{content.jindu != '已完毕' && member.uid == content.uid}}">
        <view class="action-btn">
          <button class="action-btn__complete" bindtap="completeOrder">完成工单</button>
        </view>
      </block>
      
      <view class="action-btn">
        <button class="action-btn__normal" bindtap="up">
          <image src="{{upsImg}}" class="action-btn__icon"></image>
          <text>{{supports}}</text>
        </button>
      </view>
      
      <view class="action-btn">
        <button class="action-btn__normal" bindtap="collect">
          <image src="{{collectImg}}" class="action-btn__icon"></image>
        </button>
      </view>
    </view>
  </view>

  <!-- 评论弹出层 -->
  <view class="comment-modal {{isCommentBarShow ? 'comment-modal--show' : ''}}" wx:if="{{content.jindu != '已完毕'}}">
    <view class="comment-modal__header">
      <text>发表评论</text>
      <view class="comment-modal__close" bindtap="hideFullCommentBar">
        <image src="../../icons/close.png" class="comment-modal__close-icon"></image>
      </view>
    </view>
    
    <view class="comment-modal__body">
      <textarea 
        class="comment-modal__textarea"
        placeholder="请输入评论内容..." 
        bindinput="getText"
        value="{{commentText}}"
        fixed
        maxlength="2000"
        show-confirm-bar="{{false}}"
      />
      
      <!-- 图片预览 -->
      <block wx:if="{{tempImagePaths.length > 0}}">
        <view class="image-preview">
          <view 
            class="image-preview__item" 
            wx:for="{{tempImagePaths}}" 
            wx:key="index"
          >
            <image src="{{item}}" class="image-preview__img" mode="aspectFill"></image>
            <view 
              class="image-preview__delete" 
              catchtap="deleteImage" 
              data-index="{{index}}"
            >×</view>
          </view>
        </view>
      </block>
    </view>

    <view class="comment-modal__footer">
      <view class="comment-modal__tools">
        <block wx:if="{{member.is_admin == '1' || member.uid == content.uid}}">
          <button class="upload-btn" bindtap="chooseImage">
            <image src="../../icons/camera.png" class="upload-btn__icon"></image>
            <block wx:if="{{tempImagePaths.length > 0}}">
              <text>({{tempImagePaths.length}})</text>
            </block>
          </button>
        </block>
      </view>
      <view class="comment-modal__actions">
        <button 
          class="comment-modal__btn comment-modal__btn--cancel" 
          bindtap="hideFullCommentBar"
        >取消</button>
        <button 
          class="comment-modal__btn comment-modal__btn--submit {{commentText || tempImagePaths.length > 0 ? 'is-active' : ''}}" 
          bindtap="saveComment"
        >发表</button>
      </view>
    </view>
  </view>
</view>




