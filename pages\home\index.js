var app = getApp();

Page({
  data: {
    searchKeyword: '',  // 搜索关键词
    activeTag: '全部',   // 当前选中的标签
    isSearching: false, // 是否正在搜索
    searchResults: [],  // 搜索结果
    searchPage: 1,      // 搜索页码
    searchHasMore: true, // 是否还有更多搜索结果
    searchLoading: false, // 搜索加载状态
    totalSearchCount: 0, // 搜索结果总数
    recentUpdates: [],  // 最近更新数据
    recentLoading: false, // 最近更新加载状态
    // 今日审批事项数据
    approvalList: [
      {
        id: 1,
        name: '张小明',
        type: '低保申请',
        color: '#666666',
        urgent: true,
        time: '2小时前'
      },
      {
        id: 2,
        name: '东方红农业公司',
        type: '土地流转申请',
        color: '#ff4757',
        urgent: false,
        time: '4小时前'
      },
      {
        id: 3,
        name: '李大爷',
        type: '医疗救助申请',
        color: '#2ed573',
        urgent: true,
        time: '6小时前'
      },
      {
        id: 4,
        name: '天地农业技术公司',
        type: '合作社注册',
        color: '#1e90ff',
        urgent: false,
        time: '8小时前'
      },
      {
        id: 5,
        name: '刘小山',
        type: '建房申请',
        color: '#ffa502',
        urgent: false,
        time: '10小时前'
      }
    ],
    // 新闻资讯数据
    newsList: [
      {
        id: 1,
        title: '新村务管理系统正式上线，助力乡村振兴',
        author: '村委会',
        time: '2024-01-15',
        source: '村务公开平台',
        image: '/images/avatar.png'
      },
      {
        id: 2,
        title: '农业现代化项目启动，预计带动就业200人',
        author: '农业合作社',
        time: '2024-01-14',
        source: '农业发展中心',
        image: '/images/avatar.png'
      },
      {
        id: 3,
        title: '村民医疗保险覆盖率达到98%，惠民政策见成效',
        author: '卫生院',
        time: '2024-01-13',
        source: '卫生健康委',
        image: '/images/avatar.png'
      },
      {
        id: 4,
        title: '基础设施建设项目完工，村容村貌焕然一新',
        author: '建设办',
        time: '2024-01-12',
        source: '建设发展局',
        image: '/images/avatar.png'
      },
      {
        id: 5,
        title: '教育扶贫政策落地，贫困学生全部获得资助',
        author: '教育局',
        time: '2024-01-11',
        source: '教育发展中心',
        image: '/images/avatar.png'
      },
      {
        id: 6,
        title: '环保治理成效显著，生态环境持续改善',
        author: '环保局',
        time: '2024-01-10',
        source: '环境保护局',
        image: '/images/avatar.png'
      }
    ]
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function () {
    // 检查登录状态
    const member = wx.getStorageSync('member');
    const member_auth = wx.getStorageSync('member_auth');
    const member_uid = wx.getStorageSync('member_uid');

    // 如果未登录，直接跳转到登录页面
    if (!member || !member_auth || !member_uid || !member.id) {
      wx.reLaunch({
        url: '/pages/login/login'
      });
      return;
    }

    // 加载最近更新数据
    this.loadRecentUpdates();
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function() {
    // 设置当前选中的tabbar项为首页
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().setData({
        selected: 0
      });
    }

    // 刷新最近更新数据
    this.loadRecentUpdates();
  },

  /**
   * 下拉刷新
   */
  onPullDownRefresh: function () {
    // 刷新最近更新数据
    this.loadRecentUpdates();

    // 延迟停止下拉刷新，等待数据加载完成
    setTimeout(() => {
      wx.stopPullDownRefresh();
      wx.showToast({
        title: '刷新成功',
        icon: 'success',
        duration: 1000
      });
    }, 1000);
  },

  /**
   * 搜索输入事件
   */
  onSearchInput: function(e) {
    const keyword = e.detail.value;
    this.setData({
      searchKeyword: keyword
    });
  },

  /**
   * 搜索确认事件
   */
  onSearchConfirm: function() {
    const keyword = this.data.searchKeyword.trim();

    if (keyword === '') {
      // 如果关键词为空且当前在搜索状态，恢复到列表状态
      if (this.data.isSearching) {
        this.clearSearch();
      }
      return;
    }

    // 检查登录状态
    const member_uid = wx.getStorageSync('member_uid');
    const member_auth = wx.getStorageSync('member_auth');

    if (!member_uid || !member_auth) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }

    // 执行搜索
    this.performSearch(keyword);
  },

  /**
   * 执行搜索
   */
  performSearch: function(keyword) {
    // 设置搜索状态，重置分页
    this.setData({
      isSearching: true,
      searchResults: [],
      searchPage: 1,
      searchHasMore: true,
      searchLoading: true,
      totalSearchCount: 0
    });

    // 显示加载提示
    wx.showLoading({
      title: '搜索中...'
    });

    // 调用API搜索
    this.searchFromAPI(keyword);
  },

  /**
   * 调用API搜索
   */
  searchFromAPI: function(keyword) {
    const app = getApp();
    const member_uid = wx.getStorageSync('member_uid');
    const member_auth = wx.getStorageSync('member_auth');

    if (!member_uid || !member_auth) {
      wx.hideLoading();
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }

    // 构建API请求URL
    let requestUrl = app.globalData.http_api +
      's=cmda&c=search&api_call_function=module_list' +
      '&appid=' + app.globalData.appid +
      '&appsecret=' + app.globalData.appsecret +
      '&more=1' +
      '&page=' + this.data.searchPage +
      '&pagesize=20' +
      '&api_auth_uid=' + member_uid +
      '&api_auth_code=' + member_auth +
      '&keyword=' + encodeURIComponent(keyword);

    console.log('首页搜索请求URL:', requestUrl);

    wx.request({
      url: requestUrl,
      method: 'GET',
      header: {
        'content-type': 'application/json'
      },
      success: (res) => {
        wx.hideLoading();
        console.log('首页搜索结果:', res.data);

        if (res.data.code == 1 && res.data.data && Array.isArray(res.data.data)) {
          const newResults = res.data.data.map(item => ({
            id: item.id,
            name: item.title || item.name || '未知',
            type: item.hujishuxing || '村民档案',
            color: this.getRandomColor(),
            urgent: Math.random() > 0.7, // 随机设置紧急状态
            time: '刚刚'
          }));

          // 判断是否还有更多数据
          const hasMore = newResults.length >= 20;

          // 如果是第一页，替换结果；否则追加结果
          const searchResults = this.data.searchPage === 1 ?
            newResults :
            [...this.data.searchResults, ...newResults];

          this.setData({
            searchResults: searchResults,
            searchHasMore: hasMore,
            searchLoading: false
          });

          // 如果是第一页，获取总数并显示
          if (this.data.searchPage === 1) {
            this.getTotalSearchCount(keyword);
          } else {
            wx.showToast({
              title: `加载了${newResults.length}条数据`,
              icon: 'success',
              duration: 1000
            });
          }
        } else {
          this.setData({
            searchResults: this.data.searchPage === 1 ? [] : this.data.searchResults,
            searchHasMore: false,
            searchLoading: false
          });

          if (this.data.searchPage === 1) {
            this.setData({
              totalSearchCount: 0
            });
            wx.showToast({
              title: '未找到相关结果',
              icon: 'none'
            });
          } else {
            wx.showToast({
              title: '没有更多数据了',
              icon: 'none'
            });
          }
        }
      },
      fail: (err) => {
        wx.hideLoading();
        console.error('搜索请求失败:', err);
        this.setData({
          searchResults: [],
          searchLoading: false
        });
        wx.showToast({
          title: '搜索失败，请重试',
          icon: 'none'
        });
      }
    });
  },



  /**
   * 获取搜索结果总数
   */
  getTotalSearchCount: function(keyword) {
    const app = getApp();
    const member_uid = wx.getStorageSync('member_uid');
    const member_auth = wx.getStorageSync('member_auth');

    // 构建API请求URL - 使用大的pagesize获取总数
    let requestUrl = app.globalData.http_api +
      's=cmda&c=search&api_call_function=module_list' +
      '&appid=' + app.globalData.appid +
      '&appsecret=' + app.globalData.appsecret +
      '&more=1' +
      '&page=1' +
      '&pagesize=9999' +
      '&api_auth_uid=' + member_uid +
      '&api_auth_code=' + member_auth +
      '&keyword=' + encodeURIComponent(keyword);

    console.log('获取搜索总数URL:', requestUrl);

    wx.request({
      url: requestUrl,
      method: 'GET',
      header: {
        'content-type': 'application/json'
      },
      success: (res) => {
        console.log('搜索总数结果:', res.data);

        if (res.data.code == 1 && res.data.data && Array.isArray(res.data.data)) {
          const totalCount = res.data.data.length;
          this.setData({
            totalSearchCount: totalCount
          });

          // 根据搜索类型显示不同的提示
          const isTagSearch = this.data.activeTag !== '全部' && this.data.searchKeyword === this.data.activeTag;
          const message = isTagSearch ?
            `找到${totalCount}条"${this.data.activeTag}"相关结果` :
            `找到${totalCount}条结果`;

          wx.showToast({
            title: message,
            icon: 'success',
            duration: 1500
          });
        } else {
          this.setData({
            totalSearchCount: this.data.searchResults.length
          });

          wx.showToast({
            title: `找到${this.data.searchResults.length}条结果`,
            icon: 'success',
            duration: 1500
          });
        }
      },
      fail: (err) => {
        console.error('获取搜索总数失败:', err);
        // 失败时使用当前结果数量
        this.setData({
          totalSearchCount: this.data.searchResults.length
        });

        wx.showToast({
          title: `找到${this.data.searchResults.length}条结果`,
          icon: 'success',
          duration: 1500
        });
      }
    });
  },

  /**
   * 加载更多搜索结果
   */
  loadMoreSearchResults: function() {
    if (this.data.searchLoading || !this.data.searchHasMore) {
      return;
    }

    this.setData({
      searchLoading: true,
      searchPage: this.data.searchPage + 1
    });

    // 显示加载提示
    wx.showLoading({
      title: '加载中...'
    });

    // 调用API搜索下一页
    this.searchFromAPI(this.data.searchKeyword);
  },

  /**
   * 加载最近更新数据
   */
  loadRecentUpdates: function() {
    const app = getApp();
    const member_uid = wx.getStorageSync('member_uid');
    const member_auth = wx.getStorageSync('member_auth');

    if (!member_uid || !member_auth) {
      console.log('用户未登录，无法加载最近更新数据');
      return;
    }

    this.setData({
      recentLoading: true
    });

    // 构建API请求URL - 获取最近5条数据
    let requestUrl = app.globalData.http_api +
      's=cmda&c=search&api_call_function=module_list' +
      '&appid=' + app.globalData.appid +
      '&appsecret=' + app.globalData.appsecret +
      '&more=1' +
      '&page=1' +
      '&pagesize=5' +
      '&api_auth_uid=' + member_uid +
      '&api_auth_code=' + member_auth;

    console.log('加载最近更新数据URL:', requestUrl);

    wx.request({
      url: requestUrl,
      method: 'GET',
      header: {
        'content-type': 'application/json'
      },
      success: (res) => {
        console.log('最近更新数据结果:', res.data);

        if (res.data.code == 1 && res.data.data && Array.isArray(res.data.data)) {
          const recentUpdates = res.data.data.map(item => ({
            id: item.id,
            name: item.title || item.name || '未知',
            type: item.hujishuxing || '村民档案',
            color: this.getRandomColor(),
            urgent: false, // 最近更新不设置紧急状态
            time: this.formatTime(item.create_time || item.update_time),
            address: item.hujidizhi || '',
            idCard: item.sfzhm || ''
          }));

          this.setData({
            recentUpdates: recentUpdates,
            recentLoading: false
          });
        } else {
          this.setData({
            recentUpdates: [],
            recentLoading: false
          });
        }
      },
      fail: (err) => {
        console.error('加载最近更新数据失败:', err);
        this.setData({
          recentUpdates: [],
          recentLoading: false
        });
      }
    });
  },

  /**
   * 格式化时间
   */
  formatTime: function(timestamp) {
    if (!timestamp) return '刚刚';

    const now = new Date();
    const time = new Date(timestamp * 1000); // 假设是Unix时间戳
    const diff = now - time;

    const minutes = Math.floor(diff / (1000 * 60));
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));

    if (minutes < 1) return '刚刚';
    if (minutes < 60) return `${minutes}分钟前`;
    if (hours < 24) return `${hours}小时前`;
    if (days < 7) return `${days}天前`;

    // 超过7天显示具体日期
    return `${time.getMonth() + 1}月${time.getDate()}日`;
  },

  /**
   * 获取随机颜色
   */
  getRandomColor: function() {
    const colors = ['#666666', '#ff4757', '#2ed573', '#1e90ff', '#ffa502'];
    return colors[Math.floor(Math.random() * colors.length)];
  },

  /**
   * 清除搜索
   */
  clearSearch: function() {
    this.setData({
      searchKeyword: '',
      isSearching: false,
      searchResults: [],
      searchPage: 1,
      searchHasMore: true,
      searchLoading: false,
      totalSearchCount: 0,
      activeTag: '全部'
    });

    wx.showToast({
      title: '已清除搜索',
      icon: 'success',
      duration: 1000
    });
  },

  /**
   * 标签点击事件
   */
  onTagTap: function(e) {
    const tag = e.currentTarget.dataset.tag;

    this.setData({
      activeTag: tag
    });

    // 根据标签类型进行搜索
    if (tag === '全部') {
      // 如果点击"全部"，清除搜索状态，显示默认审批事项
      if (this.data.isSearching) {
        this.clearSearch();
      } else {
        wx.showToast({
          title: '已显示全部数据',
          icon: 'success',
          duration: 1000
        });
      }
    } else {
      // 其他标签直接进行搜索
      this.performTagSearch(tag);
    }
  },

  /**
   * 执行标签搜索
   */
  performTagSearch: function(tag) {
    // 设置搜索关键词为标签名
    this.setData({
      searchKeyword: tag,
      isSearching: true,
      searchResults: [],
      searchPage: 1,
      searchHasMore: true,
      searchLoading: true,
      totalSearchCount: 0
    });

    // 显示加载提示
    wx.showLoading({
      title: '搜索中...'
    });

    // 调用API搜索
    this.searchFromAPI(tag);
  },



  /**
   * 审批事项点击事件
   */
  onApprovalTap: function(e) {
    const item = e.currentTarget.dataset.item;

    // 如果有ID（搜索结果或最近更新），跳转到详情页面
    if (item.id) {
      wx.navigateTo({
        url: `/pages/cmda/show?id=${item.id}`
      });
      return;
    }

    // 否则显示模态框（静态数据）
    wx.showModal({
      title: '详情',
      content: `姓名：${item.name}\n类型：${item.type}\n时间：${item.time}`,
      confirmText: '查看详情',
      cancelText: '取消',
      success: function(res) {
        if (res.confirm) {
          wx.showToast({
            title: '功能开发中',
            icon: 'none'
          });
        }
      }
    });
  },



  /**
   * 功能卡片点击事件
   */
  onCardTap: function(e) {
    const type = e.currentTarget.dataset.type;
    let title = '';
    switch(type) {
      case 'labor':
        title = '劳务派遣';
        break;
      case 'consult':
        title = '管理咨询';
        break;
    }
    wx.showToast({
      title: title + '功能开发中',
      icon: 'none'
    });
  },

  /**
   * 新闻点击事件
   */
  onNewsTap: function(e) {
    const item = e.currentTarget.dataset.item;
    wx.showModal({
      title: item.title,
      content: `发布者：${item.author}\n发布时间：${item.time}\n来源：${item.source}`,
      showCancel: false,
      confirmText: '确定'
    });
  }




})