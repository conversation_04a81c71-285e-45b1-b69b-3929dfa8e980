var app = getApp();
var http_url = app.globalData.http_api;

Page({

  data: {
    // 搜索相关数据
    searchKeyword: '',  // 搜索关键词
    showSearchHistory: false,  // 是否显示搜索历史
    searchHistory: [],  // 搜索历史记录
    // 轮播图数据数组
    sdData: [],
    // 发布数据数组
    fabuData: [],
    // 轮播图图片数组
    banners: [],
    // 功能图标配置数组
    icons: [
      {
        name: '提交工单',  // 图标名称
        icon: '/images/submit.png',  // 图标路径
        url: '/pages/ticket/submit'  // 跳转路径
      },
      {
        name: '村民档案',
        icon: '/images/search.png',
        url: '/pages/cmda/list'
      },
      {
        name: '常见问题',
        icon: '/images/faq.png',
        url: '/pages/faq/index'
      },
      {
        name: '我的工单',
        icon: '/images/my.png',
        url: '/pages/member/workorder'
      }
    ],
    // 工单统计相关数据
    totalTickets: 0,  // 工单总数
    pendingTickets: 0,  // 待处理工单数
    processingTickets: 0,  // 处理中工单数
    completedTickets: 0,  // 已完成工单数
    // 最新更新相关数据
    updateFilter: 'all',  // 更新筛选器：all, tickets, notices
    allUpdates: [],  // 所有更新数据
    filteredUpdates: [],  // 筛选后的更新数据
    updatePage: 1,  // 更新列表当前页码
    updateHasMore: false,  // 是否还有更多更新数据
    updateIsLoading: false,  // 是否正在加载更新数据
    // 用户相关数据
    isLoggedIn: false,  // 用户是否已登录
    isAdmin: false,  // 用户是否是管理员
    currentUserId: null  // 当前用户ID
  },

  /**
   * 生命周期函数--监听页面加载
   * 页面初始化时调用，首先检查登录状态
   */
  onLoad: function (options) {
    try {
      var self = this;

      // 首先检查登录状态
      const member = wx.getStorageSync('member');
      const member_auth = wx.getStorageSync('member_auth');
      const member_uid = wx.getStorageSync('member_uid');

      // 如果未登录，直接跳转到登录页面
      if (!member || !member_auth || !member_uid || !member.id) {
        wx.reLaunch({
          url: '/pages/login/login'
        });
        return;
      }

      // 用户已登录，继续加载页面数据
      // 显示加载提示
      wx.showLoading({
        title: '加载中...',
        mask: true
      });

      // 初始化所有数据为空数组或默认值
      self.setData({
        sdData: [],
        fabuData: [],
        banners: [],
        allUpdates: [],
        filteredUpdates: [],
        totalTickets: 0,
        pendingTickets: 0,
        processingTickets: 0,
        completedTickets: 0
      });

      // 加载搜索历史
      self.loadSearchHistory();

      // 设置用户状态
      const userState = {
        isLoggedIn: true,
        isAdmin: member.is_admin && parseInt(member.is_admin) > 0,
        currentUserId: member.id
      };

      // 一次性设置用户状态
      self.setData(userState);

      // 使用Promise.all并行加载所有数据
      Promise.all([
        // 加载基础数据
        new Promise((resolve) => {
          wx.request({
            url: http_url,
            method: 'GET',
            success: function (res) {
              if (res.data && res.data.code == 1) {
                self.setData({
                  fabuData: Array.isArray(res.data.data) ? res.data.data : []
                });
              }
              resolve();
            },
            fail: function() {
              resolve();
            }
          });
        }),
        // 加载轮播图
        new Promise((resolve) => {
          self.loadBanners();
          resolve();
        }),
        // 加载统计数据
        new Promise((resolve) => {
          self.loadStatistics();
          resolve();
        }),
        // 加载最新更新
        new Promise((resolve) => {
          self.loadLatestUpdates(1, false);
          resolve();
        })
      ]).then(() => {
        // 所有数据加载完成后隐藏loading
        wx.hideLoading();
      }).catch(error => {
        console.error('加载数据出错：', error);
        wx.hideLoading();
      });
    } catch (error) {
      console.error('页面加载出错：', error);
      wx.hideLoading();
    }
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function() {
    // 检查登录状态
    const member = wx.getStorageSync('member');
    const member_auth = wx.getStorageSync('member_auth');
    const member_uid = wx.getStorageSync('member_uid');

    // 如果未登录，跳转到登录页面
    if (!member || !member_auth || !member_uid || !member.id) {
      wx.reLaunch({
        url: '/pages/login/login'
      });
      return;
    }

    // 用户已登录，更新用户状态
    const isAdmin = member.is_admin && parseInt(member.is_admin) > 0;
    this.setData({
      isLoggedIn: true,
      isAdmin: isAdmin,
      currentUserId: member.id
    });

    // 设置当前选中的tabbar项为首页
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().setData({
        selected: 0
      });
    }
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   * 实现下拉刷新功能
   */
  onPullDownRefresh: function () {
    var self = this;
    
    // 重新加载所有数据
    Promise.all([
      // 重新加载基础数据
      new Promise((resolve) => {
        wx.request({
          url: http_url,
          method: 'GET',
          success: function (res) {
            if (res.data && res.data.code == 1) {
              self.setData({
                fabuData: Array.isArray(res.data.data) ? res.data.data : []
              });
            }
            resolve();
          },
          fail: function() {
            resolve();
          }
        });
      }),
      // 重新加载轮播图
      new Promise((resolve) => {
        self.loadBanners();
        resolve();
      }),
      // 重新加载统计数据
      new Promise((resolve) => {
        self.loadStatistics();
        resolve();
      }),
      // 重新加载最新更新
      new Promise((resolve) => {
        self.loadLatestUpdates(1, false);
        resolve();
      })
    ]).then(() => {
      // 停止下拉刷新动画
      wx.stopPullDownRefresh();
      // 显示刷新成功提示
      wx.showToast({
        title: '刷新成功',
        icon: 'success',
        duration: 1500
      });
    }).catch(error => {
      console.error('刷新数据出错：', error);
      wx.stopPullDownRefresh();
      wx.showToast({
        title: '刷新失败',
        icon: 'error',
        duration: 1500
      });
    });
  },

  /**
   * 加载轮播图数据
   * 从服务器获取轮播图图片URL
   */
  loadBanners: function() {
    var self = this;
    wx.request({
      url: http_url + 's=httpapi&id=1',
      method: 'GET',
      success: function(res) {
        if (res.data.code == 1 && Array.isArray(res.data.data)) {
          // 过滤无效的轮播图URL
          const validBanners = res.data.data.filter(item => {
            return typeof item === 'string' && item.trim() !== '';
          });
          
          self.setData({
            banners: validBanners
          });
        } else {
          // 如果数据无效，设置默认图片
          self.setData({
            banners: ['/images/default-banner.png']
          });
        }
      },
      fail: function(error) {
        console.error('加载轮播图失败：', error);
        // 加载失败时设置默认图片
        self.setData({
          banners: ['/images/default-banner.png']
        });
      }
    });
  },

  /**
   * 加载工单统计数据
   * 获取各类工单的数量统计
   */
  loadStatistics: function() {
    var self = this;
    const appid = app.globalData.appid;
    const appsecret = app.globalData.appsecret;
    
    // 获取总工单数
    wx.request({
      url: http_url + 's=httpapi&id=4',
      method: 'GET',
      success: function(res) {
        if (res.data.code == 1) {
          self.setData({
            totalTickets: res.data.data.total || 0
          });
        }
      }
    });

    // 获取待处理工单数
    wx.request({
      url: http_url + 's=httpapi&id=5',
      method: 'GET',
      success: function(res) {
        if (res.data.code == 1) {
          self.setData({
            pendingTickets: res.data.data.total || 0
          });
        }
      }
    });

    // 获取处理中工单数
    wx.request({
      url: http_url + 's=httpapi&id=6',
      method: 'GET',
      success: function(res) {
        if (res.data.code == 1) {
          self.setData({
            processingTickets: res.data.data.total || 0
          });
        }
      }
    });

    // 获取已完成工单数
    wx.request({
      url: http_url + 's=httpapi&id=7',
      method: 'GET',
      success: function(res) {
        if (res.data.code == 1) {
          self.setData({
            completedTickets: res.data.data.total || 0
          });
        }
      }
    });
  },

  /**
   * 获取工单状态对应的样式类
   * @param {string} jindu - 工单进度状态
   * @return {string} 对应的CSS类名
   */
  getStatusClass: function(jindu) {
    var statusMap = {
      '待受理': 'status-pending',
      '待回复': 'status-waiting',
      '已回复': 'status-replied',
      '处理中': 'status-processing',
      '已完毕': 'status-completed'
    };
    return statusMap[jindu] || 'status-default';
  },

  /**
   * 加载最新更新列表（合并工单和公告）
   * @param {number} page - 当前页码
   * @param {boolean} isLoadMore - 是否为加载更多
   */
  loadLatestUpdates: function(page = 1, isLoadMore = false) {
    var self = this;

    // 如果正在加载中，则返回
    if (this.data.updateIsLoading) return;

    // 设置加载状态
    this.setData({ updateIsLoading: true });

    // 并行加载工单和公告数据
    Promise.all([
      // 加载最新工单
      new Promise((resolve) => {
        wx.request({
          url: http_url + 's=workorder&c=search&pagesize=10&page=' + page + '&api_call_function=module_list',
          method: 'GET',
          success: function(res) {
            let tickets = [];
            if (res.data && res.data.code == 1 && Array.isArray(res.data.data)) {
              tickets = res.data.data.map(function(item) {
                if (!item) return null;
                return {
                  id: item.id || '',
                  type: 'ticket',
                  title: item.title || '',
                  description: item.description || '',
                  author: item.author || '',
                  updateTime: item.inputtime || '',
                  status: item.jindu || '未知状态',
                  gongkai: item.gongkai || '',
                  userid: item.userid || ''
                };
              }).filter(item => {
                // 过滤掉无效数据和保密工单（根据权限）
                if (item === null) return false;

                if (item.gongkai === '保密') {
                  if (!self.data.isLoggedIn) return false;
                  return self.data.isAdmin || item.userid == self.data.currentUserId;
                }

                return true;
              });
            }
            resolve(tickets);
          },
          fail: function() {
            resolve([]);
          }
        });
      }),

      // 加载最新公告
      new Promise((resolve) => {
        wx.request({
          url: http_url + 's=news&c=search&api_call_function=module_list&pagesize=10&page=' + page,
          method: 'GET',
          success: function(res) {
            let notices = [];
            if (res.data && res.data.code == 1 && Array.isArray(res.data.data)) {
              notices = res.data.data.map(function(item) {
                return {
                  id: item.id || '',
                  type: 'notice',
                  title: item.title || '',
                  description: item.description || '',
                  author: item.author || '',
                  updateTime: item.updatetime || ''
                };
              });
            }
            resolve(notices);
          },
          fail: function() {
            resolve([]);
          }
        });
      })
    ]).then(([tickets, notices]) => {
      try {
        // 合并工单和公告数据
        let allData = [...tickets, ...notices];

        // 按更新时间排序（最新的在前）
        allData.sort((a, b) => {
          const timeA = this.parseDate(a.updateTime).getTime() || 0;
          const timeB = this.parseDate(b.updateTime).getTime() || 0;
          return timeB - timeA;
        });

        // 限制显示数量
        allData = allData.slice(0, 20);

        // 判断是否还有更多数据
        const hasMore = tickets.length === 10 || notices.length === 10;

        if (isLoadMore) {
          // 加载更多时，追加数据
          const newAllUpdates = [...self.data.allUpdates, ...allData];
          self.setData({
            allUpdates: newAllUpdates,
            updatePage: page,
            updateHasMore: hasMore
          });
        } else {
          // 首次加载或刷新时，替换数据
          self.setData({
            allUpdates: allData,
            updatePage: 1,
            updateHasMore: hasMore
          });
        }

        // 应用筛选
        self.filterUpdates();

      } catch (error) {
        console.error('处理更新数据时出错：', error);
        if (!isLoadMore) {
          self.setData({
            allUpdates: [],
            filteredUpdates: [],
            updatePage: 1,
            updateHasMore: false
          });
        }
      }
    }).catch(error => {
      console.error('加载更新数据失败：', error);
      if (!isLoadMore) {
        self.setData({
          allUpdates: [],
          filteredUpdates: [],
          updatePage: 1,
          updateHasMore: false
        });
      }
    }).finally(() => {
      self.setData({ updateIsLoading: false });
    });
  },



  /**
   * 搜索输入事件处理
   * @param {Object} e - 事件对象
   */
  onSearchInput: function(e) {
    const keyword = e.detail.value;
    this.setData({
      searchKeyword: keyword,
      showSearchHistory: keyword.length === 0 && this.data.searchHistory.length > 0
    });
  },

  /**
   * 搜索框获得焦点
   */
  onSearchFocus: function() {
    if (this.data.searchKeyword.length === 0 && this.data.searchHistory.length > 0) {
      this.setData({
        showSearchHistory: true
      });
    }
  },

  /**
   * 搜索框失去焦点
   */
  onSearchBlur: function() {
    // 延迟隐藏搜索历史，以便用户能够点击历史项
    setTimeout(() => {
      this.setData({
        showSearchHistory: false
      });
    }, 200);
  },

  /**
   * 搜索确认事件处理
   */
  onSearchConfirm: function() {
    const keyword = this.data.searchKeyword.trim();
    if (keyword) {
      this.saveSearchHistory(keyword);
      this.performSearch(keyword);
    }
  },

  /**
   * 选择搜索历史关键词
   * @param {Object} e - 事件对象
   */
  selectHistoryKeyword: function(e) {
    const keyword = e.currentTarget.dataset.keyword;
    this.setData({
      searchKeyword: keyword,
      showSearchHistory: false
    });
    this.performSearch(keyword);
  },

  /**
   * 执行搜索
   * @param {string} keyword - 搜索关键词
   */
  performSearch: function(keyword) {
    // 检查用户权限
    const member = wx.getStorageSync('member');
    const member_auth = wx.getStorageSync('member_auth');
    const member_uid = wx.getStorageSync('member_uid');

    if (!member || !member_auth || !member_uid) {
      wx.showModal({
        title: '提示',
        content: '请先登录',
        showCancel: false,
        success: function() {
          wx.navigateTo({
            url: '/pages/login/login'
          });
        }
      });
      return;
    }

    // 检查是否为管理员
    if (!member.is_admin || member.is_admin <= 0) {
      wx.showModal({
        title: '提示',
        content: '村民档案搜索功能仅管理员可用',
        showCancel: false
      });
      return;
    }

    // 跳转到村民档案列表页面，并传递搜索关键词
    wx.navigateTo({
      url: '/pages/cmda/list?search=' + encodeURIComponent(keyword)
    });
  },

  /**
   * 加载搜索历史
   */
  loadSearchHistory: function() {
    try {
      const history = wx.getStorageSync('searchHistory') || [];
      this.setData({
        searchHistory: history.slice(0, 8) // 最多显示8个历史记录
      });
    } catch (error) {
      console.error('加载搜索历史失败：', error);
    }
  },

  /**
   * 保存搜索历史
   * @param {string} keyword - 搜索关键词
   */
  saveSearchHistory: function(keyword) {
    try {
      let history = wx.getStorageSync('searchHistory') || [];

      // 移除重复的关键词
      history = history.filter(item => item !== keyword);

      // 添加到开头
      history.unshift(keyword);

      // 限制历史记录数量
      history = history.slice(0, 10);

      wx.setStorageSync('searchHistory', history);
      this.setData({
        searchHistory: history.slice(0, 8)
      });
    } catch (error) {
      console.error('保存搜索历史失败：', error);
    }
  },

  /**
   * 清空搜索历史
   */
  clearSearchHistory: function() {
    wx.showModal({
      title: '确认清空',
      content: '确定要清空所有搜索历史吗？',
      success: (res) => {
        if (res.confirm) {
          try {
            wx.removeStorageSync('searchHistory');
            this.setData({
              searchHistory: [],
              showSearchHistory: false
            });
            wx.showToast({
              title: '已清空',
              icon: 'success'
            });
          } catch (error) {
            console.error('清空搜索历史失败：', error);
          }
        }
      }
    });
  },

  /**
   * 切换更新筛选器
   * @param {Object} e - 事件对象
   */
  changeUpdateFilter: function(e) {
    const filter = e.currentTarget.dataset.filter;
    this.setData({
      updateFilter: filter
    });
    this.filterUpdates();
  },

  /**
   * 筛选更新数据
   */
  filterUpdates: function() {
    const { allUpdates, updateFilter } = this.data;
    let filtered = [];

    switch (updateFilter) {
      case 'tickets':
        filtered = allUpdates.filter(item => item.type === 'ticket');
        break;
      case 'notices':
        filtered = allUpdates.filter(item => item.type === 'notice');
        break;
      default:
        filtered = allUpdates;
        break;
    }

    this.setData({
      filteredUpdates: filtered
    });
  },

  /**
   * 跳转到更新详情页
   * @param {Object} e - 事件对象
   */
  goToUpdateDetail: function(e) {
    const item = e.currentTarget.dataset.item;
    if (item.type === 'ticket') {
      wx.navigateTo({
        url: '/pages/workorder/show?id=' + item.id
      });
    } else if (item.type === 'notice') {
      wx.navigateTo({
        url: '/pages/gonggao/gonggao?id=' + item.id
      });
    }
  },

  /**
   * 加载更多更新
   */
  loadMoreUpdates: function() {
    if (this.data.updateHasMore && !this.data.updateIsLoading) {
      this.loadLatestUpdates(this.data.updatePage + 1, true);
    }
  },

  /**
   * 解析日期字符串，兼容iOS
   * @param {string} dateString - 日期字符串
   * @return {Date} 日期对象
   */
  parseDate: function(dateString) {
    if (!dateString) return new Date(0);

    // 如果已经是Date对象，直接返回
    if (dateString instanceof Date) return dateString;

    // 转换为字符串
    let dateStr = String(dateString);

    // iOS兼容性处理：将 "yyyy-MM-dd HH:mm:ss" 格式转换为 "yyyy/MM/dd HH:mm:ss"
    // 这种格式在iOS上更兼容
    dateStr = dateStr.replace(/-/g, '/');

    // 尝试解析日期
    try {
      const date = new Date(dateStr);
      // 检查日期是否有效
      if (isNaN(date.getTime())) {
        console.warn('无效的日期格式:', dateString);
        return new Date(0);
      }
      return date;
    } catch (error) {
      console.error('日期解析错误:', error, dateString);
      return new Date(0);
    }
  },

  /**
   * 退出登录（用于测试）
   */
  logout: function() {
    wx.showModal({
      title: '确认退出',
      content: '确定要退出登录吗？',
      success: (res) => {
        if (res.confirm) {
          // 清除登录信息
          wx.clearStorageSync();

          // 跳转到登录页面
          wx.reLaunch({
            url: '/pages/login/login'
          });
        }
      }
    });
  }
})