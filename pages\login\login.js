var app = getApp();
Page({

  /**
   * 页面的初始数据
   */
  data: {
    userName: "",
    userPwd: "",
    isLoading: false,
    showPassword: false,
    remember: false
  },

  onLoad: function () {
    // 检查是否有保存的账号密码
    this.checkSavedCredentials();
  },
  
  checkSavedCredentials: function() {
    try {
      const savedUserName = wx.getStorageSync('savedUserName');
      const savedUserPwd = wx.getStorageSync('savedUserPwd');
      const remember = wx.getStorageSync('rememberPassword');
      
      if (remember && savedUserName && savedUserPwd) {
        this.setData({
          userName: savedUserName,
          userPwd: savedUserPwd,
          remember: true
        });
      }
    } catch (e) {
      console.error('获取保存的账号密码失败', e);
    }
  },

  getUserName: function (e) {
    this.setData({
      userName: e.detail.value
    });
  },
  
  getUserPwd: function (e) {
    this.setData({ 
      userPwd: e.detail.value 
    });
  },
  
  clear: function() {
    this.setData({
      userName: ""
    });
  },
  
  togglePasswordVisibility: function() {
    this.setData({
      showPassword: !this.data.showPassword
    });
  },
  
  toggleRemember: function() {
    this.setData({
      remember: !this.data.remember
    });
  },
  


  login: function() {
    // 表单验证
    if (!this.data.userName || !this.data.userName.trim()) {
      wx.showToast({
        title: '请输入账号',
        icon: 'none'
      });
      return;
    }
    
    if (!this.data.userPwd || !this.data.userPwd.trim()) {
      wx.showToast({
        title: '请输入密码',
        icon: 'none'
      });
      return;
    }
    
    // 显示加载状态
    this.setData({
      isLoading: true
    });
    
    const self = this;
    const postParams = "is_ajax=1&data[username]=" + this.data.userName + "&data[password]=" + this.data.userPwd;
    
    wx.request({//登录
      url: app.globalData.http_api + "s=member&c=login",
      data: postParams,
      method: 'post',
      header: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      success: function (res) {
        console.log(res.data);
        if (res.data.code == 1) {
          // 记住密码功能
          if (self.data.remember) {
            wx.setStorageSync('savedUserName', self.data.userName);
            wx.setStorageSync('savedUserPwd', self.data.userPwd);
            wx.setStorageSync('rememberPassword', true);
          } else {
            wx.removeStorageSync('savedUserName');
            wx.removeStorageSync('savedUserPwd');
            wx.removeStorageSync('rememberPassword');
          }
          
          // 登录成功储存会员信息
          wx.clearStorageSync();
          wx.setStorageSync('member_uid', res.data.data.member.id);
          wx.setStorageSync('member_auth', res.data.data.auth);
          wx.setStorageSync('member', res.data.data.member);
          
          // 跳转到首页
          wx.showToast({
            title: "登录成功",
            icon: 'success',
            success: function () {
              wx.reLaunch({ url: "../home/<USER>" });
            },
            duration: 2000
          })
        }
        else {
          wx.showModal({
            showCancel: false,
            content: res.data.msg
          })
        }
      },
      fail: function(err) {
        console.error('登录请求失败', err);
        wx.showModal({
          showCancel: false,
          content: '网络连接失败，请检查网络后重试'
        });
      },
      complete: function() {
        // 隐藏加载状态
        self.setData({
          isLoading: false
        });
      }
    })
  }
})