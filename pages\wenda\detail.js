// pages/wenda/detail.js
const app = getApp()
// 引入WxParse模块
var WxParse = require('../../wxParse/wxParse.js');

Page({

  /**
   * 页面的初始数据
   */
  data: {
    detail: null,
    relatedImages: [],
    originalImages: [],
    apiUrl: '', 
    appid: '', // 需要填写
    appsecret: '', // 需要填写
    // 评论相关数据
    commentList: [],
    commentText: '',
    tempImagePaths: [],
    isCommentBarShow: false,
    isLoading: false,
    hasMore: true,
    member: null,
    page: 1,
    pageSize: 10
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    // 获取ID
    const id = options.id
    
    // 从全局配置获取API信息
    if (app.globalData && app.globalData.config) {
      this.setData({
        appid: app.globalData.config.appid || '',
        appsecret: app.globalData.config.appsecret || ''
      })
    }
    
    // 获取用户信息
    var member = wx.getStorageSync('member');
    if (member) {
      this.setData({ member: member });
    }
    
    // 为WxParse模块添加全局方法引用
    this.wxParseImgLoad = this.wxParseImgLoad.bind(this);
    this.wxParseImgTap = this.wxParseImgTap.bind(this);
    
    // 加载详情
    this.loadDetail(id)
    
    // 加载评论列表
    this.loadComments()
    
    // 加载点赞数据
    this.loadDiggData(id)
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {
    // 清理WxParse相关内存
    const keys = Object.keys(this.data);
    keys.forEach(key => {
      if (key.startsWith('comment_')) {
        this.data[key] = null;
      }
    });
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    if (this.data.detail && this.data.detail.id) {
      this.loadDetail(this.data.detail.id)
      this.loadComments(true)
      wx.stopPullDownRefresh()
    }
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {
    if (this.data.hasMore && !this.data.isLoading) {
      this.loadMoreComments()
    }
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    if (!this.data.detail) return {}
    
    return {
      title: this.data.detail.title,
      path: '/pages/wenda/detail?id=' + this.data.detail.id
    }
  },
  
  // 加载详情数据
  loadDetail: function(id) {
    wx.showLoading({ title: '加载中...' })
    
    // 构建API请求URL
    const requestUrl = app.globalData.http_api + 
      's=wenda&c=show&id=' + id + 
      '&api_call_function=module_show' +
      '&appid=' + app.globalData.appid +
      '&appsecret=' + app.globalData.appsecret;
    
    wx.request({
      url: requestUrl,
      method: 'GET',
      header: {
        'content-type': 'application/json'
      },
      success: (res) => {
        wx.hideLoading()
        
        if (res.data && res.data.code === 1 && res.data.data) {
          const detailData = res.data.data
          
          // 处理状态值
          let statusValue = 0;
          let statusText = '未解决';
          
          // 将文本状态转换为数字状态值
          if (detailData.is_comment !== undefined) {
            if (typeof detailData.is_comment === 'string') {
              if (detailData.is_comment === '已完成') {
                statusValue = 1;
                statusText = '已完成';
              } else if (detailData.is_comment === '解决中') {
                statusValue = 2;
                statusText = '解决中';
              } else {
                statusValue = 0;
                statusText = '未解决';
              }
            } else {
              // 如果是数字，直接使用
              statusValue = parseInt(detailData.is_comment);
              switch(statusValue) {
                case 1:
                  statusText = '已完成';
                  break;
                case 2:
                  statusText = '解决中';
                  break;
                default:
                  statusText = '未解决';
              }
            }
          }
          
          // 处理详情数据，直接使用接口返回的所有字段
          this.setData({
            detail: {
              ...detailData, // 保留原有所有字段
              status_value: statusValue,
              status_text: statusText,
              like_count: parseInt(detailData.support) || 0,
              unlike_count: parseInt(detailData.oppose) || 0,
            },
            relatedImages: detailData.xgzp || [],
            originalImages: detailData.ytzp || []
          })
          
          // 更新页面标题
          wx.setNavigationBarTitle({
            title: detailData.title || '详情'
          });
          
          // 如果有内容，使用WxParse解析
          if (detailData.content) {
            WxParse.wxParse('article', 'html', detailData.content, this, 5);
          }
        } else {
          wx.showToast({
            title: '获取详情失败',
            icon: 'none'
          })
        }
      },
      fail: (err) => {
        wx.hideLoading()
        wx.showToast({
          title: '网络请求失败',
          icon: 'none'
        })
      }
    })
  },
  
  // 预览相关展品图片
  previewRelatedImage: function(e) {
    const index = e.currentTarget.dataset.index
    const images = this.data.relatedImages.map(item => item.file)
    
    wx.previewImage({
      current: images[index],
      urls: images
    })
  },
  
  // 预览原图展品图片
  previewOriginalImage: function(e) {
    const index = e.currentTarget.dataset.index
    const images = this.data.originalImages.map(item => item.file)
    
    wx.previewImage({
      current: images[index],
      urls: images
    })
  },
  
  // 处理反馈链接点击
  navigateToFankui: function(e) {
    const url = e.currentTarget.dataset.url;
    const id = e.currentTarget.dataset.id;
    
    console.log('反馈链接点击', id, url);
    
    // 跳转到相应的页面
    if (id) {
      wx.navigateTo({
        url: '/pages/cmda/cmdashow?id=' + id,
        success: function() {
          console.log('导航成功');
        },
        fail: function(error) {
          console.error('导航失败', error);
          // 导航失败时尝试使用webview或复制链接
          if (url) {
            wx.setClipboardData({
              data: url,
              success: function() {
                wx.showToast({
                  title: '链接已复制',
                  icon: 'success'
                });
              }
            });
          }
        }
      });
    } else if (url) {
      // 如果只有url，直接复制链接
      wx.setClipboardData({
        data: url,
        success: function() {
          wx.showToast({
            title: '链接已复制',
            icon: 'success'
          });
        }
      });
    }
  },
  
  // 预览单张图片
  previewSingleImage: function(e) {
    const url = e.currentTarget.dataset.url
    
    wx.previewImage({
      current: url,
      urls: [url]
    })
  },
  
  // 预览文档图片
  previewDocImage: function(e) {
    const index = e.currentTarget.dataset.index
    const images = e.currentTarget.dataset.images
    const urls = images.map(item => item.url)
    
    wx.previewImage({
      current: urls[index],
      urls: urls
    })
  },
  
  // 打开文档
  openDocument: function(e) {
    const url = e.currentTarget.dataset.url
    
    wx.showLoading({
      title: '正在下载...',
    })
    
    // 下载文档
    wx.downloadFile({
      url: url,
      success: function(res) {
        wx.hideLoading()
        // 打开文档
        wx.openDocument({
          filePath: res.tempFilePath,
          success: function(res) {
            console.log('打开文档成功')
          },
          fail: function(err) {
            wx.showToast({
              title: '无法打开文档',
              icon: 'none'
            })
          }
        })
      },
      fail: function(err) {
        wx.hideLoading()
        wx.showToast({
          title: '文档下载失败',
          icon: 'none'
        })
      }
    })
  },
  
  // 返回上一页
  goBack: function() {
    wx.navigateBack({
      delta: 1
    })
  },
  
  // 显示评论弹出层
  showFullCommentBar: function() {
    // 获取验证信息
    var member_uid = wx.getStorageSync('member_uid');
    var member_auth = wx.getStorageSync('member_auth');
    var member = wx.getStorageSync('member');
    
    if (!member_uid || !member_auth || !member) {
      wx.showModal({
        title: '提示',
        content: '请先登录后再评论',
        success: (res) => {
          if (res.confirm) {
            wx.navigateTo({
              url: '/pages/login/login'
            })
          }
        }
      })
      return
    }
    
    this.setData({
      isCommentBarShow: true
    })
  },
  
  // 隐藏评论弹出层
  hideFullCommentBar: function() {
    this.setData({
      isCommentBarShow: false,
      commentText: '',
      tempImagePaths: []
    })
  },
  
  // 获取评论输入内容
  getText: function(e) {
    this.setData({
      commentText: e.detail.value
    })
  },
  
  // 选择图片
  chooseImage: function() {
    wx.chooseMedia({
      count: 9 - this.data.tempImagePaths.length,
      mediaType: ['image'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        let tempFiles = res.tempFiles;
        let newPaths = tempFiles.map(file => file.tempFilePath);
        
        this.setData({
          tempImagePaths: this.data.tempImagePaths.concat(newPaths)
        });
      }
    })
  },
  
  // 删除已选图片
  deleteImage: function(e) {
    const index = e.currentTarget.dataset.index
    let paths = this.data.tempImagePaths
    paths.splice(index, 1)
    this.setData({ tempImagePaths: paths })
  },
  
  // 保存评论
  saveComment: function() {
    if ((!this.data.commentText || this.data.commentText.trim() === '') && this.data.tempImagePaths.length === 0) {
      wx.showToast({
        title: '请输入评论内容或上传图片',
        icon: 'none'
      })
      return
    }
    
    // 获取验证信息
    var member_uid = wx.getStorageSync('member_uid');
    var member_auth = wx.getStorageSync('member_auth');
    var member = wx.getStorageSync('member');
    
    if (!member_uid || !member_auth || !member) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      })
      return
    }
    
    wx.showLoading({
      title: '提交中...',
      mask: true
    })
    
    // 先上传图片（如果有）
    if (this.data.tempImagePaths.length > 0) {
      this.uploadImages(0, [])
    } else {
      this.submitComment('', [])
    }
  },
  
  // 递归上传图片
  uploadImages: function(index, uploadedImages, imagesArray = []) {
    if (index >= this.data.tempImagePaths.length) {
      // 所有图片上传完成，提交评论
      let imagesHtml = '';
      
      // 构建图片HTML内容
      if (uploadedImages.length > 0) {
        // 创建一个包裹所有图片的容器div，根据图片数量添加特定类名
        let containerClass = 'wxParse-p-with-img';
        if (uploadedImages.length === 1) {
          containerClass += ' single-img';
        } else if (uploadedImages.length === 2) {
          containerClass += ' double-img';
        }
        
        imagesHtml = `<div class="${containerClass}">`;
        
        // 添加所有图片
        uploadedImages.forEach(url => {
          if (uploadedImages.length === 1) {
            // 单图样式
            imagesHtml += `<img src="${url}" class="wxParse-img wxParse-img-single" mode="widthFix">`;
          } else if (uploadedImages.length === 2) {
            // 双图样式
            imagesHtml += `<img src="${url}" class="wxParse-img wxParse-img-double" mode="aspectFill">`;
          } else {
            // 多图样式
            imagesHtml += `<img src="${url}" class="wxParse-img" mode="aspectFill">`;
          }
        });
        
        // 如果超过3张图片，添加计数指示器
        if (uploadedImages.length > 3) {
          imagesHtml += `<div class="image-count-indicator">共${uploadedImages.length}张</div>`;
        }
        
        // 关闭容器
        imagesHtml += '</div>';
      }
      
      // 直接传递图片数组，而不是JSON字符串
      this.submitComment(imagesHtml, imagesArray);
      return;
    }
    
    const imgPath = this.data.tempImagePaths[index];
    
    // 获取验证信息
    var member_uid = wx.getStorageSync('member_uid');
    var member_auth = wx.getStorageSync('member_auth');
    
    // 使用cmdashow.js中的上传图片接口
    wx.uploadFile({
      url: app.globalData.http_api + 's=api&c=file&m=ueditor&action=uploadimage&encode=utf-8',
      filePath: imgPath,
      name: 'upfile',
      formData: {
        'api_auth_uid': member_uid,
        'api_auth_code': member_auth
      },
      success: (res) => {
        try {
          // 尝试解析JSON
          let data = JSON.parse(res.data);
          if (data && data.url) {
            uploadedImages.push(data.url);
            // 同时收集图片URL到数组中
            imagesArray.push(data.url);
          } else if (data && data.state === 'SUCCESS' && data.url) {
            uploadedImages.push(data.url);
            // 同时收集图片URL到数组中
            imagesArray.push(data.url);
          }
        } catch (e) {
          // 检查返回的数据是否是HTML格式(通常由 '<' 开始)
          if (res.data && typeof res.data === 'string' && res.data.startsWith('<')) {
            wx.showToast({
              title: '图片上传失败',
              icon: 'none'
            });
          }
        }
      },
      fail: (err) => {
        // 图片上传失败
      },
      complete: () => {
        // 上传下一张，无论成功失败都继续
        this.uploadImages(index + 1, uploadedImages, imagesArray);
      }
    });
  },
  
  // 提交评论 - 同时传递富文本内容和图片数组
  submitComment: function(imagesHtml, imagesArray) {
    // 获取验证信息
    var member_uid = wx.getStorageSync('member_uid');
    var member_auth = wx.getStorageSync('member_auth');
    var member = wx.getStorageSync('member');
    
    // 检查详情数据是否存在
    if (!this.data.detail || !this.data.detail.id) {
      wx.hideLoading();
      wx.showToast({
        title: '无法获取内容ID',
        icon: 'none'
      });
      return;
    }
    
    // 构建内容
    let content = this.data.commentText || '';
    if (imagesHtml) {
      if (content) {
        content = `<p>${content}</p>${imagesHtml}`;
      } else {
        content = imagesHtml;
      }
    }
    
    // 修复API请求URL格式，避免参数重复
    const url = app.globalData.http_api + 
      "index.php?v=1" +
      "&s=wenda&c=comment" + 
      "&id=" + this.data.detail.id +
      "&m=post" +
      "&api_auth_uid=" + member_uid + 
      "&api_auth_code=" + member_auth;
    
    // 构建请求参数
    const params = {
      content: content,
      images: JSON.stringify(imagesArray), // 将图片数组转换为JSON字符串
      uid: member_uid,
      author: member.username || '匿名用户',
      status: 1
    };
    
    wx.request({
      url: url,
      method: 'POST',
      header: {
        'content-type': 'application/x-www-form-urlencoded'
      },
      data: params,
      success: (res) => {
        if (res.data.code == 1) {
          wx.showToast({
            title: '评论成功',
            icon: 'success'
          });
          
          // 重置数据
          this.setData({
            commentText: '',
            tempImagePaths: [],
            isCommentBarShow: false,
            commentList: [],
            page: 1,
            hasMore: true
          });
          
          // 重新加载评论
          this.loadComments(true);
          
          // 刷新主数据，更新评论数量
          this.loadDetail(this.data.detail.id);
        } else {
          wx.showToast({
            title: res.data.msg || '评论失败',
            icon: 'none',
            duration: 2000
          });
        }
      },
      fail: (err) => {
        wx.showToast({
          title: '网络错误',
          icon: 'none'
        });
      },
      complete: () => {
        wx.hideLoading();
      }
    });
  },
  
  // 加载评论列表 - 使用正确的API路径
  loadComments: function(refresh = false) {
    if (!this.data.detail || !this.data.detail.id) {
      return;
    }
    
    if (refresh) {
      this.setData({
        page: 1,
        commentList: [],
        hasMore: true
      });
    }
    
    if (this.data.isLoading || !this.data.hasMore) {
      return;
    }
    
    this.setData({
      isLoading: true
    });
    
    // 获取验证信息
    var member_uid = wx.getStorageSync('member_uid');
    var member_auth = wx.getStorageSync('member_auth');
    
    // 修复API请求URL格式，避免参数重复
    const requestUrl = app.globalData.http_api + 
      'index.php?v=1' +
      '&s=wenda&c=comment' +
      '&id=' + this.data.detail.id + 
      '&page=' + this.data.page + 
      '&pagesize=' + this.data.pageSize +
      '&api_call_function=module_comment_list' +
      '&api_auth_uid=' + member_uid +
      '&api_auth_code=' + member_auth;
    
    wx.request({
      url: requestUrl,
      method: 'GET',
      header: {
        'content-type': 'application/json'
      },
      success: (res) => {
        if (res.data.code == 1 && res.data.data) {
          let comments = res.data.data;
          if (!Array.isArray(comments)) {
            if (comments && comments.list) {
              comments = comments.list;
            } else {
              comments = [];
            }
          }
          const total = res.data.total || 0;
          
          // 格式化评论数据
          const formattedComments = comments.map((item, index) => {
            const uniqueKey = `${item.id || ''}_${item.inputtime || ''}_${Date.now() + index}`;
            const nodeName = `comment_${uniqueKey}`;
            
            // 处理评论图片
            if (item.images && typeof item.images === 'string' && item.images.length > 0) {
              try {
                // 尝试解析图片JSON字符串
                const parsedImages = JSON.parse(item.images);
                // 确保解析结果是数组
                item.images = Array.isArray(parsedImages) ? parsedImages : [];
              } catch (e) {
                item.images = [];
              }
            } else {
              item.images = [];
            }
            
            // 使用WxParse解析评论内容
            if (item.content) {
              WxParse.wxParse(nodeName, 'html', item.content, this, 5);
            }
            
            return {
              id: item.id,
              content: item.content || '',
              author: item.username || item.author || '匿名用户',
              avatar: item.avatar || '../../images/avatar.png',
              inputtime: item.inputtime || '',
              uid: item.uid || 0,
              images: item.images || [],
              uniqueKey: uniqueKey,
              parsedContent: this.data[nodeName]
            };
          });
          
          // 更新数据
          this.setData({
            commentList: [...this.data.commentList, ...formattedComments],
            hasMore: this.data.commentList.length + formattedComments.length < total || formattedComments.length >= this.data.pageSize,
            page: this.data.page + 1,
            isLoading: false
          });
        } else {
          this.setData({
            hasMore: false,
            isLoading: false
          });
        }
      },
      fail: (err) => {
        this.setData({
          isLoading: false
        });
        wx.showToast({
          title: '加载评论失败',
          icon: 'none'
        });
      }
    });
  },
  
  // 添加WxParse的图片点击预览方法
  wxParseImgTap: function(e) {
    const src = e.currentTarget.dataset.src;
    if (!src) return;
    
    // 收集当前评论中的所有图片URL
    const commentList = this.data.commentList;
    const allImgUrls = [];
    
    // 遍历所有评论，查找所有图片
    for (let i = 0; i < commentList.length; i++) {
      const comment = commentList[i];
      if (comment.parsedContent && comment.parsedContent.nodes) {
        this.findImgUrls(comment.parsedContent.nodes, allImgUrls);
      }
    }
    
    // 如果没有找到图片，至少显示当前图片
    const imageUrls = allImgUrls.length > 0 ? allImgUrls : [src];
    
    wx.previewImage({
      current: src,
      urls: imageUrls
    });
  },
  
  // 递归查找所有图片URL
  findImgUrls: function(nodes, result) {
    if (!nodes) return;
    
    for (let i = 0; i < nodes.length; i++) {
      const node = nodes[i];
      if (node.tag === 'img' && node.attr && node.attr.src) {
        result.push(node.attr.src);
      }
      
      if (node.nodes && node.nodes.length > 0) {
        this.findImgUrls(node.nodes, result);
      }
    }
  },
  
  // WxParse的图片加载函数
  wxParseImgLoad: function(e) {
    // 图片加载完成时的处理
  },
  
  // 加载更多评论
  loadMoreComments: function() {
    this.loadComments();
  },
  
  // 预览评论图片
  previewImage: function(e) {
    const current = e.currentTarget.dataset.current;
    const urls = e.currentTarget.dataset.urls;
    
    wx.previewImage({
      current,
      urls
    });
  },
  
  /**
   * 点赞/踩操作
   */
  moduleDigg: function(e) {
    const id = e.currentTarget.dataset.id;
    const value = e.currentTarget.dataset.value;
    
    // 检查用户是否登录
    const member_uid = wx.getStorageSync('member_uid');
    const member_auth = wx.getStorageSync('member_auth');
    
    if (!member_uid || !member_auth) {
      wx.showModal({
        title: '提示',
        content: '请先登录后再进行此操作',
        confirmText: '去登录',
        success: (res) => {
          if (res.confirm) {
            wx.navigateTo({
              url: '../login/login'
            });
          }
        }
      });
      return;
    }
    
    // 构建请求URL
    const url = app.globalData.http_api + 
      'is_ajax=1&s=api&app=wenda&c=module&m=digg&id=' + id + '&value=' + value +
      '&appid=' + app.globalData.appid +
      '&appsecret=' + app.globalData.appsecret;
    
    wx.request({
      url: url,
      method: 'GET',
      header: {
        'content-type': 'application/json'
      },
      success: (res) => {
        if (res.data && res.data.code) {
          // 更新点赞数量
          const diggElem = `digg_${id}_${value}`;
          const selector = `#${diggElem}`;
          const query = wx.createSelectorQuery();
          query.select(selector).node().exec((nodeRes) => {
            if (nodeRes[0] && nodeRes[0].node) {
              nodeRes[0].node.textContent = res.data.data;
            }
          });
          
          wx.showToast({
            title: res.data.msg || '操作成功',
            icon: 'success'
          });
          
          // 更新本地数据
          const detail = { ...this.data.detail };
          if (value == 1) {
            detail.like_count = res.data.data;
          } else {
            detail.unlike_count = res.data.data;
          }
          this.setData({ detail });
        } else {
          wx.showToast({
            title: res.data.msg || '操作失败',
            icon: 'none'
          });
        }
      },
      fail: (err) => {
        console.error('点赞操作失败:', err);
        wx.showToast({
          title: '网络请求失败',
          icon: 'none'
        });
      }
    });
  },
  
  /**
   * 加载点赞数据
   */
  loadDiggData: function(id) {
    if (!id) return;
    
    // 构建请求URL获取点赞数据
    const url = app.globalData.http_api + 
      'is_ajax=1&s=zan&mid=wenda&id=' + id +
      '&appid=' + app.globalData.appid +
      '&appsecret=' + app.globalData.appsecret;
    
    wx.request({
      url: url,
      method: 'GET',
      header: {
        'content-type': 'application/json'
      },
      success: (res) => {
        if (res.data && res.data.code) {
          const data = res.data.data;
          // 更新点赞数量
          const detail = { ...this.data.detail };
          detail.unlike_count = data.a;
          detail.like_count = data.b;
          this.setData({ detail });
          
          // 更新DOM元素
          const diggElem1 = `digg_${id}_1`;
          const selector1 = `#${diggElem1}`;
          const query1 = wx.createSelectorQuery();
          query1.select(selector1).node().exec((nodeRes) => {
            if (nodeRes[0] && nodeRes[0].node) {
              nodeRes[0].node.textContent = data.b;
            }
          });
          
          const diggElem0 = `digg_${id}_0`;
          const selector0 = `#${diggElem0}`;
          const query0 = wx.createSelectorQuery();
          query0.select(selector0).node().exec((nodeRes) => {
            if (nodeRes[0] && nodeRes[0].node) {
              nodeRes[0].node.textContent = data.a;
            }
          });
        }
      },
      fail: (err) => {
        console.error('获取点赞数据失败:', err, id);
      }
    });
  }
})