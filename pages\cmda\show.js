const app = getApp();

Page({
  data: {
    villager: null, // 村民详情
    loading: true, // 加载状态
    loadError: false, // 加载错误
    errorMsg: '', // 错误信息
  },

  onLoad: function(options) {
    // 检查传入的ID参数
    if (!options.id) {
      this.setData({
        loading: false,
        loadError: true,
        errorMsg: '参数错误，缺少村民ID'
      });
      return;
    }
    
    this.villagerId = options.id;
    
    // 加载村民详情
    this.loadDetail();
  },
  
  // 加载村民详情
  loadDetail: function() {
    return new Promise((resolve, reject) => {
      this.setData({
        loading: true,
        loadError: false,
        errorMsg: ''
      });

      // 获取验证信息
      var member_uid = wx.getStorageSync('member_uid');
      var member_auth = wx.getStorageSync('member_auth');

      if (!member_uid || !member_auth) {
        this.setData({
          loading: false,
          loadError: true,
          errorMsg: '请先登录'
        });
        reject(new Error('请先登录'));
        return;
      }
    
    // 构建API请求URL
    const requestUrl = app.globalData.http_api + 
      's=cmda&c=show&id=' + this.villagerId + 
      '&api_call_function=module_show' + 
      '&api_auth_uid=' + member_uid + 
      '&api_auth_code=' + member_auth;
    
    console.log('请求详情URL:', requestUrl);
    
    wx.showLoading({
      title: '加载中...'
    });
    
    wx.request({
      url: requestUrl,
      method: 'GET',
      header: {
        'content-type': 'application/json'
      },
      success: res => {
        console.log('获取村民详情结果:', res.data);
        
        if (res.data.code == 1 && res.data.data) {
          // 处理村民详情数据
          const villagerData = res.data.data;
          
          // 打印原始数据中的thumb字段
          console.log('原始数据中的thumb字段:', villagerData.thumb);
          
          // 提取可能的其他信息字段
          const otherInfo = [];
          const mainFields = ['id', 'title', 'sfzhm', 'hujidizhi', 'hujishuxing', 'shoujihaoma', 
                             'updatetime', 'inputtime', 'grzp', 'fwzp', 'qtzjzp', 'gczp', 
                             'hits', 'comments', 'xingbie', 'fankui', 'thumb'];
          
          // 预处理处理图片数据
          let allImages = [];
          
          // 处理缩略图
          if (villagerData.thumb) {
            // 如果thumb是对象，则取其file属性
            if (typeof villagerData.thumb === 'object' && villagerData.thumb.file) {
              villagerData.thumb = villagerData.thumb.file;
            } else if (Array.isArray(villagerData.thumb) && villagerData.thumb.length > 0) {
              // 如果是数组，取第一个元素
              if (typeof villagerData.thumb[0] === 'object') {
                villagerData.thumb = villagerData.thumb[0].file;
              } else {
                villagerData.thumb = villagerData.thumb[0];
              }
            }
            console.log('处理后的thumb字段:', villagerData.thumb);
          }
          
          // 身份证件照片
          if (villagerData.grzp && Array.isArray(villagerData.grzp) && villagerData.grzp.length > 0) {
            villagerData.grzpList = villagerData.grzp.map(item => item.file);
            allImages = allImages.concat(villagerData.grzpList);
          }
          
          // 房屋照片
          if (villagerData.fwzp && Array.isArray(villagerData.fwzp) && villagerData.fwzp.length > 0) {
            villagerData.fwzpList = villagerData.fwzp.map(item => item.file);
            allImages = allImages.concat(villagerData.fwzpList);
          }
          
          // 其他证件照片
          if (villagerData.qtzjzp && Array.isArray(villagerData.qtzjzp) && villagerData.qtzjzp.length > 0) {
            villagerData.qtzjzpList = villagerData.qtzjzp.map(item => item.file);
            allImages = allImages.concat(villagerData.qtzjzpList);
          }
          
          // 耕地照片
          if (villagerData.gczp && Array.isArray(villagerData.gczp) && villagerData.gczp.length > 0) {
            villagerData.gczpList = villagerData.gczp.map(item => item.file);
            allImages = allImages.concat(villagerData.gczpList);
          }
          
          // 计算照片总数
          const grzpCount = villagerData.grzpList ? villagerData.grzpList.length : 0;
          const fwzpCount = villagerData.fwzpList ? villagerData.fwzpList.length : 0;
          const qtzjzpCount = villagerData.qtzjzpList ? villagerData.qtzjzpList.length : 0;
          const gczpCount = villagerData.gczpList ? villagerData.gczpList.length : 0;
          villagerData.photoCount = grzpCount + fwzpCount + qtzjzpCount + gczpCount;
          
          // 设置所有图片列表
          villagerData.allImages = allImages;
          
          // 遍历所有字段，找出需要显示的信息
          for (const key in villagerData) {
            // 只处理有值的字段，且不在主字段列表中，并且排除photoCount
            if (villagerData.hasOwnProperty(key) && 
                !mainFields.includes(key) && 
                villagerData[key] && 
                !Array.isArray(villagerData[key]) && // 排除数组类型
                typeof villagerData[key] !== 'object' && // 排除对象类型
                key !== 'code' &&  // 排除状态码
                key !== 'msg' &&   // 排除消息
                !key.endsWith('List') && 
                key !== 'photoCount') {  // 排除处理后的列表和photoCount
              
              otherInfo.push({
                label: this.getFieldName(key),
                value: villagerData[key]
              });
            }
          }
          
          if (otherInfo.length > 0) {
            villagerData.otherInfo = otherInfo;
          }
          
          // 补充可能缺失的字段
          const processedData = {
            id: villagerData.id || this.villagerId,
            title: villagerData.title || villagerData.name || '未知姓名',
            sfzhm: villagerData.sfzhm || villagerData.keywords || villagerData.idcard || '未知',
            hujidizhi: villagerData.hujidizhi || villagerData.address || '未知',
            hujishuxing: villagerData.hujishuxing || '一般户',
            shoujihaoma: villagerData.shoujihaoma || villagerData.phone || villagerData.mobile || '未知',
            xingbie: villagerData.xingbie || '未知',
            hits: villagerData.hits || 0,
            comments: villagerData.comments || 0,
            updatetime: villagerData.updatetime || '未知',
            inputtime: villagerData.inputtime || '未知',
            otherInfo: villagerData.otherInfo || [],
            // 个人照片缩略图
            thumb: villagerData.thumb || '',
            // 图片相关
            grzpList: villagerData.grzpList || [],
            fwzpList: villagerData.fwzpList || [],
            qtzjzpList: villagerData.qtzjzpList || [],
            gczpList: villagerData.gczpList || [],
            allImages: villagerData.allImages || [],
            // 反馈信息
            fankui: villagerData.fankui || [],
            photoCount: villagerData.photoCount || 0
          };
          
          // 判断是否为销户状态
          if (processedData.hujishuxing && typeof processedData.hujishuxing === 'string') {
            processedData.isXiaohu = processedData.hujishuxing.indexOf('销户') >= 0 || 
                                      processedData.hujishuxing.indexOf('已销户') >= 0;
          } else {
            processedData.isXiaohu = false;
          }
          
          // 是否有图片
          const hasImages = processedData.allImages.length > 0;
          
          this.setData({
            villager: processedData,
            loading: false,
            hasImages: hasImages
          });

          this.setDefaultPhotoTab(processedData);
          resolve(processedData);
        } else {
          this.setData({
            loading: false,
            loadError: true,
            errorMsg: res.data.msg || '获取村民信息失败'
          });
          reject(new Error(res.data.msg || '获取村民信息失败'));
        }
      },
      fail: (err) => {
        console.log('请求失败:', err);
        this.setData({
          loading: false,
          loadError: true,
          errorMsg: '网络错误，请重试'
        });
        reject(err);
      },
      complete: () => {
        wx.hideLoading();
      }
    });
    });
  },
  
  // 获取字段名称
  getFieldName: function(key) {
    const fieldNames = {
      // 根据实际情况添加字段名称映射
      xingbie: '性别',
      birthday: '出生日期',
      xianjudi: '现居地',
      jtgx: '家庭关系',
      huhao: '户号',
      fwxtbh: '房屋系统编号',
      bdcdjh: '不动产登记号',
      yktzh: '一卡通账号',
      ylbxkyx: '医疗保险卡银行',
      ylbxkzh: '医疗保险卡账号'
    };
    
    return fieldNames[key] || key;
  },
  

  
  // 编辑村民信息
  editVillager: function() {
    if (!this.villagerId) return;
    
    // 获取验证信息
    var member_uid = wx.getStorageSync('member_uid');
    var member_auth = wx.getStorageSync('member_auth');
    
    if (!member_uid || !member_auth) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }
    
    // 跳转到本地编辑页面
    wx.navigateTo({
      url: './cmdaedit?id=' + this.villagerId
    });
  },
  
  // 返回列表
  goBack: function() {
    wx.navigateBack();
  },
  
  // 打开反馈链接
  openFeedback: function(e) {
    const url = e.currentTarget.dataset.url;
    if (!url) return;
    
    wx.navigateTo({
      url: '../common/webview?url=' + encodeURIComponent(url)
    });
  },

  // 查看详情
  viewDetails: function() {
    if (!this.villagerId) return;

    wx.navigateTo({
      url: '/pages/cmda/cmdashow?id=' + this.villagerId
    });
  },

  // 跳转到图片页面
  goToPhotoPage: function(e) {
    if (!this.villagerId) return;

    const type = e.currentTarget.dataset.type;
    let scrollTarget = '';

    // 根据图片类型设置对应的滚动目标
    switch(type) {
      case 'grzp':
        scrollTarget = 'identity'; // 身份证照片
        break;
      case 'qtzjzp':
        scrollTarget = 'household'; // 户口簿照片
        break;
      case 'fwzp':
        scrollTarget = 'house'; // 房屋照片
        break;
      case 'gczp':
        scrollTarget = 'toilet'; // 改厕照片
        break;
      case 'thumb':
        scrollTarget = 'avatar'; // 个人照片
        break;
      default:
        scrollTarget = '';
    }

    wx.navigateTo({
      url: '/pages/cmda/cmdapic?id=' + this.villagerId + '&scrollTo=' + scrollTarget
    });
  },

  // 下拉刷新
  onPullDownRefresh: function() {
    this.loadDetail().then(() => {
      wx.stopPullDownRefresh();
    }).catch(() => {
      wx.stopPullDownRefresh();
    });
  },
  
  // 分享
  onShareAppMessage: function() {
    if (this.data.villager) {
      return {
        title: this.data.villager.title + '的村民档案',
        path: '/pages/cmda/cmdashow?id=' + this.villagerId
      };
    }
    return {
      title: '村民档案详情',
      path: '/pages/cmda/index'
    };
  },
  
  // WxParse的图片点击预览
  wxParseImgTap: function(e) {
    const src = e.currentTarget.dataset.src;
    if (!src) return;
    
    // 收集当前评论中的所有图片URL
    const commentList = this.data.commentList;
    const allImgUrls = [];
    
    // 遍历所有评论，查找所有图片
    for (let i = 0; i < commentList.length; i++) {
      const comment = commentList[i];
      if (comment.parsedContent && comment.parsedContent.nodes) {
        this.findImgUrls(comment.parsedContent.nodes, allImgUrls);
      }
    }
    
    // 如果没有找到图片，至少显示当前图片
    const imageUrls = allImgUrls.length > 0 ? allImgUrls : [src];
    
    wx.previewImage({
      current: src,
      urls: imageUrls
    });
  },
  
  // 递归查找所有图片URL
  findImgUrls: function(nodes, result) {
    if (!nodes) return;
    
    for (let i = 0; i < nodes.length; i++) {
      const node = nodes[i];
      if (node.tag === 'img' && node.attr && node.attr.src) {
        result.push(node.attr.src);
      }
      
      if (node.nodes && node.nodes.length > 0) {
        this.findImgUrls(node.nodes, result);
      }
    }
  },
  

  

  
  // 处理村民详情数据后，设置默认选中的照片标签页
  setDefaultPhotoTab: function(villagerData) {
    let defaultTab = '';
    
    // 按照顺序检查哪个类型的照片存在，优先选择有照片的类型
    if (villagerData.grzpList && villagerData.grzpList.length > 0) {
      defaultTab = 'grzp';
    } else if (villagerData.fwzpList && villagerData.fwzpList.length > 0) {
      defaultTab = 'fwzp';
    } else if (villagerData.qtzjzpList && villagerData.qtzjzpList.length > 0) {
      defaultTab = 'qtzjzp';
    } else if (villagerData.gczpList && villagerData.gczpList.length > 0) {
      defaultTab = 'gczp';
    }
    
    // 设置默认选中的标签
    this.setData({
      activePhotoTab: defaultTab
    });
  },
  
  // 新增方法，用于检查照片是否为空
  checkEmptyPhotos: function() {
    // 获取当前标签页和数据
    const tab = this.data.activePhotoTab;
    const villager = this.data.villager;

    if (!tab || !villager) return true;

    // 根据标签页类型检查相应的照片列表
    switch(tab) {
      case 'grzp':
        return !villager.grzpList || villager.grzpList.length === 0;
      case 'fwzp':
        return !villager.fwzpList || villager.fwzpList.length === 0;
      case 'qtzjzp':
        return !villager.qtzjzpList || villager.qtzjzpList.length === 0;
      case 'gczp':
        return !villager.gczpList || villager.gczpList.length === 0;
      default:
        return true;
    }
  },

  /**
   * 更新图片功能
   */
  updateImages: function() {
    console.log('updateImages 方法被调用');
    console.log('villager数据:', this.data.villager);
    console.log('villagerId:', this.villagerId);

    if (!this.data.villager || !this.villagerId) {
      console.log('村民信息检查失败');
      wx.showToast({
        title: '村民信息不完整',
        icon: 'none'
      });
      return;
    }

    // 检查权限
    const member = wx.getStorageSync('member');
    console.log('用户权限信息:', member);

    if (!member || !member.is_admin || member.is_admin <= 0) {
      console.log('权限检查失败');
      wx.showModal({
        title: '权限不足',
        content: '只有管理员可以更新村民图片',
        showCancel: false
      });
      return;
    }

    // 跳转到图片更新页面
    const targetUrl = './cmdapic?id=' + this.villagerId;
    console.log('准备跳转到:', targetUrl);

    wx.navigateTo({
      url: targetUrl,
      success: function() {
        console.log('页面跳转成功');
      },
      fail: function(err) {
        console.error('页面跳转失败:', err);
        wx.showToast({
          title: '页面跳转失败',
          icon: 'none'
        });
      }
    });
  },

  /**
   * 更新信息功能
   */
  updateInfo: function() {
    if (!this.data.villager || !this.villagerId) {
      wx.showToast({
        title: '村民信息不完整',
        icon: 'none'
      });
      return;
    }

    // 检查权限
    const member = wx.getStorageSync('member');
    if (!member || !member.is_admin || member.is_admin <= 0) {
      wx.showModal({
        title: '权限不足',
        content: '只有管理员可以更新村民信息',
        showCancel: false
      });
      return;
    }

    // 跳转到编辑页面
    wx.navigateTo({
      url: './cmdaedit?id=' + this.villagerId
    });
  },

  /**
   * 新增档案功能
   */
  addNewArchive: function() {
    // 检查权限
    const member = wx.getStorageSync('member');
    if (!member || !member.is_admin || member.is_admin <= 0) {
      wx.showModal({
        title: '权限不足',
        content: '只有管理员可以新增村民档案',
        showCancel: false
      });
      return;
    }

    // 跳转到新增档案页面
    wx.navigateTo({
      url: './cmdaedit'
    });
  },

  /**
   * 新增备注功能
   */
  addNewNote: function() {
    if (!this.data.villager || !this.villagerId) {
      wx.showToast({
        title: '村民信息不完整',
        icon: 'none'
      });
      return;
    }

    // 检查权限
    const member = wx.getStorageSync('member');
    if (!member) {
      wx.showModal({
        title: '请先登录',
        content: '需要登录后才能添加备注',
        showCancel: false,
        success: () => {
          wx.navigateTo({
            url: '/pages/login/login'
          });
        }
      });
      return;
    }

    // 显示评论输入框
    this.showFullCommentBar();
  }
});