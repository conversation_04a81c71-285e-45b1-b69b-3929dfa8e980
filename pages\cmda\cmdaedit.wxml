<!-- 档案编辑页面 -->
<view class="container">
  <!-- 顶部状态栏 -->
  <view class="status-bar">
    <view class="status-indicator {{loading ? 'loading' : 'success'}}">
      <view class="status-dot"></view>
      <text>{{loading ? '加载中' : '编辑档案'}}</text>
    </view>
  </view>

  <!-- 加载中提示 -->
  <view class="loading-container" wx:if="{{loading}}">
    <view class="loading-spinner"></view>
    <text class="loading-text">正在加载档案信息...</text>
  </view>

  <!-- 编辑表单 -->
  <scroll-view scroll-y class="edit-form" wx:if="{{!loading}}">
    <!-- 基本信息区 -->
    <view class="form-section">
      <view class="section-header">
        <view class="section-title-container">
          <view class="section-icon basic-icon">
            <text class="iconfont icon-user"></text>
          </view>
          <text class="section-title">基本信息</text>
        </view>
      </view>
      
      <view class="form-group">
        <view class="form-item">
          <text class="form-label required">姓名</text>
          <text class="form-value">{{villager.title || ''}}</text>
        </view>
        
        <view class="form-item">
          <text class="form-label required">联系电话</text>
          <input class="form-input" value="{{villager.shoujihaoma}}" bindinput="inputHandler" data-field="shoujihaoma" placeholder="请输入联系电话" type="number" maxlength="11"/>
        </view>
        
        <view class="form-item">
          <text class="form-label">一卡通账号</text>
          <input class="form-input" value="{{villager.yktzh}}" bindinput="inputHandler" data-field="yktzh" placeholder="请输入一卡通账号"/>
        </view>
        
        <view class="form-item">
          <text class="form-label">养老保险卡银行</text>
          <input class="form-input" value="{{villager.ylbxkyx}}" bindinput="inputHandler" data-field="ylbxkyx" placeholder="请输入养老保险卡银行"/>
        </view>
        
        <view class="form-item">
          <text class="form-label">养老保险卡账号</text>
          <input class="form-input" value="{{villager.ylbxkzh}}" bindinput="inputHandler" data-field="ylbxkzh" placeholder="请输入养老保险卡账号"/>
        </view>
        
        <view class="form-item">
          <text class="form-label">不动产登记号</text>
          <input class="form-input" value="{{villager.bdcdjh}}" bindinput="inputHandler" data-field="bdcdjh" placeholder="请输入不动产登记号"/>
        </view>
        
        <view class="form-item checkbox-item">
          <text class="form-label required">户籍属性</text>
          <view class="checkbox-group-container">
            <view class="selected-tips" wx:if="{{selectedHujishuxing.length > 0}}">
              <view class="selected-title">已选户籍属性:</view>
              <view class="selected-content">
                <text wx:for="{{selectedHujishuxing}}" wx:key="index" class="selected-tag">{{item}}</text>
              </view>
            </view>
            <view class="checkbox-group">
              <view wx:for="{{hujishuxingOptions}}" wx:key="index"
                class="checkbox-item {{selectedHujishuxing.includes(item) ? 'checkbox-selected' : ''}}"
                data-value="{{item}}" bindtap="toggleCheckbox">
                <view class="checkbox-icon">
                  <view wx:if="{{!selectedHujishuxing.includes(item)}}" class="unchecked"></view>
                  <view wx:else class="checked"></view>
                </view>
                <text>{{item}}</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

  </scroll-view>
  
  <!-- 底部操作栏 -->
  <view class="action-bar">
    <button class="action-button cancel-button" hover-class="button-hover" bindtap="cancelEdit">
      <text class="button-text">取消</text>
    </button>
    <button class="action-button save-button" hover-class="button-hover" bindtap="saveVillager">
      <text class="button-text">保存</text>
    </button>
  </view>
</view> 