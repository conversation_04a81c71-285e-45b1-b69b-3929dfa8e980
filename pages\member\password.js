var app = getApp();
Page({

  /**
   * 页面的初始数据
   */
  data: {
    member: wx.getStorageSync('member'),
    pwd1Hidden: true,
    pwd2Hidden: true,
    pwd3Hidden: true
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    if (wx.getStorageSync('member') == "") {
      // 未登录跳转登录界面
      wx.reLaunch({ url: "../login/login" });
    }
  },

  /**
   * 切换密码可见性
   */
  togglePwdVisible: function (e) {
    const field = e.currentTarget.dataset.field;
    
    if (field === 'pwd1') {
      this.setData({
        pwd1Hidden: !this.data.pwd1Hidden
      });
    } else if (field === 'pwd2') {
      this.setData({
        pwd2Hidden: !this.data.pwd2Hidden
      });
    } else if (field === 'pwd3') {
      this.setData({
        pwd3Hidden: !this.data.pwd3Hidden
      });
    }
  },

  /**
   * 表单提交
   */
  formBindsubmit: function (e) {
      app.showModel();

    var self = this; 
    var postParams = "is_ajax=1&"
      + "&data[password]=" + e.detail.value.password1
      + "&data[password2]=" + e.detail.value.password2 
      + "&data[password3]=" + e.detail.value.password3;
    wx.request({//提交
      url: app.globalData.http_api + "s=member&c=account&m=password&api_auth_code=" + wx.getStorageSync('member_auth') + "&api_auth_uid=" +wx.getStorageSync('member_uid'),
      data: postParams,
      method: 'post',
      header: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      success: function (res) {
        console.log(res.data);
        wx.hideLoading();
        if (res.data.code == 1) {
          // 修改成功，重新登录
          wx.setStorageSync('member', "");
            wx.showModal({
                showCancel: false,
                success: function(res) {
                    if (res.confirm) {
                        wx.navigateTo({ url: "../login/login" });
                    }
                },
                content: res.data.msg
            })
        }
        else {
            wx.showModal({
                showCancel: false,
                content: res.data.msg
            })
        }
      }
    })

  },

  
})